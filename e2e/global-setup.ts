import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
	console.log('🚀 Starting E2E test setup...');

	// Launch browser for setup
	const browser = await chromium.launch();
	const page = await browser.newPage();

	try {
		// Wait for the development server to be ready
		const baseURL = config.projects[0].use.baseURL || 'http://localhost:3000';
		console.log(`⏳ Waiting for server at ${baseURL}...`);

		// Try to connect to the server
		let retries = 30;
		while (retries > 0) {
			try {
				const response = await page.goto(`${baseURL}/api/auth/status`);
				if (response?.status() === 401) {
					// 401 means server is running but not authenticated
					console.log('✅ Server is ready!');
					break;
				}
			} catch (error) {
				console.log(`⏳ Server not ready, retrying... (${retries} attempts left)`);
				await new Promise((resolve) => setTimeout(resolve, 2000));
				retries--;
			}
		}

		if (retries === 0) {
			throw new Error('❌ Server failed to start within timeout period');
		}

		// Setup test data if needed
		console.log('📝 Setting up test data...');

		// You can add any global test data setup here
		// For example, creating test users, collections, etc.

		console.log('✅ E2E test setup completed successfully!');
	} catch (error) {
		console.error('❌ E2E test setup failed:', error);
		throw error;
	} finally {
		await browser.close();
	}
}

export default globalSetup;
