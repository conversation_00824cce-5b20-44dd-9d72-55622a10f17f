import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
	console.log('🧹 Starting E2E test teardown...');

	try {
		// Cleanup test data if needed
		console.log('📝 Cleaning up test data...');

		// You can add any global test data cleanup here
		// For example, removing test users, collections, etc.

		console.log('✅ E2E test teardown completed successfully!');
	} catch (error) {
		console.error('❌ E2E test teardown failed:', error);
		// Don't throw error in teardown to avoid masking test failures
	}
}

export default globalTeardown;
