'use client';

import { AnswerEvaluationResult } from '@/backend/services';
import {
	<PERSON><PERSON>,
	Label,
	LoadingSpinner,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Textarea,
	Translate,
} from '@/components/ui';
import { useKeywordsContext, useLLM, useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { useCollections } from '@/hooks';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import { KeywordWithDetail } from '@/models';
import { Collection, Difficulty, Keyword, Language } from '@prisma/client';
import { AlertCircle, Play, Settings } from 'lucide-react';
import React, { useCallback, useState } from 'react';
import { KeywordForm } from '../../components/keyword-form';
import { GenerationLoadingState, ParagraphPracticeSkeleton } from '../components';

const QUESTION_COUNT_FOR_QA = 3;

export interface UseQAPracticeLogicResult {
	// Keyword related
	keywords: Keyword[];
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	handleCreateKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	handleDeleteKeyword: (id: string) => Promise<void>;
	getKeywordNameFromId: (keywordId: string) => string;

	// Difficulty related
	difficulty: Difficulty;
	setDifficulty: (d: Difficulty) => void;

	// Q&A Practice related
	paragraphForQA: string | null;
	generatedQuestions: string[];
	userAnswers: Record<number, string>;
	qaEvaluationResults: Record<number, AnswerEvaluationResult | null | undefined>; // undefined for loading
	qaLoading: {
		generating: boolean; // For paragraph & questions
		evaluating: boolean; // For all answers
	};
	qaError: Error | null;
	handleGenerateParagraphAndQuestionsForQA: () => Promise<void>;
	handleUserAnswerChange: (index: number, value: string) => void;
	handleEvaluateUserAnswers: () => Promise<void>;
}

export function useQAPracticeLogic(): UseQAPracticeLogicResult {
	const { t } = useTranslation();
	const { currentCollection } = useCollections();
	const { keywords, selectedKeywords, setSelectedKeywords, createKeyword, deleteKeyword } =
		useKeywordsContext();
	const [difficulty, setDifficulty] = useState<Difficulty>('BEGINNER');
	const {
		generateParagraphWithQuestions: llmGenerateParagraphWithQuestions,
		evaluateAnswers: llmEvaluateAnswers,
	} = useLLM();

	const [paragraphForQA, setParagraphForQA] = useState<string | null>(null);
	const [generatedQuestions, setGeneratedQuestions] = useState<string[]>([]);
	const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
	const [qaEvaluationResults, setQaEvaluationResults] = useState<
		Record<number, AnswerEvaluationResult | null | undefined>
	>({});
	const [qaLoading, setQaLoading] = useState({
		generating: false,
		evaluating: false,
	});
	const [qaError, setQaError] = useState<Error | null>(null);

	const handleGenerateParagraphAndQuestionsForQA = useCallback(async () => {
		if (!currentCollection) {
			console.error('Collection not loaded');
			return;
		}
		if (selectedKeywords.length === 0) {
			console.error('No keywords selected');
			return;
		}
		setQaLoading((prev) => ({ ...prev, generating: true }));
		setQaError(null);
		setParagraphForQA(null);
		setGeneratedQuestions([]);
		setUserAnswers({});
		setQaEvaluationResults({});

		try {
			const keywordNames = selectedKeywords
				.map((id) => keywords.find((k) => k.id === id)?.content || '')
				.filter(Boolean);

			// Use the new combined method to generate both paragraph and questions in one API call
			const result = await llmGenerateParagraphWithQuestions({
				keywords: keywordNames,
				language: currentCollection.target_language,
				difficulty,
				sentenceCount: 20, // Example: request a paragraph of certain length
				questionCount: QUESTION_COUNT_FOR_QA,
			});

			if (
				!result ||
				!result.paragraph ||
				!result.questions ||
				result.questions.length === 0
			) {
				throw new Error('Paragraph and questions generation failed - empty result');
			}

			setParagraphForQA(result.paragraph);
			setGeneratedQuestions(result.questions);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setQaError(err);
			setParagraphForQA(null);
			console.error('Generation failed:', err.message);
		} finally {
			setQaLoading((prev) => ({ ...prev, generating: false }));
		}
	}, [
		currentCollection,
		selectedKeywords,
		keywords,
		difficulty,
		llmGenerateParagraphWithQuestions,
	]);

	const handleUserAnswerChange = useCallback((index: number, value: string) => {
		setUserAnswers((prev) => ({ ...prev, [index]: value }));
	}, []);

	const handleEvaluateUserAnswers = useCallback(async () => {
		if (!currentCollection) {
			console.error('Collection not loaded');
			return;
		}
		if (!paragraphForQA || generatedQuestions.length === 0) {
			console.error('No paragraph or questions available');
			return;
		}
		const answersArray = generatedQuestions.map((_, index) => userAnswers[index] || '');
		if (answersArray.some((answer) => !answer.trim())) {
			console.error('All answers are required');
			return;
		}

		setQaLoading((prev) => ({ ...prev, evaluating: true }));
		setQaError(null);
		setQaEvaluationResults((prev) => {
			// Mark all as loading
			const newResults: Record<number, undefined> = {};
			generatedQuestions.forEach((_, idx) => (newResults[idx] = undefined));
			return newResults;
		});

		try {
			const results = await llmEvaluateAnswers({
				paragraph: paragraphForQA,
				questions: generatedQuestions,
				answers: answersArray,
				qna_language: currentCollection.target_language,
				feedback_native_language: currentCollection.source_language,
			});
			const resultsMap: Record<number, AnswerEvaluationResult | null> = {};
			results.forEach((result, index) => {
				resultsMap[index] = result;
			});
			setQaEvaluationResults(resultsMap);

			// Track QA practice submission
			try {
				await fetch(`/api/collections/${currentCollection.id}/stats/track`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						type: 'qa_practice',
						count: 1,
					}),
				});
			} catch (trackError) {
				console.error('Failed to track QA practice submission:', trackError);
				// Don't throw error as this is not critical for user experience
			}
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setQaError(err);
			setQaEvaluationResults((prev) => {
				// Mark all as error on general failure
				const newResults: Record<number, null> = {};
				generatedQuestions.forEach((_, idx) => (newResults[idx] = null));
				return newResults;
			});
			console.error('Evaluation failed:', err.message);
		} finally {
			setQaLoading((prev) => ({ ...prev, evaluating: false }));
		}
	}, [paragraphForQA, generatedQuestions, userAnswers, currentCollection, llmEvaluateAnswers]);

	// Loading state is handled by shared logic

	const getKeywordNameFromId = useCallback(
		(keywordId: string) => {
			return keywords.find((k) => k.id === keywordId)?.content || '';
		},
		[keywords]
	);

	const handleCreateKeyword = useCallback(
		async (name: string) => {
			return await createKeyword(name);
		},
		[createKeyword]
	);

	const handleDeleteKeyword = useCallback(
		async (id: string) => {
			await deleteKeyword(id);
		},
		[deleteKeyword]
	);

	return {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		handleCreateKeyword,
		handleDeleteKeyword,
		getKeywordNameFromId,
		difficulty,
		setDifficulty,
		paragraphForQA,
		generatedQuestions,
		userAnswers,
		qaEvaluationResults,
		qaLoading,
		qaError,
		handleGenerateParagraphAndQuestionsForQA,
		handleUserAnswerChange,
		handleEvaluateUserAnswers,
	};
}

// Smaller components

function QAGeneratedParagraph({
	paragraph,
	targetLanguage,
}: {
	paragraph: string;
	targetLanguage: Language;
}) {
	return (
		<div className="space-y-4 mt-6 p-4 border rounded-lg shadow-sm bg-card dark:bg-zinc-900">
			<h3 className="text-lg font-semibold">
				<Translate text="qa_practice.generated_paragraph_title" />
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
				</span>
			</h3>
			<p className="whitespace-pre-wrap text-sm p-3 rounded-md bg-muted dark:bg-zinc-800">
				{paragraph}
			</p>
		</div>
	);
}

function QAQuestionFeedback({
	result,
	index,
	collection,
}: {
	result: AnswerEvaluationResult | null | undefined;
	index: number;
	collection: { target_language: Language; source_language: Language };
}) {
	const { t } = useTranslation();

	if (result === null) {
		return (
			<div className="mt-2 p-2 bg-red-100 border-l-4 border-red-500 rounded text-sm text-red-700 dark:bg-red-900/50 dark:border-red-700 dark:text-red-200">
				<Translate text="qa_practice.errors.evaluation_failed_title" />.
			</div>
		);
	}

	if (!result) return null;

	return (
		<div
			className={`mt-2 p-3 rounded-md border-l-4 text-sm
						${
							result?.score !== undefined && result?.score !== null
								? result?.score >= 4
									? 'border-green-500 bg-green-50 text-green-700 dark:border-green-700 dark:bg-green-900/30 dark:text-green-300'
									: result?.score >= 2
									? 'border-yellow-500 bg-yellow-50 text-yellow-700 dark:border-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
									: 'border-red-500 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/30 dark:text-red-300'
								: 'border-gray-500 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-800/30 dark:text-gray-300'
						}
						`}
		>
			<p className="font-semibold">
				<Translate text="qa_practice.feedback_title" />
				{result?.score !== undefined && result?.score !== null && (
					<span className="ml-2">
						(<Translate text="qa_practice.score_label" />: {result.score}/5)
					</span>
				)}
				{result?.is_correct !== undefined && (
					<span
						className={`ml-2 font-normal px-1.5 py-0.5 rounded text-xs ${
							result.is_correct
								? 'bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-200'
								: 'bg-red-100 text-red-800 dark:bg-red-800/50 dark:text-red-200'
						}`}
					>
						{result.is_correct ? t('qa_practice.correct') : t('qa_practice.incorrect')}
					</span>
				)}
			</p>
			<div className="mt-1 space-y-2">
				{result?.feedback?.qna_feedback_text && (
					<div>
						<strong className="text-sm">
							<Translate
								text={getTranslationKeyOfLanguage(collection.target_language)}
							/>
							:
						</strong>
						<p className="whitespace-pre-wrap text-sm">
							{result.feedback.qna_feedback_text}
						</p>
					</div>
				)}
				{result?.feedback?.native_feedback_text && (
					<div>
						<strong className="text-sm">
							<Translate
								text={getTranslationKeyOfLanguage(collection.source_language)}
							/>
							:
						</strong>
						<p className="whitespace-pre-wrap text-sm">
							{result.feedback.native_feedback_text}
						</p>
					</div>
				)}
			</div>
			{result?.suggested_answer && (
				<div className="mt-2 pt-2 border-t border-border/50">
					<p className="font-medium">
						<Translate text="qa_practice.suggested_answer_label" />:
					</p>
					<p className="whitespace-pre-wrap">{result.suggested_answer}</p>
				</div>
			)}
		</div>
	);
}

function QAQuestionItem({
	question,
	answer,
	onAnswerChange,
	feedbackResult,
	disabled,
	index,
	collection,
}: {
	question: string;
	answer: string;
	onAnswerChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	feedbackResult: AnswerEvaluationResult | null | undefined;
	disabled: boolean;
	index: number;
	collection: Collection;
}) {
	const { t } = useTranslation();

	return (
		<div className="p-4 border rounded-lg shadow-sm bg-card dark:bg-zinc-900 space-y-3">
			<div className="flex items-start space-x-2">
				<div className="flex-shrink-0">
					<p className="font-semibold text-base">
						<Translate text="qa_practice.question_label" /> {index + 1}:
					</p>
				</div>
				<div className="flex-grow">
					<p className="font-normal text-base">{question}</p>
				</div>
			</div>
			<Label htmlFor={`answer-${index}`} className="sr-only">
				Answer for Question {index + 1}
			</Label>
			<Textarea
				id={`answer-${index}`}
				value={answer || ''}
				onChange={onAnswerChange}
				placeholder={t('qa_practice.answer_placeholder')}
				rows={3}
				className="w-full mt-1 p-3 text-base"
				disabled={disabled}
			/>
			<QAQuestionFeedback result={feedbackResult} index={index} collection={collection} />
		</div>
	);
}

function QAQuestionsSection({
	questions,
	userAnswers,
	onUserAnswerChange,
	qaEvaluationResults,
	collection,
	isOverallLoading,
	handleEvaluateUserAnswers,
	qaLoading,
}: {
	questions: string[];
	userAnswers: Record<number, string>;
	onUserAnswerChange: (index: number, value: string) => void;
	qaEvaluationResults: Record<number, AnswerEvaluationResult | null | undefined>;
	collection: Collection;
	isOverallLoading: boolean;
	handleEvaluateUserAnswers: () => void;
	qaLoading: { evaluating: boolean; generating: boolean };
}) {
	const allAnswersFilled = questions.every(
		(_, idx) => userAnswers[idx] && userAnswers[idx].trim() !== ''
	);

	return (
		<div className="space-y-6 mt-6">
			<h3 className="text-lg font-semibold">
				<Translate text="qa_practice.questions_title" />
			</h3>
			{questions.map((question, index) => (
				<QAQuestionItem
					key={index}
					question={question}
					answer={userAnswers[index] || ''}
					onAnswerChange={(e) => onUserAnswerChange(index, e.target.value)}
					feedbackResult={qaEvaluationResults[index]}
					disabled={isOverallLoading}
					index={index}
					collection={collection}
				/>
			))}
			<Button
				onClick={handleEvaluateUserAnswers}
				disabled={isOverallLoading || questions.length === 0 || !allAnswersFilled}
				className="w-full sm:w-auto"
			>
				{qaLoading.evaluating ? (
					<LoadingSpinner size="sm" />
				) : (
					<Translate text="qa_practice.buttons.evaluate_answers" />
				)}
			</Button>
		</div>
	);
}

export function QAPracticeClient() {
	const { t } = useTranslation();
	const { currentCollection, loading } = useCollections();
	const logic = useQAPracticeLogic();

	// Page Guidance hook - must be called before any early returns
	usePageGuidance({
		titleKey: 'qa_practice.guidance.title',
		steps: [
			{ key: 'qa_practice.guidance.step1' },
			{ key: 'qa_practice.guidance.step2' },
			{ key: 'qa_practice.guidance.step3' },
			{ key: 'qa_practice.guidance.step4' },
			{ key: 'qa_practice.guidance.step5' },
		],
		tipKey: 'qa_practice.guidance.tip',
		requirementKey: 'qa_practice.guidance.requirement',
		defaultOpen: !currentCollection || logic.selectedKeywords.length === 0,
	});

	// Show skeleton while collection is loading or doesn't exist
	if (loading.get || loading.setCurrent || !currentCollection) {
		return <ParagraphPracticeSkeleton />;
	}

	const isOverallLoading = logic.qaLoading.generating || logic.qaLoading.evaluating;

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="qa_practice.title" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="qa_practice.description" />
					</p>
				</header>

				<div className="space-y-6">
					<div className="flex items-center gap-3 mb-6">
						<Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
						<h2 className="text-xl font-semibold text-primary dark:text-primary">
							Practice Configuration
						</h2>
					</div>

					<KeywordForm />

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-3">
							<Label className="text-sm font-semibold text-primary dark:text-primary">
								<Translate text="difficulty.select_difficulty" />
							</Label>
							<Select
								value={logic.difficulty as string}
								onValueChange={(value) => logic.setDifficulty(value as Difficulty)}
								disabled={isOverallLoading}
							>
								<SelectTrigger className="w-full h-12 border-2 border-border dark:border-border rounded-lg">
									<SelectValue placeholder={t('difficulty.select_difficulty')} />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value={Difficulty.BEGINNER}>
										{t('difficulty.BEGINNER')}
									</SelectItem>
									<SelectItem value={Difficulty.INTERMEDIATE}>
										{t('difficulty.INTERMEDIATE')}
									</SelectItem>
									<SelectItem value={Difficulty.ADVANCED}>
										{t('difficulty.ADVANCED')}
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					{logic.selectedKeywords.length > 0 && (
						<div className="text-sm text-primary/80 font-medium">
							<Translate
								text="words.selected_count"
								values={{ count: logic.selectedKeywords.length }}
							/>
						</div>
					)}

					<Button
						onClick={logic.handleGenerateParagraphAndQuestionsForQA}
						disabled={isOverallLoading || logic.selectedKeywords.length === 0}
						size="lg"
						className="w-full sm:w-auto h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
					>
						{logic.qaLoading.generating ? (
							<>
								<LoadingSpinner size="sm" />
								<span className="ml-3">
									<Translate text="qa_practice.generating" />
								</span>
							</>
						) : (
							<>
								<Play className="w-5 h-5 mr-2" />
								<Translate text="qa_practice.buttons.generate_paragraph_questions" />
							</>
						)}
					</Button>
				</div>

				{logic.qaError && !isOverallLoading && (
					<div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 border-2 border-red-200 dark:border-red-800 rounded-xl p-6 shadow-sm">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
								<AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
							</div>
							<div>
								<span className="text-red-700 dark:text-red-300 font-semibold text-lg">
									<Translate text="qa_practice.errors.generation_failed_title" />
								</span>
								<p className="text-red-600 dark:text-red-400 text-sm mt-1">
									{logic.qaError.message}
								</p>
							</div>
						</div>
					</div>
				)}

				{logic.qaLoading.generating && (
					<GenerationLoadingState
						titleKey="qa_practice.generating_content"
						description="Creating personalized Q&A exercises..."
					/>
				)}

				{logic.paragraphForQA && (
					<QAGeneratedParagraph
						paragraph={logic.paragraphForQA}
						targetLanguage={currentCollection.target_language}
					/>
				)}

				{logic.generatedQuestions.length > 0 && (
					<QAQuestionsSection
						questions={logic.generatedQuestions}
						userAnswers={logic.userAnswers}
						onUserAnswerChange={logic.handleUserAnswerChange}
						qaEvaluationResults={logic.qaEvaluationResults}
						collection={currentCollection}
						isOverallLoading={isOverallLoading}
						handleEvaluateUserAnswers={logic.handleEvaluateUserAnswers}
						qaLoading={logic.qaLoading}
					/>
				)}
			</div>
		</div>
	);
}
