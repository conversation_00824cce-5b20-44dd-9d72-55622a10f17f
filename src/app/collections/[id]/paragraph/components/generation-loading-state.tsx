'use client';

import { LoadingSpinner, Translate } from '@/components/ui';

interface GenerationLoadingStateProps {
	titleKey: string;
	description: string;
	className?: string;
}

export function GenerationLoadingState({ 
	titleKey, 
	description, 
	className = '' 
}: GenerationLoadingStateProps) {
	return (
		<section className={`bg-white dark:bg-background/50 border-2 border-border dark:border-border rounded-2xl p-12 shadow-lg ${className}`}>
			<div className="flex flex-col items-center text-center space-y-6">
				<div className="relative">
					<LoadingSpinner size="lg" />
					<div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full animate-ping opacity-20"></div>
				</div>
				<div className="space-y-2">
					<h3 className="text-xl font-semibold text-primary dark:text-primary">
						<Translate text={titleKey} />
					</h3>
					<p className="text-muted-foreground dark:text-muted-foreground">
						{description}
					</p>
				</div>
			</div>
		</section>
	);
}
