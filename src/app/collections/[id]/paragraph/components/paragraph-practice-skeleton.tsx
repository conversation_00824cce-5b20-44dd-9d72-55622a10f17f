'use client';

interface ParagraphPracticeSkeletonProps {
	className?: string;
}

export function ParagraphPracticeSkeleton({ className = '' }: ParagraphPracticeSkeletonProps) {
	return (
		<div className={`min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background p-6 ${className}`}>
			<div className="max-w-6xl mx-auto space-y-8">
				<header className="text-center">
					<div className="h-10 w-80 bg-muted rounded-xl animate-pulse mb-4 mx-auto" />
					<div className="h-6 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
				</header>
				<div className="space-y-6">
					<div className="h-40 w-full bg-muted rounded-xl animate-pulse" />
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
						<div className="h-24 bg-muted rounded-xl animate-pulse" />
						<div className="h-24 bg-muted rounded-xl animate-pulse" />
					</div>
					<div className="h-12 w-40 bg-muted rounded-xl animate-pulse" />
				</div>
			</div>
		</div>
	);
}
