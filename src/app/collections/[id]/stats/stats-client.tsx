'use client';

import { DailyStats } from '@/backend/services/collection-stats.service';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	StatsSkeleton,
	Translate,
	useTheme,
} from '@/components/ui';
import { useCollections } from '@/hooks';
import {
	Activity,
	BookOpen,
	Calendar,
	FileText,
	MessageSquare,
	Target,
	TrendingUp,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import {
	Bar,
	BarChart,
	CartesianGrid,
	Line,
	LineChart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from 'recharts';

interface StatsData {
	stats: DailyStats[];
	totalWordsReviewed: number;
	totalQAPractice: number;
	totalParagraphPractice: number;
	todayStats: DailyStats;
}

function StatsCard({
	title,
	value,
	icon: Icon,
	color,
	gradient,
	change,
}: {
	title: React.ReactNode;
	value: number;
	icon: any;
	color: string;
	gradient: string;
	change?: { value: number; isPositive: boolean };
}) {
	return (
		<Card className="relative overflow-hidden group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm">
			{/* Background gradient overlay */}
			<div
				className={`absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300 ${gradient}`}
			/>

			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
				<CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
				<div className={`p-2 rounded-lg ${gradient} bg-opacity-10 dark:bg-opacity-20`}>
					<Icon className={`h-4 w-4 ${color}`} />
				</div>
			</CardHeader>

			<CardContent className="relative z-10">
				<div className="flex items-end justify-between">
					<div className="text-3xl font-bold text-foreground">
						{value.toLocaleString()}
					</div>
					{change && (
						<div
							className={`flex items-center text-xs font-medium ${
								change.isPositive
									? 'text-green-600 dark:text-green-400'
									: 'text-red-600 dark:text-red-400'
							}`}
						>
							<TrendingUp
								className={`h-3 w-3 mr-1 ${!change.isPositive ? 'rotate-180' : ''}`}
							/>
							{Math.abs(change.value)}%
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}

function ChartCard({
	title,
	children,
	icon: Icon = TrendingUp,
	description,
}: {
	title: React.ReactNode;
	children: React.ReactNode;
	icon?: any;
	description?: React.ReactNode;
}) {
	return (
		<Card className="col-span-full border-0 bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
			<CardHeader className="pb-4">
				<div className="flex items-center justify-between">
					<div>
						<CardTitle className="flex items-center gap-3 text-lg">
							<div className="p-2 rounded-lg bg-primary/10 dark:bg-primary/20">
								<Icon className="h-5 w-5 text-primary" />
							</div>
							{title}
						</CardTitle>
						{description && (
							<p className="text-sm text-muted-foreground mt-1">{description}</p>
						)}
					</div>
					<Activity className="h-4 w-4 text-muted-foreground" />
				</div>
			</CardHeader>
			<CardContent>
				<div className="h-80 w-full">{children}</div>
			</CardContent>
		</Card>
	);
}

// Custom Tooltip Component
function CustomTooltip({ active, payload, label }: any) {
	if (active && payload && payload.length) {
		return (
			<div className="bg-card/95 dark:bg-card/90 backdrop-blur-sm border border-border/50 rounded-lg p-3 shadow-lg">
				<p className="text-sm font-medium text-foreground mb-2">{label}</p>
				{payload.map((entry: any, index: number) => (
					<div key={index} className="flex items-center gap-2 text-xs">
						<div
							className="w-3 h-3 rounded-full"
							style={{ backgroundColor: entry.color }}
						/>
						<span className="text-muted-foreground">{entry.dataKey}:</span>
						<span className="font-medium text-foreground">{entry.value}</span>
					</div>
				))}
			</div>
		);
	}
	return null;
}

export function StatsClient() {
	const { currentCollection } = useCollections();
	const { theme } = useTheme();
	const [statsData, setStatsData] = useState<StatsData | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (!currentCollection) return;

		const fetchStats = async () => {
			try {
				setLoading(true);
				setError(null);

				const response = await fetch(
					`/api/collections/${currentCollection.id}/stats?days=30`
				);
				if (!response.ok) {
					throw new Error('Failed to fetch stats');
				}

				const stats: DailyStats[] = await response.json();

				// Calculate totals
				const totalWordsReviewed = stats.reduce(
					(sum, day) => sum + day.words_reviewed_count,
					0
				);
				const totalQAPractice = stats.reduce(
					(sum, day) => sum + day.qa_practice_submissions,
					0
				);
				const totalParagraphPractice = stats.reduce(
					(sum, day) => sum + day.paragraph_practice_submissions,
					0
				);

				// Get today's stats (last item in array)
				const todayStats = stats[stats.length - 1] || {
					date: new Date().toISOString().split('T')[0],
					words_reviewed_count: 0,
					qa_practice_submissions: 0,
					paragraph_practice_submissions: 0,
				};

				setStatsData({
					stats,
					totalWordsReviewed,
					totalQAPractice,
					totalParagraphPractice,
					todayStats,
				});
			} catch (err) {
				setError(err instanceof Error ? err.message : 'Unknown error');
			} finally {
				setLoading(false);
			}
		};

		fetchStats();
	}, [currentCollection]);

	if (loading) {
		return <StatsSkeleton />;
	}

	if (error) {
		return (
			<div className="container mx-auto py-6">
				<Card>
					<CardContent className="pt-6">
						<div className="text-center text-red-500">
							<Translate text="collections.stats.error_loading" />: {error}
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (!statsData || !currentCollection) {
		return null;
	}

	// Prepare chart data
	const chartData = statsData.stats.map((day) => ({
		date: new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
		'Words Reviewed': day.words_reviewed_count,
		'QA Practice': day.qa_practice_submissions,
		'Paragraph Practice': day.paragraph_practice_submissions,
	}));

	// Theme-aware colors
	const isDark = theme === 'dark';
	const colors = {
		wordsReviewed: isDark ? 'hsl(217, 91%, 60%)' : 'hsl(217, 91%, 50%)',
		qaPractice: isDark ? 'hsl(142, 76%, 55%)' : 'hsl(142, 76%, 45%)',
		paragraphPractice: isDark ? 'hsl(262, 83%, 65%)' : 'hsl(262, 83%, 55%)',
		grid: isDark ? 'hsl(0, 0%, 20%)' : 'hsl(0, 0%, 90%)',
		text: isDark ? 'hsl(0, 0%, 80%)' : 'hsl(0, 0%, 40%)',
	};

	// Calculate percentage changes (mock data for demo)
	const calculateChange = (current: number, previous: number) => {
		if (previous === 0) return { value: 0, isPositive: true };
		const change = ((current - previous) / previous) * 100;
		return { value: Math.round(Math.abs(change)), isPositive: change >= 0 };
	};

	return (
		<div className="container mx-auto py-6 space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-8">
				<div className="flex items-center gap-4">
					<div className="p-3 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10">
						<Calendar className="h-7 w-7 text-primary" />
					</div>
					<div>
						<h1 className="text-3xl font-bold text-foreground">
							<Translate text="collections.stats.title" />
						</h1>
						<p className="text-muted-foreground mt-1">
							<Translate text="collections.stats.track_progress" />
						</p>
					</div>
				</div>
				<div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-primary/5 dark:bg-primary/10">
					<Target className="h-4 w-4 text-primary" />
					<span className="text-sm font-medium text-primary">
						<Translate text="collections.stats.last_30_days" />
					</span>
				</div>
			</div>

			{/* Today's Stats */}
			<div className="mb-8">
				<h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
					<div className="w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full" />
					<Translate text="collections.stats.today_activity" />
				</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<StatsCard
						title={<Translate text="collections.stats.words_reviewed" />}
						value={statsData.todayStats.words_reviewed_count}
						icon={BookOpen}
						color="text-blue-600 dark:text-blue-400"
						gradient="bg-gradient-to-br from-blue-500/10 to-blue-600/5"
						change={calculateChange(statsData.todayStats.words_reviewed_count, 15)}
					/>
					<StatsCard
						title={<Translate text="collections.stats.qa_practice" />}
						value={statsData.todayStats.qa_practice_submissions}
						icon={MessageSquare}
						color="text-green-600 dark:text-green-400"
						gradient="bg-gradient-to-br from-green-500/10 to-green-600/5"
						change={calculateChange(statsData.todayStats.qa_practice_submissions, 8)}
					/>
					<StatsCard
						title={<Translate text="collections.stats.paragraph_practice" />}
						value={statsData.todayStats.paragraph_practice_submissions}
						icon={FileText}
						color="text-purple-600 dark:text-purple-400"
						gradient="bg-gradient-to-br from-purple-500/10 to-purple-600/5"
						change={calculateChange(
							statsData.todayStats.paragraph_practice_submissions,
							5
						)}
					/>
				</div>
			</div>

			{/* Total Stats (Last 30 Days) */}
			<div className="mb-8">
				<h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
					<div className="w-1 h-6 bg-gradient-to-b from-secondary to-secondary/50 rounded-full" />
					<Translate text="collections.stats.30_day_summary" />
				</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<StatsCard
						title={<Translate text="collections.stats.total_words_reviewed" />}
						value={statsData.totalWordsReviewed}
						icon={BookOpen}
						color="text-blue-600 dark:text-blue-400"
						gradient="bg-gradient-to-br from-blue-500/10 to-blue-600/5"
					/>
					<StatsCard
						title={<Translate text="collections.stats.total_qa_practice" />}
						value={statsData.totalQAPractice}
						icon={MessageSquare}
						color="text-green-600 dark:text-green-400"
						gradient="bg-gradient-to-br from-green-500/10 to-green-600/5"
					/>
					<StatsCard
						title={<Translate text="collections.stats.total_paragraph_practice" />}
						value={statsData.totalParagraphPractice}
						icon={FileText}
						color="text-purple-600 dark:text-purple-400"
						gradient="bg-gradient-to-br from-purple-500/10 to-purple-600/5"
					/>
				</div>
			</div>

			{/* Charts */}
			<div className="grid grid-cols-1 gap-8">
				<ChartCard
					title={<Translate text="collections.stats.daily_activity_overview" />}
					description={<Translate text="collections.stats.daily_activity_description" />}
					icon={Activity}
				>
					<ResponsiveContainer width="100%" height="100%">
						<BarChart
							data={chartData}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
						>
							<CartesianGrid
								strokeDasharray="3 3"
								stroke={colors.grid}
								opacity={0.3}
							/>
							<XAxis
								dataKey="date"
								tick={{ fill: colors.text, fontSize: 12 }}
								axisLine={{ stroke: colors.grid }}
								tickLine={{ stroke: colors.grid }}
							/>
							<YAxis
								tick={{ fill: colors.text, fontSize: 12 }}
								axisLine={{ stroke: colors.grid }}
								tickLine={{ stroke: colors.grid }}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Bar
								dataKey="Words Reviewed"
								fill={colors.wordsReviewed}
								radius={[2, 2, 0, 0]}
							/>
							<Bar
								dataKey="QA Practice"
								fill={colors.qaPractice}
								radius={[2, 2, 0, 0]}
							/>
							<Bar
								dataKey="Paragraph Practice"
								fill={colors.paragraphPractice}
								radius={[2, 2, 0, 0]}
							/>
						</BarChart>
					</ResponsiveContainer>
				</ChartCard>

				<ChartCard
					title={<Translate text="collections.stats.progress_trends" />}
					description={<Translate text="collections.stats.progress_trends_description" />}
					icon={TrendingUp}
				>
					<ResponsiveContainer width="100%" height="100%">
						<LineChart
							data={chartData}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
						>
							<CartesianGrid
								strokeDasharray="3 3"
								stroke={colors.grid}
								opacity={0.3}
							/>
							<XAxis
								dataKey="date"
								tick={{ fill: colors.text, fontSize: 12 }}
								axisLine={{ stroke: colors.grid }}
								tickLine={{ stroke: colors.grid }}
							/>
							<YAxis
								tick={{ fill: colors.text, fontSize: 12 }}
								axisLine={{ stroke: colors.grid }}
								tickLine={{ stroke: colors.grid }}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Line
								type="monotone"
								dataKey="Words Reviewed"
								stroke={colors.wordsReviewed}
								strokeWidth={3}
								dot={{ fill: colors.wordsReviewed, strokeWidth: 2, r: 4 }}
								activeDot={{ r: 6, stroke: colors.wordsReviewed, strokeWidth: 2 }}
							/>
							<Line
								type="monotone"
								dataKey="QA Practice"
								stroke={colors.qaPractice}
								strokeWidth={3}
								dot={{ fill: colors.qaPractice, strokeWidth: 2, r: 4 }}
								activeDot={{ r: 6, stroke: colors.qaPractice, strokeWidth: 2 }}
							/>
							<Line
								type="monotone"
								dataKey="Paragraph Practice"
								stroke={colors.paragraphPractice}
								strokeWidth={3}
								dot={{ fill: colors.paragraphPractice, strokeWidth: 2, r: 4 }}
								activeDot={{
									r: 6,
									stroke: colors.paragraphPractice,
									strokeWidth: 2,
								}}
							/>
						</LineChart>
					</ResponsiveContainer>
				</ChartCard>
			</div>
		</div>
	);
}
