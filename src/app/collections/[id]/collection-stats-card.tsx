'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/lib';
import { CollectionWithDetail } from '@/models';
import { BookOpen, Globe, Hash } from 'lucide-react';

interface CollectionStatsCardProps {
	collection: CollectionWithDetail;
}

export function CollectionStatsCard({ collection }: CollectionStatsCardProps) {
	return (
		<div className="relative mb-4 sm:mb-6 lg:mb-8 overflow-hidden">
			{/* Background decoration */}
			<div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-secondary/5 dark:from-primary/10 dark:via-primary/20 dark:to-secondary/10 rounded-xl sm:rounded-2xl" />
			<div className="absolute top-0 right-0 w-20 h-20 sm:w-32 sm:h-32 bg-gradient-to-bl from-primary/20 to-transparent rounded-full blur-xl sm:blur-2xl" />
			<div className="absolute bottom-0 left-0 w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-tr from-secondary/20 to-transparent rounded-full blur-lg sm:blur-xl" />

			<Card className="relative border-primary/20 bg-background/80 backdrop-blur-sm shadow-lg">
				<CardHeader className="pb-3 sm:pb-4 px-4 sm:px-6 pt-4 sm:pt-6">
					<CardTitle className="text-xl sm:text-2xl lg:text-3xl font-bold flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
						<div className="p-2 rounded-xl bg-primary/10 border border-primary/20 flex-shrink-0">
							<BookOpen className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
						</div>
						<span className="break-words">
							<Translate
								text="collections.overview.welcome_title"
								values={{ name: collection.name }}
							/>
						</span>
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
						{/* <div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-blue-100/50 dark:from-blue-950/30 dark:to-blue-900/20 border border-blue-200/50 dark:border-blue-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
								<Globe className="h-5 w-5 text-blue-600 dark:text-blue-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">
									<Translate text="languages.source_language" />
								</div>
								<div className="text-sm font-semibold text-blue-900 dark:text-blue-100">
									<Translate
										text={getTranslationKeyOfLanguage(
											collection.source_language
										)}
									/>
								</div>
							</div>
						</div> */}

						<div className="group flex items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-lg sm:rounded-xl bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/30 dark:to-green-900/20 border border-green-200/50 dark:border-green-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-1.5 sm:p-2 rounded-lg bg-green-500/10 border border-green-500/20 flex-shrink-0">
								<Globe className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 dark:text-green-400" />
							</div>
							<div className="flex-1 min-w-0">
								<div className="text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">
									<Translate text="languages.target_language" />
								</div>
								<div className="text-sm font-semibold text-green-900 dark:text-green-100 truncate">
									<Translate
										text={getTranslationKeyOfLanguage(
											collection.target_language
										)}
									/>
								</div>
							</div>
						</div>

						<div className="group flex items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-lg sm:rounded-xl bg-gradient-to-r from-purple-50 to-purple-100/50 dark:from-purple-950/30 dark:to-purple-900/20 border border-purple-200/50 dark:border-purple-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-1.5 sm:p-2 rounded-lg bg-purple-500/10 border border-purple-500/20 flex-shrink-0">
								<Hash className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 dark:text-purple-400" />
							</div>
							<div className="flex-1 min-w-0">
								<div className="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">
									<Translate text="collections.overview.word_count" />
								</div>
								<div className="text-base sm:text-lg font-bold text-purple-900 dark:text-purple-100">
									{collection.word_ids.length.toLocaleString()}
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
