'use client';

import { But<PERSON>, <PERSON><PERSON>rDisplay, Loading<PERSON>pinner, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import { CollectionWithDetail } from '@/models';
import { ArrowLeft, Pencil } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { RenameCollectionDialog } from './rename-collection-dialog';

export function CollectionDetailLayoutClient({
	children,
	initialCollection,
}: {
	children: React.ReactNode;
	initialCollection: CollectionWithDetail | null;
}) {
	const {
		currentCollection,
		updateCurrentCollection,
		setCurrentCollection,
		getCollection,
		loading,
		error,
		setError,
	} = useCollections();
	const router = useRouter();
	const params = useParams();
	const { t } = useTranslation();
	const [isInitialized, setIsInitialized] = useState(false);

	useEffect(() => {
		const initCollection = async () => {
			if (initialCollection) {
				setCurrentCollection(initialCollection);
				setIsInitialized(true);
			} else {
				const collectionId = params.id as string;
				if (collectionId) {
					const collection = await getCollection(collectionId);
					if (collection) {
						setCurrentCollection(collection);
					}
				}
				setIsInitialized(true);
			}
		};

		initCollection();
	}, [initialCollection, params.id, getCollection, setCurrentCollection]);

	const [isEditingName, setIsEditingName] = useState(false);

	const renameCollection = useCallback(
		async ({ name }: { name: string }) => {
			await updateCurrentCollection({ name });
		},
		[currentCollection, updateCurrentCollection]
	);

	const handleStartRename = useCallback(() => {
		setIsEditingName(true);
	}, []);

	const handleBack = useCallback(() => {
		if (currentCollection) {
			const path = window.location.pathname;
			const expectedPath = `/collections/${currentCollection.id}`;
			if (path === expectedPath) {
				router.push('/collections');
				return;
			}
			router.push('/collections/' + currentCollection.id);
		} else {
			router.push('/collections');
		}
	}, [router, currentCollection]);

	if (!isInitialized || (!currentCollection && loading.get)) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (!currentCollection) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Collection not found</h1>
					<Button onClick={() => router.push('/collections')}>Back to Collections</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="w-full sm:py-8">
			<div className="flex items-center justify-between mb-6">
				<div className="flex items-center gap-2">
					<Button
						variant="ghost"
						size="sm"
						aria-label={t('accessibility.back_to_collections')}
						onClick={handleBack}
					>
						<ArrowLeft className="h-4 w-4" />
						<span className="hidden sm:inline">
							<Translate text="ui.back" />
						</span>
					</Button>
					<h1 className="text-2xl font-bold">{currentCollection!.name}</h1>
					<button
						type="button"
						className="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
						onClick={handleStartRename}
						disabled={loading.update}
						aria-label={t('accessibility.rename_collection')}
					>
						<Pencil className="h-4 w-4 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" />
					</button>
				</div>
			</div>

			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			<RenameCollectionDialog
				open={isEditingName}
				onOpenChange={setIsEditingName}
				collection={currentCollection}
				renameCollection={renameCollection}
				renameLoading={loading.update}
				renameError={error}
			/>

			<div className="mt-4">{children}</div>
		</div>
	);
}
