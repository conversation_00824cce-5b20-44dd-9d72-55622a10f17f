import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to search words.'
			);
		}

		const collectionId = params.id;
		if (!collectionId) {
			throw new ValidationError('Collection ID is required to search words.');
		}

		const { searchParams } = new URL(request.url);
		const term = searchParams.get('term');
		const language = searchParams.get('language') as Language;
		const limitParam = searchParams.get('limit');
		const limit = limitParam ? parseInt(limitParam, 10) : 10;

		if (!term || term.trim() === '') {
			throw new ValidationError('Search term is required and cannot be empty.');
		}

		if (term.trim().length > 100) {
			throw new ValidationError('Search term cannot exceed 100 characters.');
		}

		if (language && !Object.values(Language).includes(language)) {
			throw new ValidationError(`Invalid language: ${language}`);
		}

		if (isNaN(limit) || limit < 1 || limit > 50) {
			throw new ValidationError('Limit must be a number between 1 and 50.');
		}

		const wordService = getWordService();
		const words = await wordService.searchWordsInCollection(
			userId,
			collectionId,
			term.trim(),
			language,
			limit
		);

		return NextResponse.json(words as WordDetail[]);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Failed to search words in collection ${params.id}:`, error);
		return NextResponse.json(
			{ error: 'Failed to search words. Please try again.' },
			{ status: 500 }
		);
	}
}
