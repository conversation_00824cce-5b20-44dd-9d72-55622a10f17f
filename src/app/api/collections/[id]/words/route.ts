import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getCollectionService } from '@/backend/wire';
import { auth } from '@/lib';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { CollectionWithDetail } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');

		const collectionId = params.id;
		if (!collectionId) {
			throw new ValidationError('Collection ID is required');
		}

		const body = await request.json();
		const { wordId, wordIds, term, language } = body;

		const collectionService = getCollectionService();
		let collection: CollectionWithDetail | null;

		if (wordId) {
			// Add single word by ID
			if (!wordId) {
				throw new ValidationError('Word ID is required');
			}
			collection = await collectionService.addWordsToCollection(userId, collectionId, [
				wordId,
			]);
		} else if (wordIds && Array.isArray(wordIds)) {
			// Add multiple words by IDs
			if (wordIds.length === 0) {
				throw new ValidationError('At least one word ID is required');
			}
			collection = await collectionService.addWordsToCollection(
				userId,
				collectionId,
				wordIds
			);
		} else if (term && language) {
			// Add term with language
			if (!term) {
				throw new ValidationError('Term is required');
			}
			if (!language) {
				throw new ValidationError('Language is required');
			}
			if (!Object.values(Language).includes(language)) {
				throw new ValidationError(`Invalid language: ${language}`);
			}
			collection = await collectionService.addTermsToCollection(
				userId,
				collectionId,
				[term],
				language
			);
		} else {
			throw new ValidationError(
				'Either wordId, wordIds array, or term with language is required'
			);
		}

		if (!collection) {
			throw new NotFoundError('Collection', collectionId);
		}

		return NextResponse.json(collection);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		errorLogger.error(
			`Failed to add words to collection ${params.id}`,
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'add_words_to_collection', {
				collectionId: params.id,
			}),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to add words to collection. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');

		const collectionId = params.id;
		if (!collectionId) {
			throw new ValidationError('Collection ID is required');
		}

		const body = await request.json();
		const { wordIds } = body;

		if (!wordIds || !Array.isArray(wordIds) || wordIds.length === 0) {
			throw new ValidationError('Word IDs array is required and cannot be empty');
		}

		const collectionService = getCollectionService();
		const collection = await collectionService.removeWordsFromCollection(
			userId,
			collectionId,
			wordIds
		);

		return NextResponse.json(collection);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		errorLogger.error(
			`Failed to remove words from collection ${params.id}`,
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'remove_words_from_collection', {
				collectionId: params.id,
			}),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to remove words from collection. Please try again.' },
			{ status: 500 }
		);
	}
}
