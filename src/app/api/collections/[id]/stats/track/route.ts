import { getCollectionStatsService } from '@/backend/wire';
import { auth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: collectionId } = await params;
		const session = await auth();

		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { type, count = 1 } = body;

		if (!type || !['word_review', 'qa_practice', 'paragraph_practice'].includes(type)) {
			return NextResponse.json(
				{
					error: 'Invalid tracking type. Must be one of: word_review, qa_practice, paragraph_practice',
				},
				{ status: 400 }
			);
		}

		const collectionStatsService = getCollectionStatsService();

		switch (type) {
			case 'word_review':
				await collectionStatsService.trackWordReview(collectionId, session.user.id, count);
				break;
			case 'qa_practice':
				await collectionStatsService.trackQAPracticeSubmission(
					collectionId,
					session.user.id,
					count
				);
				break;
			case 'paragraph_practice':
				await collectionStatsService.trackParagraphPracticeSubmission(
					collectionId,
					session.user.id,
					count
				);
				break;
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error('Error tracking stats:', error);
		return NextResponse.json({ error: 'Failed to track stats' }, { status: 500 });
	}
}
