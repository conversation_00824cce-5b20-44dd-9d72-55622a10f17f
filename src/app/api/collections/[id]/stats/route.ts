import { getCollectionStatsService } from '@/backend/wire';
import { auth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: collectionId } = await params;
		const session = await auth();

		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const days = searchParams.get('days') ? parseInt(searchParams.get('days')!) : undefined;

		const collectionStatsService = getCollectionStatsService();
		const stats = await collectionStatsService.getStatsForCollection(
			collectionId,
			session.user.id,
			days
		);

		return NextResponse.json(stats);
	} catch (error) {
		console.error('Error fetching collection stats:', error);
		return NextResponse.json({ error: 'Failed to fetch collection stats' }, { status: 500 });
	}
}
