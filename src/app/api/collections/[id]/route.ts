import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getCollectionService } from '@/backend/wire';
import { auth } from '@/lib';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { CollectionWithDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: collectionId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');

		if (!collectionId) {
			throw new ValidationError('Collection ID is required');
		}

		const collectionService = getCollectionService();
		const collection = await collectionService.getCollectionById(userId, collectionId);

		return NextResponse.json(collection as CollectionWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		errorLogger.error(
			`Failed to get collection ${collectionId}`,
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'get_collection', { collectionId }),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to fetch collection. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: collectionId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');
		if (!collectionId) {
			throw new ValidationError('Collection ID is required');
		}

		const body = await request.json();
		const { name, wordIds } = body;

		const collectionService = getCollectionService();
		const collection = await collectionService.updateCollection(
			userId,
			collectionId,
			name,
			wordIds
		);

		return NextResponse.json(collection as CollectionWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		errorLogger.error(
			`Failed to update collection ${collectionId}`,
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'update_collection', { collectionId }),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to update collection. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	const { id: collectionId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');
		if (!collectionId) {
			throw new ValidationError('Collection ID is required');
		}

		const collectionService = getCollectionService();
		const result = await collectionService.deleteCollection(userId, collectionId);

		return NextResponse.json({ success: result });
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		errorLogger.error(
			`Failed to delete collection ${collectionId}`,
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'delete_collection', { collectionId }),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to delete collection. Please try again.' },
			{ status: 500 }
		);
	}
}
