import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Difficulty, Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { z, ZodError } from 'zod';

const generateParagraphSchema = z.object({
	keywords: z
		.array(z.string())
		.min(1, 'At least one keyword is required')
		.max(10, 'Cannot use more than 10 keywords'),
	language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	count: z
		.number()
		.min(1, 'Count must be at least 1')
		.max(5, 'Cannot generate more than 5 paragraphs at once'),
	sentenceCount: z.number().min(1).max(30).optional(),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('User not authenticated for generating paragraphs.');

		const body = await request.json();
		const validatedData = generateParagraphSchema.parse(body);
		const { keywords, language, difficulty, count, sentenceCount } = validatedData;

		// Create cache file path for development
		const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		const cacheKey = `paragraph-${keywords.join('-')}-${language}-${difficulty}-${count}-${
			sentenceCount || 'default'
		}`;
		const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// Try to load from cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				const cachedData = await fs.readFile(filePath, 'utf-8');
				const parsedData = JSON.parse(cachedData);
				return NextResponse.json(parsedData);
			} catch (err) {
				// Cache miss, continue with generation
			}
		}

		const llmService = await getLLMService();
		const paragraphs = await llmService.generateParagraph({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
		});

		// Save to cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(paragraphs, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save paragraph cache file ${filePath}:`,
					err.message
				);
			}
		}

		return NextResponse.json(paragraphs);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Error in generateParagraphApi:', error);
		return NextResponse.json(
			{ error: 'Failed to generate paragraph. Please try again.' },
			{ status: 500 }
		);
	}
}
