import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getKeywordService } from '@/backend/wire';
import { auth } from '@/lib';
import { KeywordWithDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: keywordId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('Unauthorized: User not authenticated to fetch keyword.');
		if (!keywordId) {
			throw new ValidationError('Keyword ID is required for retrieval.');
		}

		const keywordService = getKeywordService();
		const keyword = await keywordService.getKeywordById(keywordId);

		return NextResponse.json(keyword as KeywordWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Error in getKeywordApi for keyword ${keywordId}:`, error);
		return NextResponse.json(
			{ error: 'Failed to fetch keyword. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: keywordId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('Unauthorized: User not authenticated to update keyword.');
		if (!keywordId) {
			throw new ValidationError('Keyword ID is required for update.');
		}

		const body = await request.json();
		const { name } = body;

		if (!name) {
			throw new ValidationError('Keyword name is required for update.');
		}

		if (name.trim().length === 0) {
			throw new ValidationError('Keyword name cannot be empty or whitespace.');
		}

		if (name.length > 100) {
			throw new ValidationError('Keyword name cannot exceed 100 characters.');
		}

		const keywordService = getKeywordService();
		const keyword = await keywordService.updateKeyword(keywordId, name.trim());

		return NextResponse.json(keyword as KeywordWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Error in updateKeywordApi for keyword ${keywordId}:`, error);
		return NextResponse.json(
			{ error: 'Failed to update keyword. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	const { id: keywordId } = await params;
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('Unauthorized: User not authenticated to delete keyword.');
		if (!keywordId) {
			throw new ValidationError('Keyword ID is required for deletion.');
		}

		const keywordService = getKeywordService();
		const success = await keywordService.deleteKeyword(keywordId);
		if (typeof success === 'boolean' && !success) throw new NotFoundError('Keyword', keywordId);

		return NextResponse.json({ success: true });
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Error in deleteKeywordApi for keyword ${keywordId}:`, error);
		return NextResponse.json(
			{ error: 'Failed to delete keyword. Please try again.' },
			{ status: 500 }
		);
	}
}
