import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getKeywordService } from '@/backend/wire';
import { auth } from '@/lib';
import { KeywordWithDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('Unauthorized: User not authenticated to fetch keywords.');

		const keywordService = getKeywordService();
		const keywords = await keywordService.getUserKeywords(userId);

		return NextResponse.json(keywords as KeywordWithDetail[]);
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(`Error in getAllKeywordsOfUserApi for user:`, error);
		return NextResponse.json(
			{ error: 'Failed to fetch keywords. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('Unauthorized: User not authenticated to create keyword.');

		const body = await request.json();
		const { name } = body;

		if (!name) {
			throw new ValidationError('Keyword name is required for creation.');
		}

		if (name.trim().length === 0) {
			throw new ValidationError('Keyword name cannot be empty or whitespace.');
		}

		if (name.length > 100) {
			throw new ValidationError('Keyword name cannot exceed 100 characters.');
		}

		const keywordService = getKeywordService();
		const keyword = await keywordService.createKeyword(userId, name.trim());

		return NextResponse.json(keyword as KeywordWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Error in createKeywordApi:', error);
		return NextResponse.json(
			{ error: 'Failed to create keyword. Please try again.' },
			{ status: 500 }
		);
	}
}
