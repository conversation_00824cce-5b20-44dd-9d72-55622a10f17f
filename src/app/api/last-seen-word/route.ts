import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLastSeenWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to save last seen word.'
			);
		}

		const body = await request.json();
		const { wordId } = body;

		if (!wordId) {
			throw new ValidationError('Word ID is required to save the last seen word.');
		}

		const lastSeenWordService = getLastSeenWordService();
		await lastSeenWordService.saveLastSeenWord(userId, wordId);

		return NextResponse.json({ success: true, message: 'Last seen word saved successfully.' });
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Error in saveLastSeenWordApi:', error);
		return NextResponse.json({ error: 'Failed to save the last seen word. Please try again.' }, { status: 500 });
	}
}
