import { ServerError } from '@/backend/errors';
import { getPrismaClient } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
	try {
		const prisma = getPrismaClient();

		// Get all feedback with user information
		const feedbacks = await prisma.feedback.findMany({
			include: {
				user: {
					select: {
						id: true,
						username: true,
						provider: true,
						provider_id: true,
						created_at: true,
					},
				},
			},
			orderBy: {
				created_at: 'desc',
			},
		});

		return NextResponse.json(feedbacks);
	} catch (error) {
		console.error('Error fetching feedback:', error);

		if (error instanceof ServerError) {
			return NextResponse.json({ error: error.message }, { status: error.statusCode });
		}

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}

export async function PATCH(request: NextRequest) {
	try {
		const { id, status } = await request.json();

		if (!id || !status) {
			return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
		}

		const prisma = getPrismaClient();

		const updatedFeedback = await prisma.feedback.update({
			where: { id },
			data: { status },
		});

		return NextResponse.json(updatedFeedback);
	} catch (error) {
		console.error('Error updating feedback:', error);

		if (error instanceof ServerError) {
			return NextResponse.json({ error: error.message }, { status: error.statusCode });
		}

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
