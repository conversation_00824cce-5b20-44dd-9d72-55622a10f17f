import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { tokenMonitor } from '@/backend/services/token-monitor.service';
import { verifyToken } from '@/backend/utils/token.util';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const stats = tokenMonitor.getStats();

		return NextResponse.json({
			success: true,
			data: stats,
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting token stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
