import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { tokenMonitor } from '@/backend/services/token-monitor.service';
import { verifyToken } from '@/backend/utils/token.util';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const body = await request.json();
		const {
			endpoint,
			operation,
			inputTokens,
			outputTokens,
			model,
			userId,
			optimized,
			compressionRatio,
		} = body;

		// Validate required fields
		if (
			!endpoint ||
			!operation ||
			inputTokens === undefined ||
			outputTokens === undefined ||
			!model
		) {
			throw new ValidationError(
				'Missing required fields: endpoint, operation, inputTokens, outputTokens, model'
			);
		}

		// Validate numeric fields
		if (
			typeof inputTokens !== 'number' ||
			typeof outputTokens !== 'number' ||
			inputTokens < 0 ||
			outputTokens < 0
		) {
			throw new ValidationError('inputTokens and outputTokens must be non-negative numbers');
		}

		tokenMonitor.trackUsage({
			endpoint,
			operation,
			inputTokens,
			outputTokens,
			model,
			userId,
			optimized: optimized || false,
			compressionRatio: compressionRatio || 1,
		});

		return NextResponse.json({
			success: true,
			message: 'Token usage tracked successfully',
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error tracking token usage:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
