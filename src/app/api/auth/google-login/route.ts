import { generateToken } from '@/backend/utils';
import { getAuthService } from '@/backend/wire';
import { getAuthConfig, getServerConfig } from '@/config';
import { cookies } from 'next/headers';
import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getFeatureFlags } from '@/config';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		// Check if Google login feature is enabled
		const featureFlags = await getFeatureFlags();
		if (!featureFlags.googleLogin) {
			return NextResponse.json({ error: 'Google login is not enabled' }, { status: 403 });
		}

		const body = await request.json();
		const { google_id } = body;

		if (!google_id) {
			return NextResponse.json({ error: 'Google ID is required' }, { status: 400 });
		}

		// Use Google ID as provider_id for Google OAuth
		const authService = getAuthService();
		const user = await authService.providerLogin('GOOGLE' as any, google_id);
		if (!user) throw new UnauthorizedError('Authentication failed');

		const token = await generateToken(user);
		if (!token) throw new Error('Token generation failed');

		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		const serverConfig = await getServerConfig();
		cookieStore.set({
			name: authConfig.jwtCookieName,
			value: token,
			httpOnly: true,
			secure: serverConfig.env === 'production',
			sameSite: 'strict',
			path: '/',
			maxAge: authConfig.jwtExpiresIn,
		});

		return NextResponse.json({
			success: true,
			message: 'Google login successful',
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Google login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
