import { getAuthConfig } from '@/config';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST() {
	try {
		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		cookieStore.delete(authConfig.jwtCookieName);

		return NextResponse.json({
			success: true,
			message: 'Logout successful',
		});
	} catch (error) {
		console.error('Logout error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
