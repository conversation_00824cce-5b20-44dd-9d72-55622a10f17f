import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { generateToken } from '@/backend/utils';
import { getAuthService } from '@/backend/wire';
import { getAuthConfig, getServerConfig } from '@/config';
import { Provider } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { provider, providerId } = body;

		if (!provider || !providerId || !Object.values(Provider).includes(provider)) {
			throw new ValidationError('Invalid provider or provider ID');
		}

		const authService = getAuthService();
		const user = await authService.providerLogin(provider, providerId);
		if (!user) throw new UnauthorizedError('Authentication failed');

		const token = await generateToken(user);
		if (!token) throw new Error('Token generation failed');

		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		const serverConfig = await getServerConfig();
		cookieStore.set({
			name: authConfig.jwtCookieName,
			value: token,
			httpOnly: true,
			secure: serverConfig.env === 'production',
			sameSite: 'strict',
			path: '/',
			maxAge: authConfig.jwtExpiresIn,
		});

		return NextResponse.json({
			success: true,
			message: 'Provider login successful',
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Provider login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
