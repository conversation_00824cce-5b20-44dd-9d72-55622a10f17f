import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { generateToken } from '@/backend/utils';
import { getAuthService } from '@/backend/wire';
import { getAuthConfig, getServerConfig } from '@/config';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { username, password } = body;

		if (!username || !password) {
			throw new ValidationError('Username and password are required');
		}

		if (username.trim().length < 3) {
			throw new ValidationError('Username must be at least 3 characters long');
		}

		if (password.length < 6) {
			throw new ValidationError('Password must be at least 6 characters long');
		}

		try {
			const authService = getAuthService();
			const user = await authService.usernamePasswordLogin(username.trim(), password);

			const token = await generateToken(user);
			if (!token) throw new Error('Token generation failed');

			const cookieStore = await cookies();
			const authConfig = await getAuthConfig();
			const serverConfig = await getServerConfig();
			cookieStore.set({
				name: authConfig.jwtCookieName,
				value: token,
				httpOnly: true,
				secure: serverConfig.env === 'production',
				sameSite: 'strict',
				path: '/',
				maxAge: authConfig.jwtExpiresIn,
			});

			return NextResponse.json({
				success: true,
				message: 'Login successful',
			});
		} catch (error) {
			if (error instanceof Error && error.message === 'Invalid password') {
				throw new UnauthorizedError('Invalid username or password');
			}
			throw error;
		}
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
