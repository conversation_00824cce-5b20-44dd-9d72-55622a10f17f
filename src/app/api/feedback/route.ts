import { ValidationError } from '@/backend/errors';
import { getFeedbackService } from '@/backend/wire';
import { auth } from '@/lib';
import { Feedback } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { message } = body;

		// Validate required fields
		if (!message) {
			throw new ValidationError('Message is required');
		}

		// Ensure user is authenticated and get the user ID
		const session = await auth();
		if (!session?.user?.id) {
			// If authentication fails or user ID is missing, throw an error as it's required.
			// Using a generic Error is appropriate as it's not a user input validation issue.
			throw new Error('Authentication required to submit feedback');
		}
		// authUserId is now guaranteed to be a string
		const authUserId = session.user.id;

		const feedbackService = getFeedbackService();
		const feedback = await feedbackService.createFeedback(message, authUserId);

		return NextResponse.json(feedback as Feedback);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof Error && error.message === 'Authentication required to submit feedback') {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		// Log the detailed error for server-side debugging
		console.error('Failed to submit feedback:', error);
		// Throw a generic error for the client
		return NextResponse.json({ error: 'Failed to submit feedback' }, { status: 500 });
	}
}
