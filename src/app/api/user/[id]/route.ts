import { ValidationError } from '@/backend/errors';
import { getUserService } from '@/backend/wire';
import { UserWithDetail } from '@/models/user';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const userIdToFetch = params.id;

		if (!userIdToFetch) {
			throw new ValidationError('User ID to fetch is required.');
		}

		const userService = getUserService();
		const user = await userService.getUserById(userIdToFetch);

		return NextResponse.json(user as UserWithDetail | null);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error(`Failed to get user by ID ${params.id}:`, error);
		return NextResponse.json({ error: 'Failed to fetch user by ID. Please try again.' }, { status: 500 });
	}
}
