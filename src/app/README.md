# App Directory

This directory contains the Next.js 15 App Router pages, layouts, and app-specific components following the new App Router architecture.

## Structure

```
app/
├── layout.tsx              # Root layout with providers and global setup
├── page.tsx               # Home page (landing page)
├── globals.css            # Global styles and Tailwind CSS
├── home.client.tsx        # Client-side home page components
├── not-found.tsx          # 404 error page
├── admin/                 # Admin dashboard pages
├── api/                   # API route handlers
├── collections/           # Collection management pages
├── components/            # App-specific components
├── dashboard/             # User dashboard pages
├── login/                 # Authentication pages
└── [test-pages]/          # Development and testing pages
```

## Key Features

### Root Layout (`layout.tsx`)
- Provider hierarchy setup (Error Management, Theme, Translation, Auth, etc.)
- Global font configuration (<PERSON>eist and <PERSON>eist Mono)
- PWA manifest and viewport configuration
- Accessibility attributes and ARIA labels
- Toast notification system integration

### Pages
- **Home Page**: Landing page with hero section and feature overview
- **Collections**: Vocabulary collection management interface
- **Dashboard**: User dashboard with learning progress
- **Admin**: Administrative interface for content management
- **Login**: Authentication flows for multiple providers

### API Routes (`api/`)
- RESTful API endpoints following Next.js App Router conventions
- Authentication and authorization middleware
- Input validation and error handling
- Rate limiting and security measures

### App Components (`components/`)
- Components specific to app-level functionality
- DOM client settings and configuration
- App-specific UI elements not shared across the application

## Conventions

- All pages use `page.tsx` naming convention
- Layouts use `layout.tsx` naming convention
- Client components are explicitly marked with `'use client'`
- Server components are the default
- TypeScript interfaces for all component props
- Accessibility-first design with proper ARIA attributes

## Development Notes

- Uses Next.js 15 App Router architecture
- Supports both Server and Client Components
- Integrated with global state management through React Context
- Follows clean architecture principles with separation of concerns
- Includes comprehensive error handling and loading states
