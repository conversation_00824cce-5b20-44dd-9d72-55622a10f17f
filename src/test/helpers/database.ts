import { PrismaClient } from '@prisma/client';

let prisma: PrismaClient;

export async function setupTestDatabase(): Promise<PrismaClient> {
	if (!prisma) {
		prisma = new PrismaClient({
			datasources: {
				db: {
					url: process.env.DATABASE_URL_TEST || process.env.DATABASE_URL,
				},
			},
		});

		await prisma.$connect();

		// Ensure database extensions are available
		try {
			await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
		} catch (error) {
			// Extension might already exist, ignore error
			console.warn('UUID extension setup warning:', error);
		}
	}

	return prisma;
}

export async function cleanupTestDatabase(prismaClient?: PrismaClient): Promise<void> {
	const client = prismaClient || prisma;

	if (!client) return;

	try {
		// Clean up in reverse dependency order
		await client.$transaction([
			client.collectionStats.deleteMany(),
			client.lastSeenWord.deleteMany(),
			client.feedback.deleteMany(),
			client.keyword.deleteMany(),
			client.collection.deleteMany(),
			client.multipleChoiceExercise.deleteMany(),
			client.paragraph.deleteMany(),
			client.example.deleteMany(),
			client.explain.deleteMany(),
			client.definition.deleteMany(),
			client.word.deleteMany(),
			client.user.deleteMany(),
		]);
	} catch (error) {
		console.error('Database cleanup error:', error);
	}
}

export async function resetTestDatabase(): Promise<void> {
	if (prisma) {
		await cleanupTestDatabase(prisma);
	}
}

export async function disconnectTestDatabase(): Promise<void> {
	if (prisma) {
		await prisma.$disconnect();
	}
}

// Global setup for tests
export async function globalSetup(): Promise<void> {
	await setupTestDatabase();
}

// Global teardown for tests
export async function globalTeardown(): Promise<void> {
	await cleanupTestDatabase();
	await disconnectTestDatabase();
}
