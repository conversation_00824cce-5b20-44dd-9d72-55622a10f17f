# Test Directory

This directory contains test setup files, testing utilities, mocks, and fixtures for future testing implementation. The test infrastructure is preserved and ready for use when tests are needed.

## Structure

```
test/
├── setup.ts                                    # Test environment setup (Jest)
├── vitest-setup.ts                            # Vitest-specific configuration
├── fixtures/                                  # Test data and fixtures
├── helpers/                                   # Testing utility functions
└── mocks/                                     # Mock implementations
```

## Test Infrastructure Components

### Test Setup and Configuration

-   **setup.ts**: Global test environment configuration for Jest

    -   Next.js router mocking (useRouter, useSearchParams, usePathname, etc.)
    -   Image and Link component mocking
    -   Environment variables setup
    -   Browser API mocking (matchMedia, IntersectionObserver, ResizeObserver)
    -   localStorage and sessionStorage mocking
    -   Test cleanup utilities

-   **vitest-setup.ts**: Vitest-specific configuration and test utilities
    -   Similar setup to Jest but optimized for Vitest
    -   Fast unit testing configuration
    -   Vite compatibility setup

### Available Infrastructure

#### Mocks and Fixtures

-   **fixtures/**: Test data, sample objects, and test scenarios ready for use
-   **mocks/**: Mock implementations for external dependencies
    -   MSW handlers for API mocking
    -   Repository mocks for database operations
    -   Server setup for request interception
-   **helpers/**: Testing utility functions and custom matchers
    -   Database testing utilities
    -   Custom assertion helpers

#### Ready-to-Use Features

-   Complete Jest and Vitest configuration
-   Next.js testing environment setup
-   Mock implementations for common dependencies
-   Test data fixtures and factories
-   Comprehensive browser API mocking
-   TypeScript support for all test files

## Usage

When you're ready to add tests back to the project, you can:

1. **Create test files** following the patterns:

    - `*.test.ts` or `*.test.tsx` for unit tests
    - `*.spec.ts` or `*.spec.tsx` for integration tests
    - Place them alongside the code they test or in `__tests__` directories

2. **Add test scripts back to package.json**:

    ```json
    {
    	"scripts": {
    		"test": "jest",
    		"test:watch": "jest --watch",
    		"test:coverage": "jest --coverage",
    		"test:unit": "vitest",
    		"test:unit:watch": "vitest --watch",
    		"test:unit:coverage": "vitest --coverage",
    		"test:e2e": "playwright test"
    	}
    }
    ```

3. **Use existing infrastructure**:
    - Import test utilities from `@/test/helpers`
    - Use mock data from `@/test/fixtures`
    - Leverage pre-configured mocks from `@/test/mocks`

All test configuration files, setup files, and testing infrastructure remain intact and ready for immediate use.

## Configuration Files Preserved

-   **jest.config.js**: Complete Jest configuration with Next.js integration
-   **vitest.config.ts**: Vitest configuration with React support
-   **playwright.config.ts**: E2E testing configuration
-   **tsconfig.json**: TypeScript configuration with test exclusions
-   **src/test/setup.ts**: Jest test environment setup
-   **src/test/vitest-setup.ts**: Vitest test environment setup
-   **e2e/global-setup.ts**: Playwright global setup
-   **e2e/global-teardown.ts**: Playwright global teardown

The testing infrastructure is complete and ready for when you need to implement tests in the future.
