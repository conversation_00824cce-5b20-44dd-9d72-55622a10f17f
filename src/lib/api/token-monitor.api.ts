import { apiClient } from '@/lib/api-interceptor';
import { useState, useEffect, useCallback } from 'react';

export interface TokenUsageStats {
	totalTokens: number;
	totalCost: number;
	requestCount: number;
	averageTokensPerRequest: number;
	averageCostPerRequest: number;
	optimizationSavings: {
		tokensSaved: number;
		costSaved: number;
		compressionRatio: number;
	};
}

export interface CostAnalysis {
	daily: TokenUsageStats;
	weekly: TokenUsageStats;
	monthly: TokenUsageStats;
	byOperation: Record<string, TokenUsageStats>;
	trends: {
		tokenUsageTrend: number;
		costTrend: number;
		optimizationTrend: number;
	};
}

export interface OptimizationSuggestion {
	type: 'cache' | 'prompt' | 'batch' | 'model';
	operation: string;
	description: string;
	estimatedSavings: {
		tokens: number;
		cost: number;
		percentage: number;
	};
	priority: 'high' | 'medium' | 'low';
	implementation: string;
}

export interface BudgetAlert {
	type: string;
	message: string;
	severity: 'warning' | 'critical';
}

export interface CacheStats {
	regular: {
		hits: number;
		misses: number;
		keys: number;
		hitRate: number;
		memoryUsage: number;
	};
	semantic: {
		enabled: boolean;
		totalEntries: number;
		totalKeywords: number;
		expiredCount: number;
		averageAccess: number;
	};
	combined: {
		totalEntries: number;
		hitRate: number;
		memoryUsage: number;
		keywordIndex: number;
		expiredEntries: number;
	};
}

export interface BatchStats {
	queueLength: number;
	processing: boolean;
	config: {
		maxBatchSize: Record<string, number>;
		maxWaitTime: number;
		maxTokensPerBatch: number;
	};
}

export interface ModelStats {
	models: Record<
		string,
		{
			performance: {
				averageLatency: number;
				reliability: number;
				qualityScore: number;
			};
			capabilities: number;
			availability: string;
			costPer1kTokens: number;
		}
	>;
	available: string[];
	summary: {
		totalModels: number;
		averageCost: number;
	};
}

export class TokenMonitorAPI {
	/**
	 * Get token usage statistics
	 */
	static async getStats(timeframe: 'day' | 'week' | 'month' = 'day'): Promise<TokenUsageStats> {
		const response = await apiClient.get(`/api/token-monitor/stats?timeframe=${timeframe}`);
		return response.data;
	}

	/**
	 * Get comprehensive cost analysis
	 */
	static async getCostAnalysis(): Promise<CostAnalysis> {
		const response = await apiClient.get('/api/token-monitor/analysis');
		return response.data;
	}

	/**
	 * Get optimization suggestions
	 */
	static async getOptimizationSuggestions(): Promise<OptimizationSuggestion[]> {
		const response = await apiClient.get('/api/token-monitor/suggestions');
		return response.data;
	}

	/**
	 * Get budget alerts
	 */
	static async getBudgetAlerts(): Promise<BudgetAlert[]> {
		const response = await apiClient.get('/api/token-monitor/alerts');
		return response.data;
	}

	/**
	 * Get cache performance statistics
	 */
	static async getCacheStats(): Promise<CacheStats> {
		const response = await apiClient.get('/api/token-monitor/cache-stats');
		return response.data;
	}

	/**
	 * Get batch processing statistics
	 */
	static async getBatchStats(): Promise<BatchStats> {
		const response = await apiClient.get('/api/token-monitor/batch-stats');
		return response.data;
	}

	/**
	 * Get model selection statistics
	 */
	static async getModelStats(): Promise<ModelStats> {
		const response = await apiClient.get('/api/token-monitor/model-stats');
		return response.data;
	}

	/**
	 * Clean up expired cache entries
	 */
	static async cleanupCache(): Promise<{ cleanedEntries: number; message: string }> {
		const response = await apiClient.post('/api/token-monitor/cleanup-cache');
		return response.data;
	}

	/**
	 * Track token usage manually (for testing)
	 */
	static async trackUsage(data: {
		endpoint: string;
		operation: string;
		inputTokens: number;
		outputTokens: number;
		model: string;
		userId?: string;
		optimized?: boolean;
		compressionRatio?: number;
	}): Promise<{ message: string }> {
		const response = await apiClient.post('/api/token-monitor/track', data);
		return response.data;
	}
}

/**
 * Hook for real-time token monitoring data
 */
export function useTokenMonitoring(refreshInterval: number = 30000) {
	const [data, setData] = useState<{
		stats: TokenUsageStats | null;
		analysis: CostAnalysis | null;
		suggestions: OptimizationSuggestion[];
		alerts: BudgetAlert[];
		cacheStats: CacheStats | null;
		batchStats: BatchStats | null;
		modelStats: ModelStats | null;
		loading: boolean;
		error: string | null;
	}>({
		stats: null,
		analysis: null,
		suggestions: [],
		alerts: [],
		cacheStats: null,
		batchStats: null,
		modelStats: null,
		loading: true,
		error: null,
	});

	const fetchData = useCallback(async () => {
		try {
			setData((prev) => ({ ...prev, loading: true, error: null }));

			const [stats, analysis, suggestions, alerts, cacheStats, batchStats, modelStats] =
				await Promise.all([
					TokenMonitorAPI.getStats('day'),
					TokenMonitorAPI.getCostAnalysis(),
					TokenMonitorAPI.getOptimizationSuggestions(),
					TokenMonitorAPI.getBudgetAlerts(),
					TokenMonitorAPI.getCacheStats(),
					TokenMonitorAPI.getBatchStats(),
					TokenMonitorAPI.getModelStats(),
				]);

			setData({
				stats,
				analysis,
				suggestions,
				alerts,
				cacheStats,
				batchStats,
				modelStats,
				loading: false,
				error: null,
			});
		} catch (error) {
			console.error('Error fetching monitoring data:', error);
			setData((prev) => ({
				...prev,
				loading: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			}));
		}
	}, []);

	useEffect(() => {
		fetchData();

		const interval = setInterval(fetchData, refreshInterval);
		return () => clearInterval(interval);
	}, [fetchData, refreshInterval]);

	return {
		...data,
		refresh: fetchData,
	};
}
