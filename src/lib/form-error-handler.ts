'use client';

import { FieldError, FieldErrors, FieldValues } from 'react-hook-form';
import { ZodError, ZodIssue } from 'zod';
import { AppError, ValidationError, createErrorContext, errorLogger } from './error-handling';

// ============================================================================
// TYPES
// ============================================================================

export interface FormErrorState {
	hasErrors: boolean;
	fieldErrors: Record<string, string>;
	globalError: string | null;
	errorCount: number;
}

export interface ValidationResult {
	isValid: boolean;
	errors: FormErrorState;
	firstErrorField?: string;
}

export interface FormErrorHandlerOptions {
	showGlobalErrors?: boolean;
	logErrors?: boolean;
	translateErrors?: boolean;
	errorPrefix?: string;
}

// ============================================================================
// FORM ERROR HANDLER CLASS
// ============================================================================

export class FormErrorHandler {
	private options: Required<FormErrorHandlerOptions>;

	constructor(options: FormErrorHandlerOptions = {}) {
		this.options = {
			showGlobalErrors: true,
			logErrors: true,
			translateErrors: false,
			errorPrefix: 'form',
			...options,
		};
	}

	// Convert React Hook Form errors to our format
	fromReactHookForm(errors: FieldErrors<FieldValues>): FormErrorState {
		const fieldErrors: Record<string, string> = {};
		let errorCount = 0;

		const processError = (key: string, error: FieldError) => {
			if (error.message) {
				fieldErrors[key] = error.message;
				errorCount++;
			}
		};

		const processErrors = (errors: FieldErrors<FieldValues>, prefix = '') => {
			Object.entries(errors).forEach(([key, error]) => {
				const fullKey = prefix ? `${prefix}.${key}` : key;

				if (error && typeof error === 'object') {
					if ('message' in error) {
						processError(fullKey, error as FieldError);
					} else {
						// Nested errors
						processErrors(error as FieldErrors<FieldValues>, fullKey);
					}
				}
			});
		};

		processErrors(errors);

		const result: FormErrorState = {
			hasErrors: errorCount > 0,
			fieldErrors,
			globalError: null,
			errorCount,
		};

		if (this.options.logErrors && result.hasErrors) {
			errorLogger.warn(
				'Form validation errors detected',
				{ fieldErrors, errorCount },
				'FormErrorHandler'
			);
		}

		return result;
	}

	// Convert Zod errors to our format
	fromZodError(error: ZodError): FormErrorState {
		const fieldErrors: Record<string, string> = {};
		let errorCount = 0;

		error.issues.forEach((issue: ZodIssue) => {
			const path = issue.path.join('.');
			const message = this.formatZodMessage(issue);

			if (path) {
				fieldErrors[path] = message;
			} else {
				fieldErrors['_global'] = message;
			}
			errorCount++;
		});

		const result: FormErrorState = {
			hasErrors: errorCount > 0,
			fieldErrors,
			globalError: fieldErrors['_global'] || null,
			errorCount,
		};

		if (this.options.logErrors && result.hasErrors) {
			errorLogger.warn(
				'Zod validation errors detected',
				{ issues: error.issues, fieldErrors, errorCount },
				'FormErrorHandler'
			);
		}

		return result;
	}

	// Convert API validation errors to our format
	fromApiError(error: AppError | Error): FormErrorState {
		let fieldErrors: Record<string, string> = {};
		let globalError: string | null = null;
		let errorCount = 0;

		if (error instanceof ValidationError && error.context?.additionalData?.fieldErrors) {
			// API returned structured field errors
			fieldErrors = error.context.additionalData.fieldErrors;
			errorCount = Object.keys(fieldErrors).length;
		} else {
			// Generic error - show as global error
			globalError = error.message;
			errorCount = 1;
		}

		const result: FormErrorState = {
			hasErrors: errorCount > 0,
			fieldErrors,
			globalError,
			errorCount,
		};

		if (this.options.logErrors && result.hasErrors) {
			errorLogger.error(
				'API validation error converted to form errors',
				error instanceof AppError ? error : new Error(error.message),
				{ fieldErrors, globalError, errorCount },
				'FormErrorHandler'
			);
		}

		return result;
	}

	// Validate form data and return result
	async validateForm<T extends FieldValues>(
		data: T,
		validator: (data: T) => Promise<void> | void
	): Promise<ValidationResult> {
		try {
			await validator(data);

			return {
				isValid: true,
				errors: {
					hasErrors: false,
					fieldErrors: {},
					globalError: null,
					errorCount: 0,
				},
			};
		} catch (error) {
			let formErrors: FormErrorState;

			if (error instanceof ZodError) {
				formErrors = this.fromZodError(error);
			} else if (error instanceof AppError) {
				formErrors = this.fromApiError(error);
			} else {
				formErrors = this.fromApiError(new Error(String(error)));
			}

			const firstErrorField = Object.keys(formErrors.fieldErrors)[0];

			return {
				isValid: false,
				errors: formErrors,
				firstErrorField,
			};
		}
	}

	// Get error message for a specific field
	getFieldError(errors: FormErrorState, fieldName: string): string | undefined {
		return errors.fieldErrors[fieldName];
	}

	// Check if a specific field has an error
	hasFieldError(errors: FormErrorState, fieldName: string): boolean {
		return fieldName in errors.fieldErrors;
	}

	// Get all error messages as an array
	getAllErrorMessages(errors: FormErrorState): string[] {
		const messages = Object.values(errors.fieldErrors);

		if (errors.globalError) {
			messages.unshift(errors.globalError);
		}

		return messages;
	}

	// Clear specific field error
	clearFieldError(errors: FormErrorState, fieldName: string): FormErrorState {
		const newFieldErrors = { ...errors.fieldErrors };
		delete newFieldErrors[fieldName];

		return {
			...errors,
			fieldErrors: newFieldErrors,
			errorCount: Object.keys(newFieldErrors).length + (errors.globalError ? 1 : 0),
			hasErrors: Object.keys(newFieldErrors).length > 0 || !!errors.globalError,
		};
	}

	// Clear all errors
	clearAllErrors(): FormErrorState {
		return {
			hasErrors: false,
			fieldErrors: {},
			globalError: null,
			errorCount: 0,
		};
	}

	// Format Zod error message
	private formatZodMessage(issue: ZodIssue): string {
		switch (issue.code) {
			case 'invalid_type':
				return `Expected ${issue.expected}, received ${issue.received}`;
			case 'invalid_string':
				if (issue.validation === 'email') {
					return 'Please enter a valid email address';
				}
				if (issue.validation === 'url') {
					return 'Please enter a valid URL';
				}
				return issue.message;
			case 'too_small':
				if (issue.type === 'string') {
					return `Must be at least ${issue.minimum} characters`;
				}
				if (issue.type === 'number') {
					return `Must be at least ${issue.minimum}`;
				}
				return issue.message;
			case 'too_big':
				if (issue.type === 'string') {
					return `Must be no more than ${issue.maximum} characters`;
				}
				if (issue.type === 'number') {
					return `Must be no more than ${issue.maximum}`;
				}
				return issue.message;
			case 'invalid_enum_value':
				return `Must be one of: ${issue.options.join(', ')}`;
			case 'custom':
				return issue.message || 'Invalid value';
			default:
				return issue.message;
		}
	}
}

// ============================================================================
// REACT HOOK
// ============================================================================

export function useFormErrorHandler(options?: FormErrorHandlerOptions) {
	const handler = new FormErrorHandler(options);

	return {
		fromReactHookForm: handler.fromReactHookForm.bind(handler),
		fromZodError: handler.fromZodError.bind(handler),
		fromApiError: handler.fromApiError.bind(handler),
		validateForm: handler.validateForm.bind(handler),
		getFieldError: handler.getFieldError.bind(handler),
		hasFieldError: handler.hasFieldError.bind(handler),
		getAllErrorMessages: handler.getAllErrorMessages.bind(handler),
		clearFieldError: handler.clearFieldError.bind(handler),
		clearAllErrors: handler.clearAllErrors.bind(handler),
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createValidationError(
	fieldErrors: Record<string, string>,
	globalMessage?: string
): ValidationError {
	const context = createErrorContext('FormValidation', 'validation_failed', {
		fieldErrors,
		globalMessage,
	});

	return new ValidationError(globalMessage || 'Form validation failed', {
		...context,
		additionalData: {
			...context.additionalData,
			fieldErrors,
		},
	});
}

export function extractFieldErrorsFromApiResponse(response: any): Record<string, string> {
	const fieldErrors: Record<string, string> = {};

	// Handle different API error response formats
	if (response?.errors) {
		if (Array.isArray(response.errors)) {
			// Array of error objects with field and message
			response.errors.forEach((error: any) => {
				if (error.field && error.message) {
					fieldErrors[error.field] = error.message;
				}
			});
		} else if (typeof response.errors === 'object') {
			// Object with field names as keys
			Object.entries(response.errors).forEach(([field, message]) => {
				if (typeof message === 'string') {
					fieldErrors[field] = message;
				} else if (Array.isArray(message) && message.length > 0) {
					fieldErrors[field] = message[0];
				}
			});
		}
	}

	return fieldErrors;
}

// Default instance
export const formErrorHandler = new FormErrorHandler();
