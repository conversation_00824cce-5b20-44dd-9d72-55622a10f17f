// ============================================================================
// ERROR SEVERITY LEVELS
// ============================================================================

export enum ErrorSeverity {
	LOW = 'low',
	MEDIUM = 'medium',
	HIGH = 'high',
	CRITICAL = 'critical',
}

export enum ErrorCategory {
	NETWORK = 'network',
	VALIDATION = 'validation',
	AUTHENTICATION = 'authentication',
	AUTHORIZATION = 'authorization',
	NOT_FOUND = 'not_found',
	SERVER = 'server',
	CLIENT = 'client',
	UNKNOWN = 'unknown',
}

// ============================================================================
// ERROR CONTEXT INTERFACES
// ============================================================================

export interface UserContext {
	userId?: string;
	sessionId?: string;
	userAgent?: string;
	timestamp: string;
	url: string;
	referrer?: string;
}

export interface ApplicationContext {
	component?: string;
	action?: string;
	state?: Record<string, any>;
	props?: Record<string, any>;
	route?: string;
}

export interface BrowserContext {
	userAgent: string;
	language: string;
	platform: string;
	cookieEnabled: boolean;
	onLine: boolean;
	viewport: {
		width: number;
		height: number;
	};
}

export interface ErrorContext {
	user: UserContext;
	application: ApplicationContext;
	browser: BrowserContext;
	stackTrace?: string;
	additionalData?: Record<string, any>;
}

// ============================================================================
// ENHANCED ERROR CLASSES
// ============================================================================

export class AppError extends Error {
	public readonly timestamp: string;
	public readonly id: string;
	public context?: ErrorContext;

	constructor(
		message: string,
		public readonly code: string,
		public readonly statusCode: number = 500,
		public readonly severity: ErrorSeverity = ErrorSeverity.MEDIUM,
		public readonly category: ErrorCategory = ErrorCategory.UNKNOWN,
		context?: Partial<ErrorContext>
	) {
		super(message);
		this.name = 'AppError';
		this.timestamp = new Date().toISOString();
		this.id = this.generateErrorId();

		if (context) {
			this.context = this.enrichContext(context);
		}
	}

	private generateErrorId(): string {
		return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	private enrichContext(partialContext: Partial<ErrorContext>): ErrorContext {
		const defaultContext: ErrorContext = {
			user: {
				timestamp: this.timestamp,
				url: typeof window !== 'undefined' ? window.location.href : '',
				referrer: typeof window !== 'undefined' ? document.referrer : '',
				userAgent: typeof window !== 'undefined' ? navigator.userAgent : '',
			},
			application: {
				route: typeof window !== 'undefined' ? window.location.pathname : '',
			},
			browser: this.getBrowserContext(),
			stackTrace: this.stack,
		};

		return {
			...defaultContext,
			...partialContext,
			user: { ...defaultContext.user, ...partialContext.user },
			application: { ...defaultContext.application, ...partialContext.application },
			browser: { ...defaultContext.browser, ...partialContext.browser },
		};
	}

	private getBrowserContext(): BrowserContext {
		if (typeof window === 'undefined') {
			return {
				userAgent: '',
				language: '',
				platform: '',
				cookieEnabled: false,
				onLine: false,
				viewport: { width: 0, height: 0 },
			};
		}

		return {
			userAgent: navigator.userAgent,
			language: navigator.language,
			platform: navigator.platform,
			cookieEnabled: navigator.cookieEnabled,
			onLine: navigator.onLine,
			viewport: {
				width: window.innerWidth,
				height: window.innerHeight,
			},
		};
	}

	public toLogObject() {
		return {
			id: this.id,
			name: this.name,
			message: this.message,
			code: this.code,
			statusCode: this.statusCode,
			severity: this.severity,
			category: this.category,
			timestamp: this.timestamp,
			context: this.context,
		};
	}
}

export class ValidationError extends AppError {
	constructor(message: string, context?: Partial<ErrorContext>) {
		super(
			message,
			'VALIDATION_ERROR',
			400,
			ErrorSeverity.LOW,
			ErrorCategory.VALIDATION,
			context
		);
		this.name = 'ValidationError';
	}
}

export class NotFoundError extends AppError {
	constructor(resource: string, id: string, context?: Partial<ErrorContext>) {
		super(
			`${resource} with id ${id} not found`,
			'NOT_FOUND',
			404,
			ErrorSeverity.MEDIUM,
			ErrorCategory.NOT_FOUND,
			context
		);
		this.name = 'NotFoundError';
	}
}

export class UnauthorizedError extends AppError {
	constructor(message: string = 'Unauthorized access', context?: Partial<ErrorContext>) {
		super(
			message,
			'UNAUTHORIZED',
			401,
			ErrorSeverity.HIGH,
			ErrorCategory.AUTHENTICATION,
			context
		);
		this.name = 'UnauthorizedError';
	}
}

export class ForbiddenError extends AppError {
	constructor(message: string = 'Access forbidden', context?: Partial<ErrorContext>) {
		super(message, 'FORBIDDEN', 403, ErrorSeverity.HIGH, ErrorCategory.AUTHORIZATION, context);
		this.name = 'ForbiddenError';
	}
}

export class NetworkError extends AppError {
	constructor(message: string = 'Network request failed', context?: Partial<ErrorContext>) {
		super(message, 'NETWORK_ERROR', 0, ErrorSeverity.HIGH, ErrorCategory.NETWORK, context);
		this.name = 'NetworkError';
	}
}

export class TimeoutError extends AppError {
	constructor(message: string = 'Request timeout', context?: Partial<ErrorContext>) {
		super(message, 'TIMEOUT_ERROR', 408, ErrorSeverity.MEDIUM, ErrorCategory.NETWORK, context);
		this.name = 'TimeoutError';
	}
}

export class ServerError extends AppError {
	constructor(message: string = 'Internal server error', context?: Partial<ErrorContext>) {
		super(message, 'SERVER_ERROR', 500, ErrorSeverity.HIGH, ErrorCategory.SERVER, context);
		this.name = 'ServerError';
	}
}

export class ClientError extends AppError {
	constructor(message: string, context?: Partial<ErrorContext>) {
		super(message, 'CLIENT_ERROR', 400, ErrorSeverity.MEDIUM, ErrorCategory.CLIENT, context);
		this.name = 'ClientError';
	}
}

// ============================================================================
// ERROR LOGGING SYSTEM
// ============================================================================

export interface LogLevel {
	ERROR: 'error';
	WARN: 'warn';
	INFO: 'info';
	DEBUG: 'debug';
}

export const LOG_LEVELS: LogLevel = {
	ERROR: 'error',
	WARN: 'warn',
	INFO: 'info',
	DEBUG: 'debug',
};

export interface LogEntry {
	level: keyof LogLevel;
	message: string;
	timestamp: string;
	error?: AppError;
	context?: Record<string, any>;
	source?: string;
}

export class ErrorLogger {
	private static instance: ErrorLogger;
	private logs: LogEntry[] = [];
	private maxLogs: number = 1000;
	private enableConsoleLogging: boolean = true;
	private enableRemoteLogging: boolean = false;
	private remoteEndpoint?: string;

	private constructor() {
		// Private constructor for singleton
	}

	public static getInstance(): ErrorLogger {
		if (!ErrorLogger.instance) {
			ErrorLogger.instance = new ErrorLogger();
		}
		return ErrorLogger.instance;
	}

	public configure(options: {
		maxLogs?: number;
		enableConsoleLogging?: boolean;
		enableRemoteLogging?: boolean;
		remoteEndpoint?: string;
	}) {
		this.maxLogs = options.maxLogs ?? this.maxLogs;
		this.enableConsoleLogging = options.enableConsoleLogging ?? this.enableConsoleLogging;
		this.enableRemoteLogging = options.enableRemoteLogging ?? this.enableRemoteLogging;
		this.remoteEndpoint = options.remoteEndpoint;
	}

	public error(
		message: string,
		error?: AppError | Error,
		context?: Record<string, any>,
		source?: string
	) {
		this.log('ERROR', message, error, context, source);
	}

	public warn(message: string, context?: Record<string, any>, source?: string) {
		this.log('WARN', message, undefined, context, source);
	}

	public info(message: string, context?: Record<string, any>, source?: string) {
		this.log('INFO', message, undefined, context, source);
	}

	public debug(message: string, context?: Record<string, any>, source?: string) {
		this.log('DEBUG', message, undefined, context, source);
	}

	private log(
		level: keyof LogLevel,
		message: string,
		error?: AppError | Error,
		context?: Record<string, any>,
		source?: string
	) {
		const logEntry: LogEntry = {
			level,
			message,
			timestamp: new Date().toISOString(),
			context,
			source,
		};

		// Convert regular Error to AppError for consistent logging
		if (error) {
			if (error instanceof AppError) {
				logEntry.error = error;
			} else {
				logEntry.error = new AppError(
					error.message,
					'UNKNOWN_ERROR',
					500,
					ErrorSeverity.MEDIUM,
					ErrorCategory.UNKNOWN
				);
			}
		}

		// Add to in-memory logs
		this.addToLogs(logEntry);

		// Console logging
		if (this.enableConsoleLogging) {
			this.logToConsole(logEntry);
		}

		// Remote logging
		if (this.enableRemoteLogging && this.remoteEndpoint) {
			this.logToRemote(logEntry);
		}
	}

	private addToLogs(logEntry: LogEntry) {
		this.logs.push(logEntry);

		// Keep only the most recent logs
		if (this.logs.length > this.maxLogs) {
			this.logs = this.logs.slice(-this.maxLogs);
		}
	}

	private logToConsole(logEntry: LogEntry) {
		const { level, message, timestamp, error, context, source } = logEntry;
		const prefix = `[${timestamp}] ${level.toUpperCase()}${source ? ` [${source}]` : ''}:`;

		switch (level) {
			case 'ERROR':
				console.error(prefix, message, error?.toLogObject?.() || error, context);
				break;
			case 'WARN':
				console.warn(prefix, message, context);
				break;
			case 'INFO':
				console.info(prefix, message, context);
				break;
			case 'DEBUG':
				console.debug(prefix, message, context);
				break;
		}
	}

	private async logToRemote(logEntry: LogEntry) {
		if (!this.remoteEndpoint) return;

		try {
			await fetch(this.remoteEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(logEntry),
			});
		} catch (error) {
			// Fallback to console if remote logging fails
			console.error('Failed to send log to remote endpoint:', error);
		}
	}

	public getLogs(level?: keyof LogLevel, limit?: number): LogEntry[] {
		let filteredLogs = level ? this.logs.filter((log) => log.level === level) : this.logs;

		if (limit) {
			filteredLogs = filteredLogs.slice(-limit);
		}

		return filteredLogs;
	}

	public clearLogs() {
		this.logs = [];
	}

	public exportLogs(): string {
		return JSON.stringify(this.logs, null, 2);
	}
}

// Singleton instance
export const errorLogger = ErrorLogger.getInstance();

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Converts any error to an AppError with proper context
 */
export function normalizeError(
	error: unknown,
	defaultMessage: string = 'An unexpected error occurred',
	context?: Partial<ErrorContext>
): AppError {
	if (error instanceof AppError) {
		return error;
	}

	if (error instanceof Error) {
		return new AppError(
			error.message || defaultMessage,
			'NORMALIZED_ERROR',
			500,
			ErrorSeverity.MEDIUM,
			ErrorCategory.UNKNOWN,
			context
		);
	}

	// Handle string errors
	if (typeof error === 'string') {
		return new AppError(
			error || defaultMessage,
			'STRING_ERROR',
			500,
			ErrorSeverity.MEDIUM,
			ErrorCategory.UNKNOWN,
			context
		);
	}

	// Handle unknown error types
	return new AppError(
		defaultMessage,
		'UNKNOWN_ERROR',
		500,
		ErrorSeverity.MEDIUM,
		ErrorCategory.UNKNOWN,
		context
	);
}

/**
 * Creates error context from component information
 */
export function createErrorContext(
	component?: string,
	action?: string,
	state?: Record<string, any>,
	additionalData?: Record<string, any>
): Partial<ErrorContext> {
	return {
		application: {
			component,
			action,
			state,
		},
		additionalData,
	};
}

/**
 * Logs and returns a normalized error
 */
export function logAndNormalizeError(
	error: unknown,
	message: string,
	source?: string,
	context?: Partial<ErrorContext>
): AppError {
	const normalizedError = normalizeError(error, message, context);
	errorLogger.error(message, normalizedError, context, source);
	return normalizedError;
}

/**
 * Determines if an error should be retried
 */
export function isRetryableError(error: AppError): boolean {
	const retryableCategories = [ErrorCategory.NETWORK, ErrorCategory.SERVER];
	const retryableCodes = ['TIMEOUT_ERROR', 'NETWORK_ERROR', 'SERVER_ERROR'];

	return (
		retryableCategories.includes(error.category) ||
		retryableCodes.includes(error.code) ||
		(error.statusCode >= 500 && error.statusCode < 600)
	);
}

/**
 * Gets user-friendly error message based on error type
 */
export function getUserFriendlyMessage(error: AppError): string {
	switch (error.category) {
		case ErrorCategory.NETWORK:
			return 'Connection problem. Please check your internet connection and try again.';
		case ErrorCategory.AUTHENTICATION:
			return 'Please log in to continue.';
		case ErrorCategory.AUTHORIZATION:
			return "You don't have permission to perform this action.";
		case ErrorCategory.NOT_FOUND:
			return 'The requested item could not be found.';
		case ErrorCategory.VALIDATION:
			return error.message; // Validation messages are usually user-friendly
		case ErrorCategory.SERVER:
			return 'Something went wrong on our end. Please try again later.';
		default:
			return 'An unexpected error occurred. Please try again.';
	}
}

/**
 * Determines error display priority for UI
 */
export function getErrorDisplayPriority(error: AppError): number {
	switch (error.severity) {
		case ErrorSeverity.CRITICAL:
			return 4;
		case ErrorSeverity.HIGH:
			return 3;
		case ErrorSeverity.MEDIUM:
			return 2;
		case ErrorSeverity.LOW:
			return 1;
		default:
			return 0;
	}
}
