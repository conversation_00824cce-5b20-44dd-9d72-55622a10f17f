/**
 * Comprehensive Error Handling Integration
 *
 * This file provides utilities to integrate error handling throughout the entire application.
 * It includes helpers for components, hooks, services, and API routes.
 */

import {
	errorLogger,
	createErrorContext,
	normalizeError,
	ValidationError,
	ServerError,
	NotFoundError,
} from '@/lib/error-handling';

import { retryApiCall } from '@/lib/retry';

// ============================================================================
// HOOK ERROR INTEGRATION
// ============================================================================

/**
 * Enhanced hook wrapper that adds error handling to custom hooks
 */
export function withHookErrorHandling<T extends (...args: any[]) => any>(
	hook: T,
	hookName: string
): T {
	return ((...args: any[]) => {
		try {
			const result = hook(...args);

			// If result has async methods, wrap them with error handling
			if (result && typeof result === 'object') {
				const enhancedResult = { ...result };

				Object.keys(result).forEach((key) => {
					const value = result[key];
					if (typeof value === 'function') {
						enhancedResult[key] = async (...funcArgs: any[]) => {
							try {
								return await value(...funcArgs);
							} catch (error) {
								const normalizedError = normalizeError(
									error,
									`Hook ${hookName} method ${key} failed`,
									createErrorContext(hookName, key)
								);
								throw normalizedError;
							}
						};
					}
				});

				return enhancedResult;
			}

			return result;
		} catch (error) {
			const normalizedError = normalizeError(
				error,
				`Hook ${hookName} failed`,
				createErrorContext(hookName, 'initialization')
			);
			throw normalizedError;
		}
	}) as T;
}

// ============================================================================
// SERVICE ERROR INTEGRATION
// ============================================================================

/**
 * Service method wrapper that adds error handling and retry logic
 */
export function withServiceErrorHandling<T extends (...args: any[]) => Promise<any>>(
	serviceMethod: T,
	serviceName: string,
	methodName: string,
	options: {
		retryable?: boolean;
		maxRetries?: number;
		logErrors?: boolean;
	} = {}
): T {
	const { retryable = true, maxRetries = 3, logErrors = true } = options;

	return (async (...args: any[]) => {
		const executeMethod = async () => {
			try {
				return await serviceMethod(...args);
			} catch (error) {
				const normalizedError = normalizeError(
					error,
					`Service ${serviceName}.${methodName} failed`,
					createErrorContext(serviceName, methodName, { args })
				);

				if (logErrors) {
					errorLogger.error(
						`Service error: ${serviceName}.${methodName}`,
						normalizedError,
						createErrorContext(serviceName, methodName, { args }),
						serviceName
					);
				}

				throw normalizedError;
			}
		};

		if (retryable) {
			return await retryApiCall(executeMethod, { maxAttempts: maxRetries });
		} else {
			return await executeMethod();
		}
	}) as T;
}

// ============================================================================
// API ERROR INTEGRATION
// ============================================================================

/**
 * API route wrapper that adds comprehensive error handling
 */
export function withApiErrorHandling<T extends (...args: any[]) => Promise<any>>(
	apiHandler: T,
	routeName: string
): T {
	return (async (...args: any[]) => {
		const startTime = Date.now();
		const requestId = crypto.randomUUID();

		try {
			const result = await apiHandler(...args);

			// Log successful API calls in development
			if (process.env.NODE_ENV === 'development') {
				const duration = Date.now() - startTime;
				console.log(`[${requestId}] API ${routeName} - Success (${duration}ms)`);
			}

			return result;
		} catch (error) {
			const duration = Date.now() - startTime;
			const normalizedError = normalizeError(
				error,
				`API ${routeName} failed`,
				createErrorContext('API', routeName, { requestId, duration, args })
			);

			errorLogger.error(
				`API error: ${routeName}`,
				normalizedError,
				createErrorContext('API', routeName, { requestId, duration }),
				'API'
			);

			throw normalizedError;
		}
	}) as T;
}

// ============================================================================
// DATABASE ERROR INTEGRATION
// ============================================================================

/**
 * Database operation wrapper with error handling
 */
export function withDatabaseErrorHandling<T extends (...args: any[]) => Promise<any>>(
	dbOperation: T,
	operationName: string
): T {
	return (async (...args: any[]) => {
		try {
			return await dbOperation(...args);
		} catch (error) {
			const normalizedError = normalizeError(
				error,
				`Database operation ${operationName} failed`,
				createErrorContext('Database', operationName, { args })
			);

			errorLogger.error(
				`Database error: ${operationName}`,
				normalizedError,
				createErrorContext('Database', operationName),
				'Database'
			);

			// Convert database errors to appropriate app errors
			if (error instanceof Error) {
				if (error.message.includes('unique constraint')) {
					throw new ValidationError('Duplicate entry');
				}
				if (error.message.includes('foreign key constraint')) {
					throw new ValidationError('Invalid reference');
				}
				if (error.message.includes('not found')) {
					throw new NotFoundError('Record', 'unknown');
				}
			}

			throw new ServerError('Database operation failed');
		}
	}) as T;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create error-aware async function
 */
export function createErrorAwareAsync<T extends (...args: any[]) => Promise<any>>(
	asyncFn: T,
	context: string
): T {
	return (async (...args: any[]) => {
		try {
			return await asyncFn(...args);
		} catch (error) {
			const normalizedError = normalizeError(
				error,
				`Async operation failed in ${context}`,
				createErrorContext(context, 'async_operation')
			);

			errorLogger.error(
				`Async error in ${context}`,
				normalizedError,
				createErrorContext(context, 'async_operation'),
				context
			);

			throw normalizedError;
		}
	}) as T;
}

// ============================================================================
// INTEGRATION HELPERS
// ============================================================================

/**
 * Apply error handling to all methods of a service class
 */
export function enhanceServiceWithErrorHandling<T extends object>(
	service: T,
	serviceName: string
): T {
	const enhanced = { ...service };

	Object.getOwnPropertyNames(Object.getPrototypeOf(service)).forEach((methodName) => {
		if (methodName !== 'constructor' && typeof (service as any)[methodName] === 'function') {
			(enhanced as any)[methodName] = withServiceErrorHandling(
				(service as any)[methodName].bind(service),
				serviceName,
				methodName
			);
		}
	});

	return enhanced;
}

/**
 * Apply error handling to all methods of a repository class
 */
export function enhanceRepositoryWithErrorHandling<T extends object>(
	repository: T,
	repositoryName: string
): T {
	const enhanced = { ...repository };

	Object.getOwnPropertyNames(Object.getPrototypeOf(repository)).forEach((methodName) => {
		if (methodName !== 'constructor' && typeof (repository as any)[methodName] === 'function') {
			(enhanced as any)[methodName] = withDatabaseErrorHandling(
				(repository as any)[methodName].bind(repository),
				`${repositoryName}.${methodName}`
			);
		}
	});

	return enhanced;
}
