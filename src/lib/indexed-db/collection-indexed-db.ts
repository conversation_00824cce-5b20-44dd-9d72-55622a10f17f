import { CollectionWithDetail } from '@/models';
import { DB_NAME, DB_VERSION, IndexedDBManager } from './indexed-db';

const COLLECTIONS_STORE_NAME = 'collections';

// Instantiate the manager for our specific database and stores
const dbManager = new IndexedDBManager(DB_NAME, DB_VERSION, [
	{ storeName: COLLECTIONS_STORE_NAME, options: { keyPath: 'id' } },
]);

/**
 * Saves a single collection to the IndexedDB cache.
 * If a collection with the same ID exists, it will be updated.
 * @param collection The collection object to save.
 */
export async function saveCollection(collection: CollectionWithDetail): Promise<void> {
	return dbManager.put(COLLECTIONS_STORE_NAME, collection);
}

/**
 * Saves an array of collections to the IndexedDB cache.
 * Existing collections with matching IDs will be updated.
 * @param collections The array of collection objects to save.
 */
export async function saveCollections(collections: CollectionWithDetail[]): Promise<void> {
	const db = await dbManager.getDB(); // Access the internal openDB for a single transaction
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([COLLECTIONS_STORE_NAME], 'readwrite');
		const store = transaction.objectStore(COLLECTIONS_STORE_NAME);

		collections.forEach((collection) => {
			store.put(collection);
		});

		transaction.oncomplete = () => resolve();
		transaction.onerror = (event) => reject((event.target as IDBTransaction).error);
	});
}

/**
 * Retrieves a single collection from the IndexedDB cache by its ID.
 * @param id The ID of the collection to retrieve.
 * @returns A Promise that resolves with the collection object, or null if not found.
 */
export async function getCollection(id: string): Promise<CollectionWithDetail | null> {
	return dbManager.get<CollectionWithDetail>(COLLECTIONS_STORE_NAME, id);
}

/**
 * Retrieves all collections from the IndexedDB cache.
 * @returns A Promise that resolves with an array of all collection objects.
 */
export async function getAllCollections(): Promise<CollectionWithDetail[]> {
	return dbManager.getAll<CollectionWithDetail>(COLLECTIONS_STORE_NAME);
}

/**
 * Deletes a collection from the IndexedDB cache by its ID.
 * @param id The ID of the collection to delete.
 */
export async function deleteCollection(id: string): Promise<void> {
	return dbManager.delete(COLLECTIONS_STORE_NAME, id);
}

/**
 * Clears all collections from the IndexedDB cache.
 */
export async function clearCollections(): Promise<void> {
	return dbManager.clear(COLLECTIONS_STORE_NAME);
}
