import { useCallback, useState } from 'react';
import { AppError, ErrorCategory, isRetryableError, errorLogger } from './error-handling';

// ============================================================================
// RETRY CONFIGURATION
// ============================================================================

export interface RetryConfig {
	maxAttempts: number;
	baseDelay: number;
	maxDelay: number;
	backoffMultiplier: number;
	jitter: boolean;
	retryCondition?: (error: unknown) => boolean;
	onRetry?: (attempt: number, error: unknown) => void;
	onMaxAttemptsReached?: (error: unknown) => void;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
	maxAttempts: 3,
	baseDelay: 1000,
	maxDelay: 10000,
	backoffMultiplier: 2,
	jitter: true,
};

// ============================================================================
// RETRY UTILITIES
// ============================================================================

/**
 * Calculates the delay for the next retry attempt
 */
function calculateDelay(
	attempt: number,
	baseDelay: number,
	maxDelay: number,
	backoffMultiplier: number,
	jitter: boolean
): number {
	let delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1);
	delay = Math.min(delay, maxDelay);

	if (jitter) {
		// Add random jitter (±25%)
		const jitterAmount = delay * 0.25;
		delay += (Math.random() - 0.5) * 2 * jitterAmount;
	}

	return Math.max(delay, 0);
}

/**
 * Determines if an error should be retried
 */
function shouldRetry(error: unknown, retryCondition?: (error: unknown) => boolean): boolean {
	if (retryCondition) {
		return retryCondition(error);
	}

	// Default retry logic
	if (error instanceof AppError) {
		return isRetryableError(error);
	}

	// For network errors, timeouts, etc.
	if (error instanceof Error) {
		const message = error.message.toLowerCase();
		return (
			message.includes('network') ||
			message.includes('timeout') ||
			message.includes('fetch') ||
			message.includes('connection')
		);
	}

	return false;
}

/**
 * Executes a function with retry logic
 */
export async function withRetry<T>(
	fn: () => Promise<T>,
	config: Partial<RetryConfig> = {}
): Promise<T> {
	const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
	let lastError: unknown;

	for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
		try {
			const result = await fn();

			// Log successful retry if this wasn't the first attempt
			if (attempt > 1) {
				errorLogger.info(
					`Operation succeeded after ${attempt} attempts`,
					{ attempt, maxAttempts: finalConfig.maxAttempts },
					'RetryUtility'
				);
			}

			return result;
		} catch (error) {
			lastError = error;

			// Log the error
			errorLogger.warn(
				`Attempt ${attempt} failed`,
				{ attempt, maxAttempts: finalConfig.maxAttempts, error },
				'RetryUtility'
			);

			// Check if we should retry
			if (
				attempt === finalConfig.maxAttempts ||
				!shouldRetry(error, finalConfig.retryCondition)
			) {
				break;
			}

			// Call retry callback
			if (finalConfig.onRetry) {
				finalConfig.onRetry(attempt, error);
			}

			// Wait before next attempt
			const delay = calculateDelay(
				attempt,
				finalConfig.baseDelay,
				finalConfig.maxDelay,
				finalConfig.backoffMultiplier,
				finalConfig.jitter
			);

			await new Promise((resolve) => setTimeout(resolve, delay));
		}
	}

	// All attempts failed
	if (finalConfig.onMaxAttemptsReached) {
		finalConfig.onMaxAttemptsReached(lastError);
	}

	errorLogger.error(
		`All ${finalConfig.maxAttempts} retry attempts failed`,
		lastError instanceof Error ? lastError : new Error(String(lastError)),
		{ maxAttempts: finalConfig.maxAttempts },
		'RetryUtility'
	);

	throw lastError;
}

// ============================================================================
// RETRY HOOK
// ============================================================================

export interface UseRetryOptions extends Partial<RetryConfig> {
	enabled?: boolean;
}

export interface UseRetryReturn<T> {
	execute: () => Promise<T>;
	isRetrying: boolean;
	attempt: number;
	lastError: unknown;
	reset: () => void;
}

/**
 * React hook for retry functionality
 */
export function useRetry<T>(
	fn: () => Promise<T>,
	options: UseRetryOptions = {}
): UseRetryReturn<T> {
	const [isRetrying, setIsRetrying] = useState(false);
	const [attempt, setAttempt] = useState(0);
	const [lastError, setLastError] = useState<unknown>(null);

	const config = { ...DEFAULT_RETRY_CONFIG, ...options };

	const execute = useCallback(async (): Promise<T> => {
		if (!options.enabled) {
			return fn();
		}

		setIsRetrying(true);
		setLastError(null);

		try {
			const result = await withRetry(fn, {
				...config,
				onRetry: (attemptNum, error) => {
					setAttempt(attemptNum);
					setLastError(error);
					config.onRetry?.(attemptNum, error);
				},
			});

			setAttempt(0);
			return result;
		} catch (error) {
			setLastError(error);
			throw error;
		} finally {
			setIsRetrying(false);
		}
	}, [fn, config, options.enabled]);

	const reset = useCallback(() => {
		setIsRetrying(false);
		setAttempt(0);
		setLastError(null);
	}, []);

	return {
		execute,
		isRetrying,
		attempt,
		lastError,
		reset,
	};
}

// ============================================================================
// SPECIALIZED RETRY FUNCTIONS
// ============================================================================

/**
 * Retry configuration for API calls
 */
export const API_RETRY_CONFIG: RetryConfig = {
	maxAttempts: 3,
	baseDelay: 1000,
	maxDelay: 5000,
	backoffMultiplier: 2,
	jitter: true,
	retryCondition: (error) => {
		if (error instanceof AppError) {
			return (
				error.category === ErrorCategory.NETWORK ||
				error.category === ErrorCategory.SERVER ||
				(error.statusCode >= 500 && error.statusCode < 600) ||
				error.statusCode === 408 || // Timeout
				error.statusCode === 429 // Rate limit
			);
		}
		return false;
	},
};

/**
 * Retry configuration for critical operations
 */
export const CRITICAL_RETRY_CONFIG: RetryConfig = {
	maxAttempts: 5,
	baseDelay: 500,
	maxDelay: 8000,
	backoffMultiplier: 1.5,
	jitter: true,
};

/**
 * Retry configuration for background operations
 */
export const BACKGROUND_RETRY_CONFIG: RetryConfig = {
	maxAttempts: 10,
	baseDelay: 2000,
	maxDelay: 30000,
	backoffMultiplier: 2,
	jitter: true,
};

/**
 * Wrapper for API calls with retry logic
 */
export async function retryApiCall<T>(
	apiCall: () => Promise<T>,
	config?: Partial<RetryConfig>
): Promise<T> {
	return withRetry(apiCall, { ...API_RETRY_CONFIG, ...config });
}

/**
 * Wrapper for critical operations with retry logic
 */
export async function retryCriticalOperation<T>(
	operation: () => Promise<T>,
	config?: Partial<RetryConfig>
): Promise<T> {
	return withRetry(operation, { ...CRITICAL_RETRY_CONFIG, ...config });
}

/**
 * Wrapper for background operations with retry logic
 */
export async function retryBackgroundOperation<T>(
	operation: () => Promise<T>,
	config?: Partial<RetryConfig>
): Promise<T> {
	return withRetry(operation, { ...BACKGROUND_RETRY_CONFIG, ...config });
}
