import { UserRepository } from '@/backend/repositories';
import { UserWithDetail } from '@/models/user';
import { Prisma, Provider } from '@prisma/client';

export interface UserService {
	getUserById(userId: string): Promise<UserWithDetail | null>;
	getUserByProviderId(provider: Provider, providerId: string): Promise<UserWithDetail | null>;
	getUserByUsername(username: string): Promise<UserWithDetail | null>;
	createUser(data: {
		provider: Provider;
		provider_id: string;
		email?: string;
		name?: string;
	}): Promise<UserWithDetail>;
	createUserWithPassword(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
	}): Promise<UserWithDetail>;
}

export class UserServiceImpl implements UserService {
	constructor(private readonly getUserRepository: () => UserRepository) {}

	async getUserById(userId: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findById(userId);
		return user as UserWithDetail | null;
	}

	async getUserByProviderId(
		provider: Provider,
		providerId: string
	): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByProviderId(provider, providerId);
		return user as UserWithDetail | null;
	}

	async getUserByUsername(username: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByUsername(username);
		return user as UserWithDetail | null;
	}

	async createUser(data: {
		provider: Provider;
		provider_id: string;
		email?: string;
		name?: string;
	}): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			disabled: false,
		} as Prisma.UserCreateInput);
		return user as UserWithDetail;
	}

	async createUserWithPassword(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
	}): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			disabled: false,
		} as Prisma.UserCreateInput);
		return user as UserWithDetail;
	}
}
