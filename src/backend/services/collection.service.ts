import { CollectionRepository } from '@/backend/repositories';
import { createErrorContext, errorLogger, ServerError } from '@/lib/error-handling';
import { CollectionWithDetail } from '@/models';
import { Collection, Language, Prisma, Word } from '@prisma/client';
import { WordService } from '.';
type LLMService = import('./llm.service').LLMService;

export interface CollectionService {
	getUserCollections(userId: string): Promise<CollectionWithDetail[]>;
	createCollection(
		userId: string,
		name: string,
		target_language: Language,
		source_language: Language,
		wordIds?: string[]
	): Promise<CollectionWithDetail>;
	getCollectionById(userId: string, collectionId: string): Promise<CollectionWithDetail | null>;
	updateCollection(
		userId: string,
		collectionId: string,
		name?: string,
		wordIds?: string[]
	): Promise<CollectionWithDetail | null>;
	deleteCollection(userId: string, collectionId: string): Promise<boolean>;
	addWordsToCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null>;
	addTermsToCollection(
		userId: string,
		collectionId: string,
		terms: string[],
		language: Language
	): Promise<CollectionWithDetail | null>;
	removeWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null>;
}

export class CollectionServiceImpl implements CollectionService {
	constructor(
		private readonly getCollectionRepository: () => CollectionRepository,
		private readonly getLLMService: () => Promise<LLMService>,
		private readonly getWordService: () => WordService
	) {}

	private async _enrichCollection(
		// Collection type here now includes target_language and source_language
		collection: (Collection & { words?: Word[] | null }) | null
	): Promise<CollectionWithDetail | null> {
		if (!collection) return null;
		const words = collection.word_ids.length
			? await this.getWordService().findWordsByIds(collection.word_ids)
			: [];
		return {
			...collection, // Spreading collection includes the new language fields
			words,
		} as CollectionWithDetail; // Cast, assuming CollectionWithDetail interface is updated
	}

	private async _enrichCollections(
		// Collection type here now includes target_language and source_language
		collections: (Collection & { words?: Word[] | null })[]
	): Promise<CollectionWithDetail[]> {
		if (!collections || collections.length === 0) {
			return [];
		}
		const enriched = await Promise.all(collections.map((c) => this._enrichCollection(c)));
		return enriched.filter((c): c is CollectionWithDetail => c !== null);
	}

	async getUserCollections(userId: string): Promise<CollectionWithDetail[]> {
		try {
			const repoCollections =
				await this.getCollectionRepository().findUserCollections(userId);
			repoCollections.sort(
				(a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
			);
			const enrichedCollections = await this._enrichCollections(repoCollections);
			return enrichedCollections;
		} catch (error) {
			errorLogger.error(
				'Failed to get user collections',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('CollectionService', 'get_user_collections', { userId }),
				'CollectionService'
			);
			throw new ServerError('Failed to retrieve collections');
		}
	}

	async createCollection(
		userId: string,
		name: string,
		target_language: Language, // Accept new field
		source_language: Language, // Accept new field
		wordIds?: string[]
	): Promise<CollectionWithDetail> {
		const createInput: Prisma.CollectionCreateInput = {
			name,
			target_language, // Pass new field
			source_language, // Pass new field
			user: { connect: { id: userId } },
			...(wordIds && wordIds.length > 0 ? { word_ids: wordIds } : {}),
		};
		const repoCollection = await this.getCollectionRepository().create(createInput);
		const enrichedCollection = await this._enrichCollection(repoCollection);
		if (!enrichedCollection) {
			throw new Error('Failed to create collection');
		}
		return enrichedCollection;
	}

	async getCollectionById(
		userId: string,
		collectionId: string
	): Promise<CollectionWithDetail | null> {
		const repoCollection = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});
		const enrichedCollection = await this._enrichCollection(repoCollection);
		return enrichedCollection;
	}

	async updateCollection(
		userId: string,
		collectionId: string,
		name?: string,
		wordIds?: string[]
	): Promise<CollectionWithDetail | null> {
		const existingCollection = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});

		if (!existingCollection) {
			return null;
		}

		const updateData: Prisma.CollectionUpdateInput = {};
		if (name !== undefined) {
			// Check explicitly for undefined to allow empty string name
			updateData.name = name;
		}
		if (wordIds !== undefined) {
			// Check explicitly for undefined
			updateData.word_ids = wordIds;
		}

		if (Object.keys(updateData).length === 0) {
			const enriched = await this._enrichCollection(existingCollection);
			return enriched;
		}

		try {
			const updatedRepoCollection = await this.getCollectionRepository().update(
				collectionId,
				updateData
			);
			const enrichedCollection = await this._enrichCollection(updatedRepoCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async deleteCollection(userId: string, collectionId: string): Promise<boolean> {
		const collection = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});

		if (!collection) {
			return false;
		}
		await this.getCollectionRepository().delete({ id: collectionId });
		return true;
	}
	async addWordsToCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null> {
		const collectionExists = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});
		if (!collectionExists) {
			return null;
		}

		try {
			const currentWordIds: string[] = Array.isArray(collectionExists.word_ids)
				? collectionExists.word_ids
				: [];

			const mergedWordIds = Array.from(new Set([...currentWordIds, ...wordIds]));

			const updatedCollection = await this.getCollectionRepository().update(collectionId, {
				word_ids: mergedWordIds,
			});

			const enrichedCollection = await this._enrichCollection(updatedCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async addTermsToCollection(
		userId: string,
		collectionId: string,
		terms: string[],
		language: Language
	): Promise<CollectionWithDetail | null> {
		const collectionExists = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});
		if (!collectionExists) {
			return null;
		}

		const existingWordsModels = await this.getWordService().getWordsByTerms(terms, language);

		const termSet = new Set(terms);
		existingWordsModels.forEach((word) => {
			if (termSet.has(word.term)) {
				termSet.delete(word.term);
			}
		});
		const newTermsToProcess = Array.from(termSet);

		const allWordsForCollection: Word[] = [...existingWordsModels];

		if (newTermsToProcess.length > 0) {
			const llmService = await this.getLLMService();
			const generatedNewWords = await llmService.generateWordDetails(
				newTermsToProcess /*, language */, // LLM service generateWordDetails doesn't take language
				collectionExists.source_language,
				collectionExists.target_language
			);
			allWordsForCollection.push(...generatedNewWords);
		}

		if (allWordsForCollection.length === 0) {
			return null;
		}

		try {
			const currentWordIds: string[] = Array.isArray(collectionExists.word_ids)
				? collectionExists.word_ids
				: [];

			const newWordIds = allWordsForCollection.map((w) => w.id);
			const mergedWordIds = Array.from(new Set([...currentWordIds, ...newWordIds]));

			const updatedCollection = await this.getCollectionRepository().update(collectionId, {
				word_ids: mergedWordIds,
			});
			const enrichedCollection = await this._enrichCollection(updatedCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async removeWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null> {
		const collectionExists = await this.getCollectionRepository().findOne({
			id: collectionId,
			user_id: userId,
		});
		if (!collectionExists) {
			return null;
		}

		try {
			const updatedCollection =
				await this.getCollectionRepository().removeWordsFromCollection(
					collectionId,
					wordIds
				);
			const enrichedCollection = await this._enrichCollection(updatedCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}
}
