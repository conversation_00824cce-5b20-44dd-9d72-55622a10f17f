/**
 * Server-side only cache service wrapper
 * This file ensures that cache services are only used on the server side
 */

import { getCacheService } from '../cache-init.server';

// Import the interface type only (no runtime import)
type ICacheService = import('./cache-factory.service').ICacheService;

/**
 * Server-side cache service wrapper that throws an error if used on client side
 */
export class ServerCacheService {
	private static instance: ICacheService | null = null;
	private static initPromise: Promise<ICacheService> | null = null;

	/**
	 * Get the cache service instance (server-side only)
	 */
	static async getInstance(): Promise<ICacheService> {
		// Strict server-side check
		if (typeof window !== 'undefined') {
			throw new Error(
				'ServerCacheService can only be used on the server side. ' +
					'This error indicates that cache services are being imported or used in client-side code.'
			);
		}

		// Check if we're in a Node.js environment
		if (typeof process === 'undefined' || !process.env) {
			throw new Error(
				'ServerCacheService requires a Node.js environment with process.env available'
			);
		}

		if (this.instance) {
			return this.instance;
		}

		if (this.initPromise) {
			return this.initPromise;
		}

		this.initPromise = this.initializeCacheService();
		this.instance = await this.initPromise;
		this.initPromise = null;

		return this.instance;
	}

	private static async initializeCacheService(): Promise<ICacheService> {
		try {
			return await getCacheService();
		} catch (error) {
			console.error('Failed to initialize server cache service:', error);
			throw error;
		}
	}

	/**
	 * Reset the cache service instance (for testing purposes)
	 */
	static async reset(): Promise<void> {
		if (this.instance && 'close' in this.instance) {
			await (this.instance as any).close();
		}
		this.instance = null;
		this.initPromise = null;
	}

	/**
	 * Check if cache service is available (server-side only)
	 */
	static isAvailable(): boolean {
		return typeof window === 'undefined' && typeof process !== 'undefined' && !!process.env;
	}
}

/**
 * Convenience function to get server cache service
 */
export const getServerCacheService = async (): Promise<ICacheService> => {
	return ServerCacheService.getInstance();
};

/**
 * Type guard to check if we're on server side
 */
export const isServerSide = (): boolean => {
	return typeof window === 'undefined' && typeof process !== 'undefined';
};

/**
 * Decorator to ensure a function only runs on server side
 */
export function serverSideOnly<T extends (...args: any[]) => any>(fn: T): T {
	return ((...args: any[]) => {
		if (!isServerSide()) {
			throw new Error(
				`Function ${fn.name} can only be called on the server side. ` +
					'This error indicates that server-side code is being executed in a client environment.'
			);
		}
		return fn(...args);
	}) as T;
}
