import { Language } from '@prisma/client';

export interface PromptTemplate {
	original: string;
	optimized: string;
	tokenSavings?: number; // Estimated percentage savings
}

export interface PromptOptimizationResult {
	optimizedPrompt: string;
	estimatedTokens: number;
	tokenSavings: number;
	compressionRatio: number;
}

export class PromptOptimizerService {
	private static readonly TEMPLATES: Record<string, PromptTemplate> = {
		vocabulary: {
			original: `Generate {count} {target_lang} vocabulary terms for {source_lang} native speakers.
Keywords: {keywords}
Exclude: {excludes}
Format: lowercase (except proper nouns)
Types: mix nouns, verbs, adjectives
Level: appropriate for language learners

Return as JSON array of strings.`,
			optimized: `Gen {count} {target_lang} vocab for {source_lang}.
KW: {keywords}
Excl: {excludes}
Mix types, learner level. JSON array.`,
			tokenSavings: 45,
		},

		wordDetails: {
			original: `Create word details for: {terms}

For each term, provide:
- Definition in {target_lang}
- Translation to {source_lang}
- Example sentence in {target_lang}
- Pronunciation guide
- Part of speech
- Difficulty level (1-5)

Format as JSON array with objects containing: term, definition, translation, example, pronunciation, partOfSpeech, difficulty.`,
			optimized: `Word details for: {terms}
Each: def({target_lang}), trans({source_lang}), example, pronunciation, POS, difficulty(1-5).
JSON: [{term, definition, translation, example, pronunciation, partOfSpeech, difficulty}]`,
			tokenSavings: 50,
		},

		paragraph: {
			original: `Generate {count} paragraphs in {language} with difficulty level {difficulty}.

Requirements:
- Use keywords: {keywords}
- Each paragraph should have {sentenceCount} sentences
- Appropriate for language learners
- Natural and engaging content
- Include varied sentence structures

Return as JSON array of paragraph strings.`,
			optimized: `Gen {count} {language} paragraphs, diff:{difficulty}, {sentenceCount} sentences.
KW: {keywords}
Natural, varied structures. JSON array.`,
			tokenSavings: 40,
		},

		evaluation: {
			original: `Evaluate the following translation from {source_lang} to {target_lang}.

Original: {original}
Translation: {translation}

Provide detailed feedback on:
- Accuracy (1-10)
- Grammar (1-10)
- Naturalness (1-10)
- Overall score (1-10)
- Specific feedback and suggestions

Format as JSON with: accuracy, grammar, naturalness, overall, feedback.`,
			optimized: `Eval {source_lang}→{target_lang}:
Orig: {original}
Trans: {translation}
Rate accuracy, grammar, naturalness, overall (1-10) + feedback.
JSON: {accuracy, grammar, naturalness, overall, feedback}`,
			tokenSavings: 35,
		},

		questions: {
			original: `Generate {questionCount} comprehension questions for this paragraph:

{paragraph}

Requirements:
- Questions should be in {language}
- Answerable from paragraph content only
- Varied difficulty levels
- Clear and unambiguous wording
- Promote understanding of key concepts

Return as JSON array of question strings.`,
			optimized: `Gen {questionCount} {language} questions for paragraph:
{paragraph}
Answerable from text, varied difficulty. JSON array.`,
			tokenSavings: 42,
		},

		answerEvaluation: {
			original: `Evaluate answers to comprehension questions.

Paragraph: {paragraph}
Questions and Answers:
{questionsAndAnswers}

For each answer, provide:
- Correctness (1-10)
- Completeness (1-10)
- Clarity (1-10)
- Feedback and suggestions

Language for feedback: {feedback_language}

Format as JSON array with: question, answer, correctness, completeness, clarity, feedback.`,
			optimized: `Eval answers:
Para: {paragraph}
Q&A: {questionsAndAnswers}
Rate correctness, completeness, clarity (1-10) + feedback({feedback_language}).
JSON: [{question, answer, correctness, completeness, clarity, feedback}]`,
			tokenSavings: 38,
		},

		grammarPractice: {
			original: `Generate {count} grammar practice paragraphs in {language} with {errorTypes} error types.

Requirements:
- Use keywords: {keywords}
- Difficulty level: {difficulty}
- Include intentional errors for practice
- Natural content despite errors
- Varied error types and positions

Return as JSON array with objects containing: paragraph, errors (array of {position, type, correction}).`,
			optimized: `Gen {count} {language} grammar paragraphs, diff:{difficulty}.
KW: {keywords}, errors:{errorTypes}
JSON: [{paragraph, errors: [{position, type, correction}]}]`,
			tokenSavings: 43,
		},
	};

	/**
	 * Optimize a prompt using predefined templates
	 */
	static optimizePrompt(
		templateKey: string,
		params: Record<string, any>,
		useOptimized: boolean = true
	): PromptOptimizationResult {
		const template = this.TEMPLATES[templateKey];

		if (!template) {
			throw new Error(`Template not found: ${templateKey}`);
		}

		const promptToUse = useOptimized ? template.optimized : template.original;
		const optimizedPrompt = this.replaceParameters(promptToUse, params);
		const originalPrompt = this.replaceParameters(template.original, params);

		const estimatedTokens = this.estimateTokens(optimizedPrompt);
		const originalTokens = this.estimateTokens(originalPrompt);
		const tokenSavings = Math.round(
			((originalTokens - estimatedTokens) / originalTokens) * 100
		);
		const compressionRatio = estimatedTokens / originalTokens;

		return {
			optimizedPrompt,
			estimatedTokens,
			tokenSavings,
			compressionRatio,
		};
	}

	/**
	 * Replace parameters in template string
	 */
	private static replaceParameters(template: string, params: Record<string, any>): string {
		return template.replace(/\{(\w+)\}/g, (match, key) => {
			const value = params[key];
			if (value === undefined || value === null) {
				console.warn(`Parameter ${key} not found in params`);
				return match;
			}

			// Handle arrays by joining with commas
			if (Array.isArray(value)) {
				return value.join(', ');
			}

			return String(value);
		});
	}

	/**
	 * Estimate token count for text
	 * Improved estimation: 1 token ≈ 3.5 characters for Vietnamese/English mix
	 */
	static estimateTokens(text: string): number {
		if (!text) return 0;

		// More accurate estimation based on language characteristics
		const baseRatio = 3.5;

		// Adjust for different content types
		let adjustmentFactor = 1.0;

		// JSON structure adds overhead
		if (text.includes('{') || text.includes('[')) {
			adjustmentFactor *= 1.1;
		}

		// Technical terms are typically longer tokens
		if (text.match(/\b(definition|pronunciation|translation|accuracy|grammar)\b/i)) {
			adjustmentFactor *= 1.05;
		}

		return Math.ceil(text.length / (baseRatio * adjustmentFactor));
	}

	/**
	 * Get language name for prompts
	 */
	static getLanguageName(language: Language): string {
		const languageNames = {
			[Language.EN]: 'English',
			[Language.VI]: 'Vietnamese',
		};

		return languageNames[language] || language;
	}

	/**
	 * Validate that all required parameters are present
	 */
	static validateParameters(templateKey: string, params: Record<string, any>): string[] {
		const template = this.TEMPLATES[templateKey];
		if (!template) {
			return [`Template not found: ${templateKey}`];
		}

		const requiredParams = this.extractParameterNames(template.optimized);
		const missingParams = requiredParams.filter(
			(param) => params[param] === undefined || params[param] === null
		);

		return missingParams.map((param) => `Missing required parameter: ${param}`);
	}

	/**
	 * Extract parameter names from template
	 */
	private static extractParameterNames(template: string): string[] {
		const matches = template.match(/\{(\w+)\}/g);
		if (!matches) return [];

		return matches.map((match) => match.slice(1, -1)); // Remove { and }
	}

	/**
	 * Get estimated token savings for a template
	 */
	static getEstimatedSavings(templateKey: string): number {
		const template = this.TEMPLATES[templateKey];
		return template?.tokenSavings || 0;
	}

	/**
	 * Get all available template keys
	 */
	static getAvailableTemplates(): string[] {
		return Object.keys(this.TEMPLATES);
	}

	/**
	 * Create a custom optimized prompt (for cases not covered by templates)
	 */
	static createCustomOptimizedPrompt(originalPrompt: string): PromptOptimizationResult {
		// Basic optimization rules
		const optimized = originalPrompt
			// Remove redundant phrases
			.replace(/please\s+/gi, '')
			.replace(/\s+and\s+/g, ' & ')
			.replace(/\s+or\s+/g, ' | ')
			// Shorten common phrases
			.replace(/requirements?:/gi, 'Req:')
			.replace(/instructions?:/gi, 'Inst:')
			.replace(/example/gi, 'ex')
			.replace(/paragraph/gi, 'para')
			.replace(/sentence/gi, 'sent')
			.replace(/question/gi, 'q')
			.replace(/answer/gi, 'ans')
			// Remove extra whitespace
			.replace(/\s+/g, ' ')
			.trim();

		const originalTokens = this.estimateTokens(originalPrompt);
		const optimizedTokens = this.estimateTokens(optimized);
		const tokenSavings = Math.round(
			((originalTokens - optimizedTokens) / originalTokens) * 100
		);

		return {
			optimizedPrompt: optimized,
			estimatedTokens: optimizedTokens,
			tokenSavings,
			compressionRatio: optimizedTokens / originalTokens,
		};
	}
}
