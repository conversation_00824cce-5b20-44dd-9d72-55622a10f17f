import NodeCache from 'node-cache';

// Cache configuration
const DEFAULT_TTL = 60 * 60 * 24; // 24 hours in seconds

export interface CacheEntry<T = any> {
	value: T;
	timestamp: number;
	ttl: number;
	hits: number;
	tags?: string[];
}

export interface CacheStats {
	hits: number;
	misses: number;
	keys: number;
	hitRate: number;
	memoryUsage: number;
}

export interface OptimizedCacheOptions {
	ttl?: number;
	tags?: string[];
	priority?: 'high' | 'normal' | 'low';
	compress?: boolean;
}

export class CacheService {
	private cache: NodeCache;
	private stats: { hits: number; misses: number } = { hits: 0, misses: 0 };
	private tagIndex: Map<string, Set<string>> = new Map(); // tag -> set of keys

	constructor(ttlSeconds: number = DEFAULT_TTL) {
		this.cache = new NodeCache({
			stdTTL: ttlSeconds,
			checkperiod: ttlSeconds * 0.2,
			useClones: false,
		});
	}

	/**
	 * Get a value from cache with enhanced tracking
	 * @param key Cache key
	 * @returns Cached value or null if not found
	 */
	get<T>(key: string): T | null {
		const value = this.cache.get<T>(key);
		if (value !== undefined) {
			this.stats.hits++;
			return value;
		}
		this.stats.misses++;
		return null;
	}

	/**
	 * Get value with optimized options
	 */
	getOptimized<T>(key: string): T | null {
		return this.get<T>(key);
	}

	/**
	 * Set a value in cache with enhanced options
	 * @param key Cache key
	 * @param value Value to cache
	 * @param options Cache options including TTL and tags
	 * @returns true if successful
	 */
	set<T>(key: string, value: T, ttl: number = DEFAULT_TTL): boolean {
		return this.cache.set<T>(key, value, ttl);
	}

	/**
	 * Set value with optimized options
	 */
	setOptimized<T>(key: string, value: T, options: OptimizedCacheOptions = {}): boolean {
		const { ttl = DEFAULT_TTL, tags = [], priority = 'normal' } = options;

		// Store tags for invalidation
		if (tags.length > 0) {
			this.addToTagIndex(key, tags);
		}

		// Adjust TTL based on priority
		const adjustedTTL = this.adjustTTLByPriority(ttl, priority);

		return this.cache.set<T>(key, value, adjustedTTL);
	}

	/**
	 * Add key to tag index for tag-based invalidation
	 */
	private addToTagIndex(key: string, tags: string[]): void {
		for (const tag of tags) {
			if (!this.tagIndex.has(tag)) {
				this.tagIndex.set(tag, new Set());
			}
			this.tagIndex.get(tag)!.add(key);
		}
	}

	/**
	 * Adjust TTL based on priority
	 */
	private adjustTTLByPriority(baseTTL: number, priority: 'high' | 'normal' | 'low'): number {
		const multipliers = {
			high: 2.0, // High priority items live longer
			normal: 1.0, // Normal TTL
			low: 0.5, // Low priority items expire sooner
		};

		return Math.floor(baseTTL * multipliers[priority]);
	}

	/**
	 * Delete a value from cache
	 * @param key Cache key
	 * @returns true if successful
	 */
	del(key: string): boolean {
		return this.cache.del(key) > 0;
	}

	/**
	 * Clear all cache
	 */
	flush(): void {
		this.cache.flushAll();
	}

	/**
	 * Get enhanced cache stats
	 */
	getStats(): CacheStats {
		const nodeStats = this.cache.getStats();
		const totalRequests = this.stats.hits + this.stats.misses;

		return {
			hits: this.stats.hits,
			misses: this.stats.misses,
			keys: nodeStats.keys,
			hitRate: totalRequests > 0 ? this.stats.hits / totalRequests : 0,
			memoryUsage: nodeStats.vsize || 0,
		};
	}

	/**
	 * Invalidate cache entries by tag
	 */
	invalidateByTag(tag: string): number {
		const keys = this.tagIndex.get(tag);
		if (!keys) return 0;

		let deletedCount = 0;
		for (const key of keys) {
			if (this.del(key)) {
				deletedCount++;
			}
		}

		// Clean up tag index
		this.tagIndex.delete(tag);

		return deletedCount;
	}

	/**
	 * Get cache key for LLM operations
	 */
	generateLLMKey(operation: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				// Normalize arrays and objects for consistent keys
				let value = params[key];
				if (Array.isArray(value)) {
					value = value.sort().join(',');
				} else if (typeof value === 'object' && value !== null) {
					value = JSON.stringify(value);
				}
				result[key] = value;
				return result;
			}, {});

		return `llm:${operation}:${JSON.stringify(sortedParams)}`;
	}

	/**
	 * Get optimized TTL for different content types
	 */
	static getOptimizedTTL(contentType: string): number {
		const ttlMap: Record<string, number> = {
			vocabulary: 7 * 24 * 60 * 60, // 7 days - stable content
			wordDetails: 7 * 24 * 60 * 60, // 7 days - stable content
			paragraphs: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			questions: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			evaluations: 30 * 24 * 60 * 60, // 30 days - very stable
			grammarPractice: 1 * 24 * 60 * 60, // 1 day - more dynamic
			default: DEFAULT_TTL,
		};

		return ttlMap[contentType] || ttlMap.default;
	}

	/**
	 * Generate a cache key from parameters
	 * @param prefix Key prefix
	 * @param params Parameters to include in the key
	 * @returns Cache key
	 */
	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});

		return `${prefix}:${JSON.stringify(sortedParams)}`;
	}
}
