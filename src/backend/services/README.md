# Services Directory

This directory contains the business logic layer that orchestrates operations, implements domain rules, and coordinates between the API and repository layers.

## Structure

```
services/
├── index.ts                        # Service exports and utilities
├── auth.service.ts                 # Authentication and authorization
├── batch-processor.service.ts      # Batch processing and optimization
├── cache.service.ts                # Caching and performance optimization
├── collection.service.ts           # Collection business logic
├── collection-stats.service.ts     # Collection analytics and statistics
├── feedback.service.ts             # User feedback processing
├── keyword.service.ts              # Keyword and search functionality
├── last-seen-word.service.ts       # Spaced repetition and learning
├── llm.service.ts                  # AI/LLM integration and processing
├── model-selector.service.ts       # AI model selection and optimization
├── prompt-optimizer.service.ts     # AI prompt optimization
├── semantic-cache.service.ts       # Semantic caching for AI operations
├── token-monitor.service.ts        # Token usage monitoring and analytics
├── user.service.ts                 # User management and operations
├── word.service.ts                 # Vocabulary word operations
└── __tests__/                      # Service unit tests
```

## Service Architecture

### Business Logic Layer
- **Domain Logic**: Core business rules and domain-specific operations
- **Service Orchestration**: Coordination of multiple repository operations
- **Transaction Management**: Complex multi-entity transaction coordination
- **Validation**: Business rule validation and constraint enforcement
- **Error Handling**: Business logic error handling and transformation

### Service Categories

#### Core Domain Services
- **User Service**: User account management and profile operations
- **Collection Service**: Vocabulary collection management and organization
- **Word Service**: Vocabulary word operations and learning logic
- **Collection Stats Service**: Analytics and progress tracking

#### AI and Learning Services
- **LLM Service**: AI-powered content generation and processing
- **Model Selector Service**: AI model selection and optimization
- **Prompt Optimizer Service**: AI prompt engineering and optimization
- **Semantic Cache Service**: Intelligent caching for AI operations

#### System Services
- **Auth Service**: Authentication and authorization logic
- **Cache Service**: General caching and performance optimization
- **Batch Processor Service**: Bulk operations and background processing
- **Token Monitor Service**: API usage monitoring and analytics

#### Supporting Services
- **Keyword Service**: Search and discovery functionality
- **Last Seen Word Service**: Spaced repetition and learning algorithms
- **Feedback Service**: User feedback processing and management

## Key Features

### Business Logic Implementation
- **Domain Rules**: Implementation of complex business rules and constraints
- **Workflow Orchestration**: Multi-step business process coordination
- **Data Transformation**: Business logic data transformation and processing
- **Validation**: Business rule validation and constraint enforcement
- **State Management**: Entity state transitions and lifecycle management

### AI Integration
- **Content Generation**: AI-powered vocabulary and content creation
- **Model Management**: Dynamic AI model selection and optimization
- **Prompt Engineering**: Intelligent prompt construction and optimization
- **Semantic Caching**: Context-aware caching for AI operations
- **Cost Optimization**: AI usage cost monitoring and optimization

### Performance Optimization
- **Caching Strategies**: Multi-level caching for performance optimization
- **Batch Processing**: Efficient bulk operations and background processing
- **Query Optimization**: Service-level query optimization and coordination
- **Resource Management**: Efficient resource utilization and management
- **Monitoring**: Performance monitoring and bottleneck identification

### Learning Algorithms
- **Spaced Repetition**: Intelligent review scheduling algorithms
- **Progress Tracking**: Learning progress calculation and analytics
- **Difficulty Assessment**: Automatic content difficulty determination
- **Personalization**: User-specific learning optimization and customization
- **Analytics**: Learning analytics and insight generation

## Service Design Principles

### Clean Architecture
- **Single Responsibility**: Each service has a focused business purpose
- **Dependency Injection**: Services injected through wire.ts container
- **Layer Separation**: Services coordinate repositories, never access data directly
- **Interface Segregation**: Clean service interfaces and contracts
- **Dependency Inversion**: Abstract dependencies on concrete implementations

### Business Logic Organization
- **Domain-Driven Design**: Services reflect real-world business domains
- **Service Composition**: Complex operations through service composition
- **Transaction Coordination**: Multi-repository transaction management
- **Error Handling**: Business logic error handling and recovery
- **Validation**: Comprehensive business rule validation

### Performance and Scalability
- **Caching**: Intelligent caching strategies for performance optimization
- **Batch Operations**: Efficient bulk processing and background operations
- **Resource Optimization**: Efficient resource utilization and management
- **Monitoring**: Performance monitoring and optimization
- **Scalability**: Design for horizontal and vertical scaling

## Usage Patterns

### Service Implementation
```typescript
import { UserRepository, CollectionRepository } from '@/backend/repositories';
import { ValidationError, NotFoundError } from '@/backend/errors';

export class UserServiceImpl implements UserService {
  constructor(
    private userRepository: UserRepository,
    private collectionRepository: CollectionRepository
  ) {}

  async createUserWithDefaultCollection(userData: CreateUserData): Promise<User> {
    // Business logic validation
    await this.validateUserData(userData);

    // Multi-repository transaction
    const user = await this.userRepository.create(userData);
    const defaultCollection = await this.collectionRepository.create({
      name: 'My First Collection',
      userId: user.id,
      language: userData.preferredLanguage
    });

    return { ...user, collections: [defaultCollection] };
  }

  private async validateUserData(userData: CreateUserData): Promise<void> {
    if (await this.userRepository.findByEmail(userData.email)) {
      throw new ValidationError('Email already exists');
    }
  }
}
```

### Service Orchestration
```typescript
export class CollectionServiceImpl implements CollectionService {
  constructor(
    private collectionRepository: CollectionRepository,
    private wordService: WordService,
    private statsService: CollectionStatsService,
    private cacheService: CacheService
  ) {}

  async addWordsToCollection(collectionId: string, wordIds: string[]): Promise<Collection> {
    // Validate collection exists
    const collection = await this.collectionRepository.findById(collectionId);
    if (!collection) throw new NotFoundError('Collection', collectionId);

    // Validate words exist
    const words = await this.wordService.findByIds(wordIds);
    
    // Update collection
    const updatedCollection = await this.collectionRepository.addWords(collectionId, wordIds);
    
    // Update statistics
    await this.statsService.updateCollectionStats(collectionId, { wordsAdded: wordIds.length });
    
    // Invalidate cache
    await this.cacheService.invalidate(`collection:${collectionId}`);
    
    return updatedCollection;
  }
}
```

### AI Service Integration
```typescript
export class LLMServiceImpl implements LLMService {
  constructor(
    private modelSelector: ModelSelectorService,
    private promptOptimizer: PromptOptimizerService,
    private semanticCache: SemanticCacheService,
    private tokenMonitor: TokenMonitorService
  ) {}

  async generateVocabularyDefinition(word: string, language: Language): Promise<Definition> {
    // Check semantic cache first
    const cached = await this.semanticCache.get(`definition:${word}:${language}`);
    if (cached) return cached;

    // Select optimal model
    const model = await this.modelSelector.selectModel('definition-generation', language);
    
    // Optimize prompt
    const prompt = await this.promptOptimizer.optimizePrompt('definition', { word, language });
    
    // Generate content
    const definition = await this.generateContent(model, prompt);
    
    // Monitor token usage
    await this.tokenMonitor.recordUsage(model, prompt, definition);
    
    // Cache result
    await this.semanticCache.set(`definition:${word}:${language}`, definition);
    
    return definition;
  }
}
```

## Development Guidelines

### Service Implementation
- Implement service interfaces for all business domains
- Use dependency injection for repository and service dependencies
- Include comprehensive business logic validation
- Implement proper error handling and transformation
- Use transactions for complex multi-repository operations

### Business Logic Design
- Keep business logic in services, not in repositories or API handlers
- Implement domain rules as service methods
- Use service composition for complex operations
- Provide clear service interfaces and contracts
- Document complex business rules and algorithms

### Performance Optimization
- Implement appropriate caching strategies
- Use batch operations for bulk processing
- Monitor service performance and optimize bottlenecks
- Use efficient algorithms for business logic operations
- Consider async processing for long-running operations

### Testing Strategy
- Unit tests for all business logic methods
- Integration tests for service interactions
- Performance tests for optimization verification
- Business rule tests for domain logic validation
- Mock external dependencies appropriately
