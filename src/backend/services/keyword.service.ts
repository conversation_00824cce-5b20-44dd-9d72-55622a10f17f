import { KeywordRepository } from '@/backend/repositories';
import { KeywordWithDetail } from '@/models';

export interface KeywordService {
	getUserKeywords(userId: string): Promise<KeywordWithDetail[]>;
	createKeyword(userId: string, name: string): Promise<KeywordWithDetail>;
	getKeywordById(keywordId: string): Promise<KeywordWithDetail | null>;
	getKeywordByIds(keywordIds: string[]): Promise<KeywordWithDetail[]>;
	updateKeyword(keywordId: string, name: string): Promise<KeywordWithDetail | null>;
	deleteKeyword(keywordId: string): Promise<boolean>;
}

export class KeywordServiceImpl implements KeywordService {
	constructor(private readonly getKeywordRepository: () => KeywordRepository) {}

	async getUserKeywords(userId: string): Promise<KeywordWithDetail[]> {
		const keywords = await this.getKeywordRepository().findUserKeywords(userId);
		return keywords as KeywordWithDetail[];
	}

	async createKeyword(userId: string, name: string): Promise<KeywordWithDetail> {
		const keyword = await this.getKeywordRepository().create({
			content: name,
			user_id: userId,
		});
		return keyword as KeywordWithDetail;
	}

	async getKeywordById(keywordId: string): Promise<KeywordWithDetail | null> {
		const keyword = await this.getKeywordRepository().findById(keywordId);
		return keyword as KeywordWithDetail | null;
	}

	async getKeywordByIds(keywordIds: string[]): Promise<KeywordWithDetail[]> {
		const keywords = await this.getKeywordRepository().find({
			id: {
				in: keywordIds,
			},
		});
		return keywords as KeywordWithDetail[];
	}

	async updateKeyword(keywordId: string, name: string): Promise<KeywordWithDetail | null> {
		try {
			const keyword = await this.getKeywordRepository().update(keywordId, { content: name });
			return keyword as KeywordWithDetail | null;
		} catch (error: any) {
			// Assuming P2025 is Prisma's "Record Not Found" error code
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null; // Return null if keyword to update was not found
			}
			throw error; // Re-throw other errors
		}
	}

	async deleteKeyword(keywordId: string): Promise<boolean> {
		try {
			await this.getKeywordRepository().delete({ id: keywordId });
			return true;
		} catch (error) {
			return false;
		}
	}
}
