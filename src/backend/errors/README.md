# Errors Directory

This directory contains custom error classes and error handling utilities that provide structured error management across the backend application.

## Structure

```
errors/
├── index.ts                    # Error exports and utilities
└── application.errors.ts       # Custom application error classes
```

## Error Class Hierarchy

### Base Error Classes
- **ApplicationError**: Base class for all application-specific errors
- **HandlerNotFoundError**: Error for missing operation handlers
- **ValidationError**: Input validation and data validation errors
- **NotFoundError**: Resource not found errors
- **UnauthorizedError**: Authentication and authorization errors
- **DuplicateError**: Duplicate resource creation errors
- **ServerError**: Internal server and system errors

### Error Categories

#### Client Errors (4xx)
- **ValidationError**: Invalid input data or parameters
- **UnauthorizedError**: Missing or invalid authentication
- **NotFoundError**: Requested resource does not exist
- **DuplicateError**: Attempt to create duplicate resources

#### Server Errors (5xx)
- **ServerError**: Internal server errors and system failures
- **HandlerNotFoundError**: Missing implementation for operations
- **ApplicationError**: General application-level errors

## Key Features

### Structured Error Information
- **Error Codes**: Unique error codes for programmatic handling
- **Error Messages**: Human-readable error descriptions
- **Error Context**: Additional context and debugging information
- **Stack Traces**: Detailed stack traces for debugging
- **Error Categorization**: Logical grouping of related errors

### Error Handling Patterns
- **Error Propagation**: Consistent error bubbling through application layers
- **Error Transformation**: Converting system errors to application errors
- **Error Logging**: Structured error logging and monitoring
- **Error Recovery**: Graceful error recovery and fallback mechanisms
- **User-Friendly Messages**: Converting technical errors to user-friendly messages

### Integration Features
- **HTTP Status Mapping**: Automatic HTTP status code assignment
- **API Error Responses**: Standardized API error response formatting
- **Validation Integration**: Integration with Zod validation schemas
- **Logging Integration**: Integration with application logging systems
- **Monitoring Integration**: Error tracking and monitoring service integration

## Error Classes

### ApplicationError
- Base class for all custom application errors
- Provides error code and message standardization
- Enables error categorization and classification
- Supports error context and metadata attachment

### ValidationError
- Input validation failures and data validation errors
- Integration with Zod schema validation
- Field-specific validation error messages
- Support for multiple validation errors

### NotFoundError
- Resource not found errors with resource identification
- Standardized error messages for missing resources
- Support for resource type and identifier information
- Integration with repository layer for consistent handling

### UnauthorizedError
- Authentication and authorization failures
- JWT token validation errors
- Permission and access control violations
- Session expiration and invalid credentials

### ServerError
- Internal server errors and system failures
- Database connection and operation errors
- External service integration failures
- Unexpected application errors and exceptions

## Design Principles

### Error Design
- **Consistency**: Standardized error structure and behavior
- **Clarity**: Clear and descriptive error messages
- **Context**: Rich error context for debugging and monitoring
- **Categorization**: Logical error grouping and classification
- **Extensibility**: Easy to extend with new error types

### Error Handling Philosophy
- **Fail Fast**: Early error detection and handling
- **Graceful Degradation**: Fallback behavior for error scenarios
- **User Experience**: Minimize user disruption from errors
- **Developer Experience**: Rich error information for debugging
- **Monitoring**: Comprehensive error tracking and analytics

## Usage Patterns

### Error Creation and Throwing
```typescript
import { ValidationError, NotFoundError, UnauthorizedError } from '@/backend/errors';

// Validation error
throw new ValidationError('Invalid email format');

// Not found error
throw new NotFoundError('User', userId);

// Unauthorized error
throw new UnauthorizedError('Invalid authentication token');
```

### Error Handling in Services
```typescript
import { ApplicationError, ServerError } from '@/backend/errors';

try {
  const result = await databaseOperation();
  return result;
} catch (error) {
  if (error instanceof ApplicationError) {
    // Re-throw application errors
    throw error;
  } else {
    // Convert system errors to application errors
    throw new ServerError('Database operation failed');
  }
}
```

### Error Handling in API Layer
```typescript
import { ValidationError, NotFoundError, UnauthorizedError } from '@/backend/errors';

try {
  const result = await service.operation();
  return Response.json(result);
} catch (error) {
  if (error instanceof ValidationError) {
    return Response.json({ error: error.message }, { status: 400 });
  } else if (error instanceof NotFoundError) {
    return Response.json({ error: error.message }, { status: 404 });
  } else if (error instanceof UnauthorizedError) {
    return Response.json({ error: error.message }, { status: 401 });
  } else {
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Error Response Format

### Standardized API Error Response
```typescript
interface ApiErrorResponse {
  error: string;           // Human-readable error message
  code?: string;           // Error code for programmatic handling
  details?: any;           // Additional error details and context
  timestamp: string;       // Error occurrence timestamp
  path: string;           // API endpoint path
  method: string;         // HTTP method
}
```

### Error Context Information
```typescript
interface ErrorContext {
  operation: string;       // Operation that failed
  resource?: string;       // Resource type involved
  userId?: string;         // User context if available
  requestId?: string;      // Request tracking identifier
  metadata?: any;          // Additional context metadata
}
```

## Development Guidelines

### Error Class Implementation
- Extend ApplicationError for all custom error types
- Provide meaningful error codes and messages
- Include relevant context information
- Follow consistent naming conventions
- Document error scenarios and usage

### Error Handling Best Practices
- Use specific error types for different scenarios
- Provide clear and actionable error messages
- Include sufficient context for debugging
- Log errors appropriately for monitoring
- Consider user experience in error scenarios

### Error Testing
- Unit tests for error class behavior
- Integration tests for error handling flows
- Error scenario testing with edge cases
- Error message and code validation
- Error logging and monitoring verification
