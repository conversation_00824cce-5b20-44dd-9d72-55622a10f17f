export class ApplicationError extends Error {
	constructor(
		message: string,
		public readonly code: string
	) {
		super(message);
		this.name = 'ApplicationError';
	}
}

export class HandlerNotFoundError extends ApplicationError {
	constructor(type: string) {
		super(`No handler registered for type: ${type}`, 'HANDLER_NOT_FOUND');
		this.name = 'HandlerNotFoundError';
	}
}

export class ValidationError extends ApplicationError {
	constructor(message: string) {
		super(message, 'VALIDATION_ERROR');
		this.name = 'ValidationError';
	}
}

export class NotFoundError extends ApplicationError {
	constructor(resource: string, id: string) {
		super(`${resource} with id ${id} not found`, 'NOT_FOUND');
		this.name = 'NotFoundError';
	}
}

export class UnauthorizedError extends ApplicationError {
	constructor(message: string) {
		super(message, 'UNAUTHORIZED');
		this.name = 'UnauthorizedError';
	}
}

export class DuplicateError extends ApplicationError {
	constructor(message: string) {
		super(message, 'DUPLICATE_ERROR');
		this.name = 'DuplicateError';
	}
}

export class ServerError extends ApplicationError {
	constructor(
		message: string,
		public readonly statusCode: number = 500
	) {
		super(message, 'SERVER_ERROR');
		this.name = 'ServerError';
	}
}
