# Backend Directory

This directory contains the server-side business logic following Clean Architecture principles with clear separation between API, Service, and Repository layers.

## Structure

```
backend/
├── wire.ts                # Dependency injection container
├── api/                   # API endpoint handlers (8 files)
├── services/              # Business logic layer (10 services)
├── repositories/          # Data access layer (7 repositories)
├── middleware/            # Server middleware
├── schemas/               # MongoDB schemas and validation
├── database/              # Database configuration and utilities
├── errors/                # Custom error classes and handling
└── utils/                 # Backend utility functions
```

## Architecture Layers

### API Layer (`api/`)
- Request handling and routing logic
- Input validation using Zod schemas
- Authentication and authorization checks
- Response formatting and error handling
- Rate limiting and security middleware integration

### Service Layer (`services/`)
- Core business logic implementation
- Service orchestration and coordination
- AI/LLM integration for content generation
- Complex business rules and workflows
- Cross-cutting concerns like caching and optimization

### Repository Layer (`repositories/`)
- Data access abstraction
- MongoDB operations using Mongoose
- Query optimization and data transformation
- Database transaction management
- Data validation and integrity checks

### Dependency Injection (`wire.ts`)
- Centralized service wiring and configuration
- Dependency resolution and injection
- Service lifecycle management
- Configuration-based service instantiation

## Key Components

### Schemas (`schemas/`)
- MongoDB schema definitions using Mongoose
- Data validation rules and constraints
- Type definitions for database models
- Schema relationships and references

### Database (`database/`)
- MongoDB connection management
- Database configuration and setup
- Connection pooling and optimization
- Health checks and monitoring

### Middleware (`middleware/`)
- Authentication middleware
- Rate limiting and throttling
- Request/response logging
- Error handling middleware
- CORS and security headers

### Error Handling (`errors/`)
- Custom error classes hierarchy
- Error categorization and classification
- Error reporting and logging
- User-friendly error messages

## Design Principles

- **Clean Architecture**: Clear separation of concerns across layers
- **Dependency Injection**: Loose coupling through dependency inversion
- **Single Responsibility**: Each service has a focused purpose
- **Type Safety**: Comprehensive TypeScript typing throughout
- **Error Handling**: Robust error management and recovery
- **Security**: Authentication, authorization, and input validation
- **Performance**: Caching, optimization, and efficient data access

## Development Guidelines

- All services implement interfaces for testability
- Use dependency injection for service composition
- Follow repository pattern for data access
- Implement comprehensive error handling
- Use Zod schemas for runtime validation
- Maintain clear API contracts and documentation
