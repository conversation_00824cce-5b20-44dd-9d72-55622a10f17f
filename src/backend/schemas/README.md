# Schemas Directory

This directory contains MongoDB schema definitions, data models, and validation rules that define the structure and constraints for all database entities.

## Structure

```
schemas/
├── index.ts                    # Schema exports and utilities
├── types.ts                    # Common types and enums
├── user.schema.ts              # User entity schema and model
├── word.schema.ts              # Word entity schema and model
├── collection.schema.ts        # Collection entity schema and model
├── paragraph.schema.ts         # Paragraph entity schema and model
└── other.schemas.ts            # Additional entity schemas
```

## Schema Architecture

### MongoDB Schema System
- **Mongoose Integration**: Schema definitions using Mongoose ODM
- **Type Safety**: TypeScript interfaces for all schema definitions
- **Validation Rules**: Comprehensive data validation and constraints
- **Relationships**: Schema relationships and references between entities
- **Indexing**: Database index definitions for performance optimization

### Data Modeling
- **Entity Schemas**: Complete schema definitions for all domain entities
- **Embedded Documents**: Nested document structures for complex data
- **References**: Document references and population strategies
- **Validation**: Field-level and document-level validation rules
- **Middleware**: Schema middleware for data processing and hooks

### Schema Features
- **Timestamps**: Automatic creation and update timestamp management
- **Soft Deletes**: Soft deletion support with deletion timestamps
- **Versioning**: Document versioning and change tracking
- **Indexes**: Performance optimization through strategic indexing
- **Virtuals**: Computed properties and derived fields

## Core Schemas

### User Schema (`user.schema.ts`)
- **User Account**: Basic user account information and credentials
- **Authentication**: Provider-specific authentication data and tokens
- **Preferences**: User preferences, settings, and configuration
- **Profile**: User profile information and personal data
- **Activity**: User activity tracking and engagement metrics

### Collection Schema (`collection.schema.ts`)
- **Collection Metadata**: Collection name, description, and settings
- **Content Management**: Word and paragraph references and organization
- **Language Support**: Source and target language configuration
- **Sharing**: Collection sharing and collaboration features
- **Statistics**: Collection usage and performance metrics

### Word Schema (`word.schema.ts`)
- **Vocabulary Data**: Word text, pronunciation, and IPA information
- **Definitions**: Multiple definitions with parts of speech and explanations
- **Examples**: Usage examples and context sentences
- **Metadata**: Word difficulty, frequency, and categorization
- **Learning Data**: Spaced repetition and learning progress information

### Paragraph Schema (`paragraph.schema.ts`)
- **Content**: Paragraph text and reading material
- **Metadata**: Title, description, and content categorization
- **Difficulty**: Reading difficulty assessment and level classification
- **Language**: Source and target language information
- **Analytics**: Reading progress and comprehension tracking

### Additional Schemas (`other.schemas.ts`)
- **Keyword Schema**: Search keywords and content indexing
- **LastSeenWord Schema**: Spaced repetition and review tracking
- **Feedback Schema**: User feedback and support requests
- **CollectionStats Schema**: Daily progress and analytics data

## Schema Types and Enums (`types.ts`)

### Language Support
- **Language Enum**: Supported languages (EN, VI)
- **Language Pairs**: Source and target language combinations
- **Localization**: Language-specific formatting and validation

### Authentication
- **Provider Enum**: Authentication providers (Telegram, Google, Username/Password)
- **Provider Data**: Provider-specific authentication information
- **Token Management**: JWT tokens and refresh token handling

### Content Classification
- **PartsOfSpeech Enum**: Grammatical categories for word definitions
- **Difficulty Enum**: Content difficulty levels (Beginner, Intermediate, Advanced)
- **Length Enum**: Content length categories (Short, Medium, Long)

### Document Interfaces
- **BaseDocument**: Common document properties and timestamps
- **Entity Interfaces**: TypeScript interfaces for all schema entities
- **Validation Types**: Type definitions for validation and constraints

## Design Principles

### Schema Design
- **Consistency**: Consistent schema structure and naming conventions
- **Flexibility**: Flexible schema design for future extensibility
- **Performance**: Optimized schema design for query performance
- **Validation**: Comprehensive validation rules and data integrity
- **Relationships**: Clear and efficient relationship modeling

### Data Integrity
- **Validation Rules**: Field-level and document-level validation
- **Constraints**: Unique constraints and referential integrity
- **Indexes**: Strategic indexing for performance and uniqueness
- **Middleware**: Pre and post hooks for data processing
- **Error Handling**: Comprehensive error handling for validation failures

### Performance Optimization
- **Indexing Strategy**: Strategic index creation for query optimization
- **Query Optimization**: Schema design optimized for common queries
- **Data Denormalization**: Strategic denormalization for performance
- **Aggregation**: Efficient aggregation pipeline support
- **Caching**: Schema-level caching strategies and optimization

## Usage Patterns

### Schema Definition
```typescript
import { Schema, model, Document } from 'mongoose';
import { Language, Difficulty } from './types';

interface IUser extends Document {
  email: string;
  name: string;
  preferences: {
    language: Language;
    difficulty: Difficulty;
  };
  created_at: Date;
  updated_at: Date;
}

const UserSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  preferences: {
    language: { type: String, enum: Object.values(Language), default: Language.EN },
    difficulty: { type: String, enum: Object.values(Difficulty), default: Difficulty.BEGINNER }
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

export const UserModel = model<IUser>('User', UserSchema);
```

### Model Usage
```typescript
import { UserModel, CollectionModel } from '@/backend/schemas';

// Create new user
const user = new UserModel({
  email: '<EMAIL>',
  name: 'John Doe',
  preferences: {
    language: Language.EN,
    difficulty: Difficulty.BEGINNER
  }
});

await user.save();

// Query with population
const userWithCollections = await UserModel
  .findById(userId)
  .populate('collections')
  .exec();
```

### Validation Usage
```typescript
import { ValidationError } from '@/backend/errors';

try {
  const user = new UserModel(userData);
  await user.validate();
  await user.save();
} catch (error) {
  if (error.name === 'ValidationError') {
    throw new ValidationError(error.message);
  }
  throw error;
}
```

## Schema Configuration

### Index Configuration
- **Performance Indexes**: Indexes for frequently queried fields
- **Unique Indexes**: Unique constraints for data integrity
- **Compound Indexes**: Multi-field indexes for complex queries
- **Text Indexes**: Full-text search indexes for content search
- **Sparse Indexes**: Indexes for optional fields and null values

### Validation Configuration
- **Required Fields**: Mandatory field validation and constraints
- **Data Types**: Type validation and conversion rules
- **Custom Validators**: Custom validation functions and rules
- **Enum Validation**: Enumeration value validation and constraints
- **Range Validation**: Numeric and date range validation

## Development Guidelines

### Schema Implementation
- Use TypeScript interfaces for all schema definitions
- Implement comprehensive validation rules and constraints
- Use strategic indexing for performance optimization
- Include proper error handling for validation failures
- Document schema relationships and dependencies

### Performance Optimization
- Design schemas for efficient query patterns
- Use appropriate indexing strategies for performance
- Consider denormalization for frequently accessed data
- Implement efficient aggregation pipeline support
- Monitor schema performance and optimize as needed

### Data Integrity
- Implement proper validation rules and constraints
- Use referential integrity where appropriate
- Include data consistency checks and validation
- Implement proper error handling for data integrity violations
- Use transactions for complex multi-document operations
