import { Collection, Language, Prisma, PrismaClient } from '@prisma/client';
import { BaseRepository, BaseRepositoryImpl } from './base.repository';

export interface CollectionRepository extends BaseRepository<Collection> {
	findUserCollections(userId: string): Promise<Collection[]>;
	findByUserIdAndName(userId: string, name: string): Promise<Collection | null>;
	addWordsToCollection(collectionId: string, wordIds: string[]): Promise<Collection | null>;
	removeWordsFromCollection(collectionId: string, wordIds: string[]): Promise<Collection | null>;
}

export class CollectionRepositoryImpl
	extends BaseRepositoryImpl<Collection>
	implements CollectionRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.collection);
	}

	override async findById(id: string): Promise<Collection | null> {
		const collection = await this.prisma.collection.findUnique({
			where: { id },
		});
		// Prisma client will include the new fields after generate
		return collection as Collection | null;
	}

	override async findOne(query: Prisma.CollectionWhereInput): Promise<Collection | null> {
		const collection = await this.prisma.collection.findFirst({
			where: query,
		});
		// Prisma client will include the new fields after generate
		return collection as Collection | null;
	}

	override async find(query: Prisma.CollectionWhereInput): Promise<Collection[]> {
		const collections = await this.prisma.collection.findMany({
			where: query,
		});
		// Prisma client will include the new fields after generate
		return collections as Collection[];
	}

	override async create(data: Prisma.CollectionCreateInput): Promise<Collection> {
		// Data now includes target_language and source_language based on schema.prisma
		if (!data.name || !data.user) {
			throw new Error('name and user are required');
		}
		// Add validation for new fields if necessary, though Prisma handles enum validity
		if (
			!data.target_language ||
			!Object.values(Language).includes(data.target_language as Language)
		) {
			throw new Error('Valid target_language is required');
		}
		if (
			!data.source_language ||
			!Object.values(Language).includes(data.source_language as Language)
		) {
			throw new Error('Valid source_language is required');
		}

		const collection = await this.prisma.collection.create({
			data,
		});
		return collection;
	}

	override async update(id: string, data: Prisma.CollectionUpdateInput): Promise<Collection> {
		// Data now includes optional target_language and source_language based on schema.prisma
		// Add validation for new fields if provided
		if (
			data.target_language !== undefined &&
			!Object.values(Language).includes(data.target_language as Language)
		) {
			throw new Error('Valid target_language is required');
		}
		if (
			data.source_language !== undefined &&
			!Object.values(Language).includes(data.source_language as Language)
		) {
			throw new Error('Valid source_language is required');
		}
		const collection = await this.prisma.collection.update({
			where: { id },
			data,
		});
		// Prisma client will include the new fields after generate
		return collection;
	}

	override async delete(query: Prisma.CollectionWhereInput): Promise<void> {
		await this.prisma.collection.deleteMany({
			where: query,
		});
	}

	async findUserCollections(userId: string): Promise<Collection[]> {
		const collections = await this.prisma.collection.findMany({
			where: { user_id: userId },
		});
		// Prisma client will include the new fields after generate
		return collections as Collection[];
	}

	async findByUserIdAndName(userId: string, name: string): Promise<Collection | null> {
		const collection = await this.prisma.collection.findFirst({
			where: {
				user_id: userId,
				name,
			},
		});
		// Prisma client will include the new fields after generate
		return collection;
	}

	async addWordsToCollection(
		collectionId: string,
		wordIds: string[]
	): Promise<Collection | null> {
		// If wordIds is empty, no need to process
		if (wordIds.length === 0) return this.findById(collectionId);

		// Perform update directly with Prisma, avoiding separate find+update operations
		try {
			const collection = await this.prisma.collection.update({
				where: { id: collectionId },
				data: {
					// Use a raw query to add only unique values without fetching first
					word_ids: {
						push: wordIds,
					},
				},
			});

			// Ensure word_ids are unique (needed because push might create duplicates)
			if (collection.word_ids.length !== new Set(collection.word_ids).size) {
				return await this.prisma.collection.update({
					where: { id: collectionId },
					data: {
						word_ids: [...new Set(collection.word_ids)],
					},
				});
			}
			// Prisma client will include the new fields after generate
			return collection;
		} catch (error) {
			// Handle case where collection doesn't exist
			if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async removeWordsFromCollection(
		collectionId: string,
		wordIds: string[]
	): Promise<Collection | null> {
		// If wordIds is empty, no need to process
		if (wordIds.length === 0) return this.findById(collectionId);

		// Create a Set for faster lookups
		const wordIdsSet = new Set(wordIds);

		try {
			// Fetch and update in a single transaction for consistency
			return await this.prisma.$transaction(async (tx) => {
				const collection = await tx.collection.findUnique({
					where: { id: collectionId },
				});

				if (!collection) return null;

				// Filter out words to remove with optimized lookup
				const updatedWordIds = collection.word_ids.filter((id) => !wordIdsSet.has(id));

				// Only update if there's an actual change
				if (updatedWordIds.length !== collection.word_ids.length) {
					return tx.collection.update({
						where: { id: collectionId },
						data: { word_ids: updatedWordIds },
					});
				}
				// Prisma client will include the new fields after generate
				return collection;
			});
		} catch (error) {
			if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}
}
