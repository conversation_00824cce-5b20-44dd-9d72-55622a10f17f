import type { Keyword, PrismaClient } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';

export interface KeywordRepository extends BaseRepository<Keyword> {
	findUserKeywords(userId: string): Promise<Keyword[]>;
	search(query: string, userId?: string): Promise<Keyword[]>;
}

export class KeywordRepositoryImpl
	extends BaseRepositoryImpl<Keyword>
	implements KeywordRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.keyword);
	}

	async findUserKeywords(userId: string): Promise<Keyword[]> {
		return this.prisma.keyword.findMany({
			where: {
				user_id: userId,
			},
		});
	}

	async search(query: string, userId?: string): Promise<Keyword[]> {
		return this.prisma.keyword.findMany({
			where: {
				content: {
					contains: query,
					mode: 'insensitive',
				},
				...(userId ? { user_id: userId } : {}),
			},
		});
	}
}
