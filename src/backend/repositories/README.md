# Repositories Directory

This directory contains the data access layer that abstracts database operations and provides a clean interface between the service layer and data storage systems.

## Structure

```
repositories/
├── index.ts                        # Repository exports and utilities
├── base.repository.ts              # Base repository interface and contracts
├── base.mongo.repository.ts        # Base MongoDB repository implementation
├── collection.repository.ts        # Collection repository interface
├── collection.mongo.repository.ts  # Collection MongoDB implementation
├── collection-stats.repository.ts  # Collection statistics repository
├── feedback.repository.ts          # Feedback repository interface
├── keyword.repository.ts           # Keyword repository interface
├── last-seen-word.repository.ts    # Spaced repetition repository
├── other.mongo.repositories.ts     # Additional MongoDB repositories
├── paragraph.repository.ts         # Paragraph repository interface
├── user.repository.ts              # User repository interface
├── user.mongo.repository.ts        # User MongoDB implementation
├── word.repository.ts              # Word repository interface
└── word.mongo.repository.ts        # Word MongoDB implementation
```

## Repository Architecture

### Repository Pattern
- **Interface Segregation**: Clean interfaces for each domain entity
- **Implementation Abstraction**: Database-agnostic repository interfaces
- **Concrete Implementations**: Database-specific repository implementations
- **Dependency Injection**: Repository injection through wire.ts container
- **Base Repository**: Common functionality through base repository classes

### Data Access Layer
- **CRUD Operations**: Create, Read, Update, Delete operations for all entities
- **Query Abstraction**: Complex query operations abstracted from business logic
- **Transaction Support**: Database transaction management and coordination
- **Data Validation**: Repository-level data validation and integrity checks
- **Error Handling**: Database error handling and transformation

### Multi-Database Support
- **PostgreSQL Repositories**: Prisma-based repository implementations
- **MongoDB Repositories**: Mongoose-based repository implementations
- **Database Switching**: Dynamic repository selection based on configuration
- **Migration Support**: Data migration between different database systems
- **Consistency**: Consistent interface across different database implementations

## Repository Categories

### Core Entity Repositories

#### User Repository (`user.repository.ts`, `user.mongo.repository.ts`)
- User account CRUD operations
- Authentication and authorization data management
- User preferences and settings storage
- User activity tracking and analytics
- Profile management and updates

#### Collection Repository (`collection.repository.ts`, `collection.mongo.repository.ts`)
- Vocabulary collection CRUD operations
- Collection word and term management
- Collection sharing and collaboration features
- Collection organization and categorization
- Collection statistics and analytics

#### Word Repository (`word.repository.ts`, `word.mongo.repository.ts`)
- Vocabulary word CRUD operations
- Word definition and example management
- Word search and filtering capabilities
- Pronunciation and IPA data storage
- Word difficulty and categorization

#### Collection Stats Repository (`collection-stats.repository.ts`)
- Daily progress tracking and analytics
- Learning statistics and metrics calculation
- Performance insights and reporting data
- Progress visualization data storage
- User achievement and milestone tracking

### Supporting Repositories

#### Keyword Repository (`keyword.repository.ts`)
- Keyword CRUD operations for search functionality
- Content indexing and retrieval operations
- Keyword categorization and tagging
- Search optimization and ranking data
- Discovery and recommendation support

#### Last Seen Word Repository (`last-seen-word.repository.ts`)
- Spaced repetition tracking and management
- Review scheduling and optimization data
- Learning progress monitoring and analytics
- Retention analytics and insights
- Personalized learning algorithm support

#### Feedback Repository (`feedback.repository.ts`)
- User feedback submission and storage
- Bug reports and feature request management
- User experience feedback collection
- Feedback analytics and reporting
- Support ticket and issue tracking

#### Paragraph Repository (`paragraph.repository.ts`)
- Reading material CRUD operations
- Content difficulty assessment and storage
- Paragraph categorization and tagging
- Reading progress tracking data
- Comprehension and analysis features

## Base Repository System

### Base Repository Interface (`base.repository.ts`)
- **Generic CRUD Operations**: Common create, read, update, delete operations
- **Query Interface**: Standardized query and filtering interface
- **Pagination Support**: Consistent pagination across all repositories
- **Transaction Support**: Database transaction management interface
- **Error Handling**: Standardized error handling and transformation

### MongoDB Base Repository (`base.mongo.repository.ts`)
- **Mongoose Integration**: MongoDB operations using Mongoose ODM
- **Schema Validation**: MongoDB schema validation and data integrity
- **Connection Management**: MongoDB connection pooling and management
- **Query Optimization**: MongoDB-specific query optimization strategies
- **Index Management**: Database index creation and optimization

## Design Principles

### Repository Design
- **Single Responsibility**: Each repository manages one domain entity
- **Interface Segregation**: Clean interfaces with focused responsibilities
- **Dependency Inversion**: Abstract dependencies on concrete implementations
- **Open/Closed Principle**: Easy to extend with new repository implementations
- **Liskov Substitution**: Repository implementations are interchangeable

### Data Access Patterns
- **Repository Pattern**: Encapsulate data access logic and queries
- **Unit of Work**: Coordinate multiple repository operations in transactions
- **Specification Pattern**: Encapsulate complex query logic and criteria
- **Data Mapper**: Map between domain objects and database representations
- **Active Record**: Entity-specific data access methods and operations

### Performance Optimization
- **Query Optimization**: Database-specific query optimization strategies
- **Connection Pooling**: Efficient database connection management
- **Caching**: Repository-level caching for frequently accessed data
- **Lazy Loading**: On-demand data loading and relationship resolution
- **Batch Operations**: Efficient bulk data operations and updates

## Usage Patterns

### Repository Interface Usage
```typescript
import { UserRepository, CollectionRepository } from '@/backend/repositories';

// Repository injection through dependency injection
class UserService {
  constructor(
    private userRepository: UserRepository,
    private collectionRepository: CollectionRepository
  ) {}

  async getUserCollections(userId: string) {
    const user = await this.userRepository.findById(userId);
    const collections = await this.collectionRepository.findByUserId(userId);
    return { user, collections };
  }
}
```

### Repository Implementation
```typescript
import { BaseMongoRepository } from '@/backend/repositories';
import { UserModel, IUser } from '@/backend/schemas';

export class UserMongoRepositoryImpl extends BaseMongoRepository<IUser> implements UserRepository {
  constructor() {
    super(UserModel);
  }

  async findByEmail(email: string): Promise<IUser | null> {
    return await this.model.findOne({ email }).exec();
  }

  async updatePreferences(userId: string, preferences: any): Promise<IUser | null> {
    return await this.model.findByIdAndUpdate(
      userId,
      { preferences },
      { new: true }
    ).exec();
  }
}
```

### Transaction Usage
```typescript
import { startTransaction } from '@/backend/repositories';

async function createUserWithCollection(userData: any, collectionData: any) {
  const session = await startTransaction();
  
  try {
    const user = await userRepository.create(userData, { session });
    const collection = await collectionRepository.create({
      ...collectionData,
      userId: user.id
    }, { session });
    
    await session.commitTransaction();
    return { user, collection };
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
}
```

## Development Guidelines

### Repository Implementation
- Implement repository interfaces for all domain entities
- Use base repository classes for common functionality
- Follow database-specific best practices for implementations
- Include comprehensive error handling and logging
- Implement proper transaction support for complex operations

### Query Optimization
- Use database-specific query optimization techniques
- Implement proper indexing strategies for performance
- Use connection pooling for efficient resource management
- Monitor query performance and optimize slow operations
- Implement caching strategies for frequently accessed data

### Testing Strategy
- Unit tests for repository logic and data operations
- Integration tests for database interactions
- Performance tests for query optimization verification
- Transaction tests for data consistency verification
- Error handling tests for database failure scenarios
