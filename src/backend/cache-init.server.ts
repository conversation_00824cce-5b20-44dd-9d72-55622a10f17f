/**
 * Server-only cache initialization
 * This file should NEVER be imported on the client side
 */

// Strict server-side check at module level
if (typeof window !== 'undefined') {
	throw new Error(
		'cache-init.server.ts is being imported on the client side. ' +
			'This file contains Redis imports and should only be used on the server.'
	);
}

import { CacheService } from './services/cache.service';
import { RedisCacheService } from './services/redis-cache.service';

// Cache interface that both implementations must follow
export interface ICacheService {
	get<T>(key: string): Promise<T | null>;
	getOptimized<T>(key: string): Promise<T | null>;
	set<T>(key: string, value: T, ttl?: number): Promise<boolean>;
	setOptimized<T>(key: string, value: T, options?: any): Promise<boolean>;
	del(key: string): Promise<boolean>;
	flush(): Promise<void>;
	getStats(): Promise<any>;
	invalidateByTag(tag: string): Promise<number>;
	generateLLMKey(operation: string, params: Record<string, any>): string;
	generateKey(prefix: string, params: Record<string, any>): string;
}

// Wrapper to make node-cache async-compatible
class AsyncCacheServiceWrapper implements ICacheService {
	constructor(private cacheService: CacheService) {}

	async get<T>(key: string): Promise<T | null> {
		return this.cacheService.get<T>(key);
	}

	async getOptimized<T>(key: string): Promise<T | null> {
		return this.cacheService.getOptimized<T>(key);
	}

	async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
		return this.cacheService.set<T>(key, value, ttl);
	}

	async setOptimized<T>(key: string, value: T, options?: any): Promise<boolean> {
		return this.cacheService.setOptimized<T>(key, value, options);
	}

	async del(key: string): Promise<boolean> {
		return this.cacheService.del(key);
	}

	async flush(): Promise<void> {
		return this.cacheService.flush();
	}

	async getStats(): Promise<any> {
		return this.cacheService.getStats();
	}

	async invalidateByTag(tag: string): Promise<number> {
		return this.cacheService.invalidateByTag(tag);
	}

	generateLLMKey(operation: string, params: Record<string, any>): string {
		return this.cacheService.generateLLMKey(operation, params);
	}

	generateKey(prefix: string, params: Record<string, any>): string {
		return this.cacheService.generateKey(prefix, params);
	}
}

let cacheServiceInstance: ICacheService | null = null;

/**
 * Initialize cache service (server-side only)
 */
export async function initializeCacheService(): Promise<ICacheService> {
	if (cacheServiceInstance) {
		return cacheServiceInstance;
	}

	const useRedis =
		process.env.CACHE_PROVIDER === 'redis' || process.env.NODE_ENV === 'production';

	if (useRedis) {
		try {
			console.log('Initializing Redis cache service...');
			const redisCacheService = new RedisCacheService();
			// Test Redis connection
			await redisCacheService.get('test-connection');
			cacheServiceInstance = redisCacheService;
			console.log('Redis cache service initialized successfully');
		} catch (error) {
			console.warn('Failed to initialize Redis cache, falling back to node-cache:', error);
			cacheServiceInstance = new AsyncCacheServiceWrapper(new CacheService());
		}
	} else {
		console.log('Using node-cache service');
		cacheServiceInstance = new AsyncCacheServiceWrapper(new CacheService());
	}

	return cacheServiceInstance!;
}

/**
 * Get cache service instance (server-side only)
 */
export async function getCacheService(): Promise<ICacheService> {
	return initializeCacheService();
}

/**
 * Reset cache service (for testing)
 */
export async function resetCacheService(): Promise<void> {
	if (cacheServiceInstance && 'close' in cacheServiceInstance) {
		await (cacheServiceInstance as any).close();
	}
	cacheServiceInstance = null;
}
