# Types Directory

This directory contains TypeScript type definitions, interfaces, and type utilities that provide comprehensive type safety across the application.

## Structure

```
types/
├── index.ts                # Main type exports and utilities
└── floating-ui.ts          # Floating UI specific type definitions
```

## Type Categories

### Core Types (`index.ts`)
- Application-wide type definitions and interfaces
- Common data structures and entity types
- API request and response type definitions
- Utility types and type helpers
- Shared enums and constants

### Floating UI Types (`floating-ui.ts`)
- Floating UI component type definitions
- Positioning and layout type interfaces
- Event handling types for floating elements
- Configuration types for floating UI behavior
- Animation and transition type definitions

## Key Features

### Type Safety
- **Comprehensive Coverage**: Types for all major application entities
- **API Contracts**: Strict typing for API requests and responses
- **Component Props**: Type-safe component interfaces and props
- **State Management**: Typed state objects and action definitions
- **Error Handling**: Typed error objects and error handling patterns

### Type Utilities
- **Generic Types**: Reusable generic type definitions
- **Utility Types**: Helper types for common patterns
- **Type Guards**: Runtime type checking utilities
- **Conditional Types**: Advanced TypeScript type manipulation
- **Mapped Types**: Dynamic type generation and transformation

### Domain Types
- **User Types**: User entity and authentication types
- **Collection Types**: Vocabulary collection and learning types
- **Word Types**: Vocabulary word and definition types
- **UI Types**: User interface and component types
- **API Types**: Backend service and endpoint types

## Design Principles

### Type Design
- **Strict Typing**: Comprehensive type coverage with strict TypeScript settings
- **Composability**: Types that can be composed and extended
- **Reusability**: Generic types that work across different contexts
- **Clarity**: Clear and descriptive type names and structures
- **Maintainability**: Easy to update and extend type definitions

### Type Organization
- **Logical Grouping**: Related types grouped together
- **Hierarchical Structure**: Base types extended by specific implementations
- **Namespace Management**: Proper use of TypeScript namespaces and modules
- **Export Strategy**: Clear and consistent type export patterns
- **Documentation**: Well-documented complex types and interfaces

## Usage Patterns

### Basic Type Usage
```typescript
import { User, Collection, Word, ApiResponse } from '@/types';

// Type-safe function parameters
function processUser(user: User): void {
  // Function implementation
}

// Type-safe API responses
const response: ApiResponse<Collection[]> = await fetchCollections();
```

### Generic Type Usage
```typescript
import { PaginatedResponse, FilterOptions } from '@/types';

// Generic pagination
const paginatedUsers: PaginatedResponse<User> = await fetchUsers();

// Generic filtering
const filters: FilterOptions<Collection> = {
  language: 'EN',
  difficulty: 'BEGINNER',
};
```

### Floating UI Types
```typescript
import { FloatingPosition, FloatingConfig } from '@/types/floating-ui';

// Floating UI configuration
const config: FloatingConfig = {
  placement: 'bottom-start',
  offset: 10,
  flip: true,
};

// Position calculation
const position: FloatingPosition = calculatePosition(config);
```

## Type Categories

### Entity Types
- **User**: User account, profile, and authentication information
- **Collection**: Vocabulary collections and learning materials
- **Word**: Vocabulary words, definitions, and learning data
- **Paragraph**: Reading materials and comprehension content
- **Keyword**: Search and discovery functionality types

### API Types
- **Request Types**: API request payload and parameter types
- **Response Types**: API response structure and data types
- **Error Types**: API error response and error handling types
- **Pagination Types**: Pagination and filtering type definitions
- **Authentication Types**: JWT tokens and authentication flow types

### UI Types
- **Component Props**: React component property interfaces
- **Event Types**: UI event handling and interaction types
- **State Types**: Application and component state definitions
- **Theme Types**: Styling and theme configuration types
- **Animation Types**: Animation and transition configuration types

### Utility Types
- **Generic Helpers**: Reusable generic type utilities
- **Conditional Types**: Advanced TypeScript type manipulation
- **Mapped Types**: Dynamic type generation and transformation
- **Type Guards**: Runtime type checking and validation
- **Branded Types**: Type safety for primitive values

## Development Guidelines

### Type Definition
- Use descriptive and consistent naming conventions
- Prefer interfaces over types for object shapes
- Use union types for controlled value sets
- Implement proper type inheritance and composition
- Include JSDoc comments for complex types

### Type Safety
- Enable strict TypeScript compiler options
- Use type assertions sparingly and safely
- Implement runtime type validation where needed
- Avoid `any` type and use `unknown` instead
- Use type guards for runtime type checking

### Type Organization
- Group related types in logical modules
- Use consistent export patterns
- Avoid circular type dependencies
- Keep types close to their usage when possible
- Maintain clear type hierarchies

### Performance Considerations
- Avoid overly complex type computations
- Use type aliases for frequently used complex types
- Consider compilation performance for large type definitions
- Profile TypeScript compilation times
- Optimize type checking for development experience
