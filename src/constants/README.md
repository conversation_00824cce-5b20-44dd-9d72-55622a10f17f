# Constants Directory

This directory contains application-wide constants, enums, and static configuration values used throughout the application.

## Structure

```
constants/
├── index.ts               # Main constants exports
├── loading-keys.ts        # Loading state identifiers
└── loading-scopes.ts      # Loading scope definitions
```

## Constant Categories

### Main Constants (`index.ts`)
- Application-wide constant values
- API endpoints and route definitions
- Default values and fallbacks
- Configuration constants
- Business logic constants

### Loading Management (`loading-keys.ts`, `loading-scopes.ts`)
- Loading state identifiers for different operations
- Scope definitions for loading state management
- Loading key categorization and organization
- Loading state hierarchy and relationships

## Key Features

### Loading State Management
- **Loading Keys**: Unique identifiers for different loading operations
- **Loading Scopes**: Hierarchical organization of loading states
- **State Isolation**: Separate loading states for different features
- **Performance Optimization**: Granular loading state control

### Application Constants
- **Route Definitions**: Static route paths and navigation constants
- **API Endpoints**: Backend API endpoint definitions
- **Default Values**: Fallback values for various operations
- **Configuration**: Static configuration values and settings

### Business Logic Constants
- **Validation Rules**: Input validation constants and limits
- **Display Settings**: UI-related constants and preferences
- **Feature Flags**: Static feature toggle definitions
- **Timing Constants**: Timeout, delay, and interval values

## Design Principles

### Constant Management
- **Centralization**: All constants in dedicated files
- **Type Safety**: TypeScript const assertions and enums
- **Immutability**: Read-only constant definitions
- **Organization**: Logical grouping and categorization
- **Documentation**: Clear naming and purpose documentation

### Loading State Architecture
- **Granular Control**: Fine-grained loading state management
- **Hierarchical Organization**: Nested loading scopes
- **Performance**: Efficient loading state updates
- **User Experience**: Smooth loading transitions

## Usage Patterns

### Constant Access
```typescript
import { ROUTES, API_ENDPOINTS, DEFAULT_VALUES } from '@/constants';

// Route navigation
const dashboardPath = ROUTES.DASHBOARD;

// API calls
const apiUrl = API_ENDPOINTS.COLLECTIONS;

// Default values
const defaultLanguage = DEFAULT_VALUES.LANGUAGE;
```

### Loading State Management
```typescript
import { LOADING_KEYS, LOADING_SCOPES } from '@/constants';

// Loading key usage
const loadingKey = LOADING_KEYS.FETCH_COLLECTIONS;

// Loading scope usage
const scope = LOADING_SCOPES.COLLECTIONS;
```

## Development Guidelines

### Constant Definition
- Use UPPER_SNAKE_CASE for constant names
- Group related constants together
- Provide TypeScript type definitions
- Use const assertions for immutability
- Document complex constant relationships

### Loading State Constants
- Define unique loading keys for each operation
- Organize loading keys by feature or scope
- Use hierarchical naming for related operations
- Provide clear descriptions for loading states
- Maintain consistency across loading patterns

### Best Practices
- Avoid magic numbers and strings in code
- Use constants for repeated values
- Keep constants close to their usage context
- Validate constant values at compile time
- Use enums for related constant groups
