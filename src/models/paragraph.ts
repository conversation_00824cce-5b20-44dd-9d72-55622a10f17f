import { Difficulty, Language, Length, Paragraph } from '@prisma/client';

// Request for generating a new paragraph
export interface ParagraphGenerationRequest {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	length: Length;
}

/**
 * Enrich model for Paragraph (bao gồm các tr<PERSON>ờng enrich như exercises, keywords, owner, ...)
 */
export type ParagraphWithDetail = Paragraph & {
	// Add enrich fields here if needed, e.g. exercises, keywords, owner, etc.
};
