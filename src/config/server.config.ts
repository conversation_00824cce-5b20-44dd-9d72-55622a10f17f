'use server';

const env = process.env;

// ============================================================================
// TYPES
// ============================================================================

export interface ServerConfig {
	port: number;
	env: string;
}

export interface AuthConfig {
	jwtCookieName: string;
	jwtSecret: string;
	jwtExpiresIn: number;
	google: {
		clientId: string;
		clientSecret: string;
	};
}

export interface RedisConfig {
	host: string;
	port: number;
	password: string;
	db: number;
	retryDelayOnFailover: number;
	maxRetriesPerRequest: number;
	lazyConnect: boolean;
	keepAlive: number;
	connectTimeout: number;
	commandTimeout: number;
	keyPrefix: string;
}

export interface LLMConfig {
	openAIKey: string;
	openAIModel: string;
	maxExamples: number;
	temperature: number;
	maxTokens: number;
}

export interface LLMOptimizationConfig {
	enabled: boolean;
	promptOptimization: {
		enabled: boolean;
		compressionTarget: number;
	};
	caching: {
		enabled: boolean;
		ttl: {
			vocabulary: number;
			wordDetails: number;
			paragraphs: number;
			questions: number;
			evaluations: number;
			grammarPractice: number;
		};
		semanticSimilarity: {
			enabled: boolean;
			threshold: number;
			maxKeywords: number;
			keywordWeight: number;
			structuralWeight: number;
			semanticWeight: number;
		};
	};
	batchProcessing: {
		enabled: boolean;
		maxBatchSize: {
			generateWordDetails: number;
			evaluateAnswers: number;
			evaluateTranslation: number;
			generateQuestions: number;
			generateParagraph: number;
			generateGrammarPractice: number;
		};
		maxWaitTime: number;
		maxTokensPerBatch: number;
	};
	modelSelection: {
		enabled: boolean;
		costOptimization: boolean;
		qualityThreshold: number;
		latencyThreshold: number;
		adaptiveLearning: boolean;
	};
	tokenManagement: {
		budgetLimits: {
			daily: number;
			monthly: number;
		};
		costAlerts: {
			dailyThreshold: number;
			monthlyThreshold: number;
		};
		estimation: {
			enabled: boolean;
			trackActualUsage: boolean;
		};
	};
	monitoring: {
		enabled: boolean;
		logLevel: string;
		metricsCollection: boolean;
	};
}

// ============================================================================
// SERVER CONFIGURATION FUNCTIONS
// ============================================================================

/**
 * Get server configuration
 * Contains server-specific settings like port and environment
 */
export async function getServerConfig(): Promise<ServerConfig> {
	return {
		port: Number.parseInt(env.PORT || '5000', 10),
		env: env.NODE_ENV || 'development',
	};
}

/**
 * Get authentication configuration
 * Contains sensitive authentication settings including secrets
 */
export async function getAuthConfig(): Promise<AuthConfig> {
	return {
		jwtCookieName: env.JWT_COOKIE_NAME || 'auth_token',
		jwtSecret: env.JWT_SECRET || 'your-secret-key',
		jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 30 * 24 * 60 * 60,
		google: {
			clientId: env.GOOGLE_CLIENT_ID || '',
			clientSecret: env.GOOGLE_CLIENT_SECRET || '',
		},
	};
}

/**
 * Get Redis configuration
 * Contains all Redis connection and caching settings
 */
export async function getRedisConfig(): Promise<RedisConfig> {
	// Ensure this only runs on server side
	if (typeof window !== 'undefined') {
		throw new Error('Redis configuration can only be accessed on the server side');
	}
	
	return {
		host: env.REDIS_HOST || 'localhost',
		port: Number.parseInt(env.REDIS_PORT || '6380', 10),
		password: env.REDIS_PASSWORD || 'redis123',
		db: Number.parseInt(env.REDIS_DB || '0', 10),
		retryDelayOnFailover: 100,
		maxRetriesPerRequest: 3,
		lazyConnect: true,
		keepAlive: 30000,
		connectTimeout: 10000,
		commandTimeout: 5000,
		keyPrefix: env.REDIS_KEY_PREFIX || 'vocab:',
	};
}

/**
 * Get LLM configuration
 * Contains OpenAI API settings and model parameters
 */
export async function getLLMConfig(): Promise<LLMConfig> {
	return {
		openAIKey: env.LLM_OPENAI_API_KEY || '',
		openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
		maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
		temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
		maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
	};
}

/**
 * Get LLM optimization configuration
 * Contains advanced LLM optimization and monitoring settings
 */
export async function getLLMOptimizationConfig(): Promise<LLMOptimizationConfig> {
	return {
		enabled: env.LLM_OPTIMIZATION_ENABLED === 'true',
		promptOptimization: {
			enabled: env.LLM_PROMPT_OPTIMIZATION_ENABLED !== 'false', // Default true
			compressionTarget: Number.parseFloat(env.LLM_COMPRESSION_TARGET || '0.4'), // 40% reduction target
		},
		caching: {
			enabled: env.LLM_CACHING_ENABLED !== 'false', // Default true
			ttl: {
				vocabulary: Number.parseInt(env.LLM_CACHE_TTL_VOCABULARY || '604800'), // 7 days
				wordDetails: Number.parseInt(env.LLM_CACHE_TTL_WORD_DETAILS || '604800'), // 7 days
				paragraphs: Number.parseInt(env.LLM_CACHE_TTL_PARAGRAPHS || '259200'), // 3 days
				questions: Number.parseInt(env.LLM_CACHE_TTL_QUESTIONS || '259200'), // 3 days
				evaluations: Number.parseInt(env.LLM_CACHE_TTL_EVALUATIONS || '2592000'), // 30 days
				grammarPractice: Number.parseInt(env.LLM_CACHE_TTL_GRAMMAR || '86400'), // 1 day
			},
			semanticSimilarity: {
				enabled: env.LLM_SEMANTIC_CACHE_ENABLED === 'true',
				threshold: Number.parseFloat(env.LLM_SEMANTIC_CACHE_THRESHOLD || '0.8'),
				maxKeywords: Number.parseInt(env.LLM_SEMANTIC_MAX_KEYWORDS || '20'),
				keywordWeight: Number.parseFloat(env.LLM_SEMANTIC_KEYWORD_WEIGHT || '0.4'),
				structuralWeight: Number.parseFloat(env.LLM_SEMANTIC_STRUCTURAL_WEIGHT || '0.3'),
				semanticWeight: Number.parseFloat(env.LLM_SEMANTIC_SEMANTIC_WEIGHT || '0.3'),
			},
		},
		batchProcessing: {
			enabled: env.LLM_BATCH_PROCESSING_ENABLED !== 'false', // Default true
			maxBatchSize: {
				generateWordDetails: Number.parseInt(env.LLM_BATCH_SIZE_WORD_DETAILS || '20'),
				evaluateAnswers: Number.parseInt(env.LLM_BATCH_SIZE_EVALUATE_ANSWERS || '10'),
				evaluateTranslation: Number.parseInt(
					env.LLM_BATCH_SIZE_EVALUATE_TRANSLATION || '15'
				),
				generateQuestions: Number.parseInt(env.LLM_BATCH_SIZE_QUESTIONS || '5'),
				generateParagraph: Number.parseInt(env.LLM_BATCH_SIZE_PARAGRAPHS || '3'),
				generateGrammarPractice: Number.parseInt(env.LLM_BATCH_SIZE_GRAMMAR || '2'),
			},
			maxWaitTime: Number.parseInt(env.LLM_BATCH_MAX_WAIT_TIME || '2000'), // milliseconds
			maxTokensPerBatch: Number.parseInt(env.LLM_BATCH_MAX_TOKENS || '8000'),
		},
		modelSelection: {
			enabled: env.LLM_MODEL_SELECTION_ENABLED !== 'false', // Default true
			costOptimization: env.LLM_COST_OPTIMIZATION_ENABLED === 'true',
			qualityThreshold: Number.parseFloat(env.LLM_QUALITY_THRESHOLD || '0.8'),
			latencyThreshold: Number.parseInt(env.LLM_LATENCY_THRESHOLD || '5000'), // milliseconds
			adaptiveLearning: env.LLM_ADAPTIVE_LEARNING_ENABLED === 'true',
		},
		tokenManagement: {
			budgetLimits: {
				daily: Number.parseInt(env.LLM_TOKEN_BUDGET_DAILY || '100000'),
				monthly: Number.parseInt(env.LLM_TOKEN_BUDGET_MONTHLY || '2500000'),
			},
			costAlerts: {
				dailyThreshold: Number.parseFloat(env.LLM_COST_ALERT_DAILY || '10.0'), // USD
				monthlyThreshold: Number.parseFloat(env.LLM_COST_ALERT_MONTHLY || '250.0'), // USD
			},
			estimation: {
				enabled: env.LLM_TOKEN_ESTIMATION_ENABLED !== 'false', // Default true
				trackActualUsage: env.LLM_TRACK_ACTUAL_USAGE === 'true',
			},
		},
		monitoring: {
			enabled: env.LLM_MONITORING_ENABLED === 'true',
			logLevel: env.LLM_LOG_LEVEL || 'info', // debug, info, warn, error
			metricsCollection: env.LLM_METRICS_COLLECTION === 'true',
		},
	};
}
