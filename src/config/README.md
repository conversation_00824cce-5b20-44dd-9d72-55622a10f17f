# Config Directory

This directory contains application configuration files and settings management for different environments and services.

## Structure

```
config/
├── index.ts               # Main configuration exports and utilities
├── config.ts              # Backward compatibility re-exports
├── server.config.ts       # Server-side configuration (sensitive data)
└── client.config.ts       # Client-side configuration (public data)
```

## Configuration Files

### Server Configuration (`server.config.ts`)

-   **Server-only settings** with sensitive data
-   Authentication secrets (JWT secret, OAuth client secrets)
-   Database connection settings (Redis configuration)
-   LLM API keys and optimization settings
-   Server environment and port configuration
-   **Usage**: Import in server-side code (API routes, server actions)

### Client Configuration (`client.config.ts`)

-   **Client-safe settings** for browser use
-   Public authentication settings (cookie names, OAuth client IDs)
-   Feature flags for conditional UI rendering
-   Public application settings (app name, version, API base URL)
-   **Usage**: Import in client-side components and hooks

### Core Configuration (`config.ts`)

-   **Backward compatibility layer** that re-exports from server and client configs
-   Maintains existing import patterns for legacy code
-   **Usage**: Existing code can continue using current imports

### Configuration Index (`index.ts`)

-   Centralized configuration exports from all config files
-   Provides access to both server and client configurations
-   Type definitions and utility exports

## Key Features

### Environment Management

-   Development, staging, and production configurations
-   Environment variable validation and parsing
-   Secure handling of sensitive configuration data
-   Configuration override mechanisms

### Database Configuration

-   MongoDB connection string management
-   Connection pooling and optimization settings
-   Database health monitoring and diagnostics
-   Automatic reconnection and failover handling

### Security Configuration

-   JWT secret management
-   API key and token configuration
-   CORS and security header settings
-   Rate limiting configuration

### Service Integration

-   External API configuration (OpenAI, authentication providers)
-   Service endpoint management
-   Timeout and retry configuration
-   Service health check settings

## Design Principles

### Configuration Management

-   **Type Safety**: All configuration values are typed
-   **Validation**: Runtime validation of configuration values
-   **Environment Separation**: Clear separation between environments
-   **Security**: Secure handling of sensitive data
-   **Flexibility**: Easy configuration updates and overrides

### Best Practices

-   Use environment variables for sensitive data
-   Provide sensible defaults for optional configuration
-   Validate configuration at application startup
-   Document all configuration options
-   Use TypeScript for configuration type safety

## Usage Patterns

### Server-Side Configuration Access

```typescript
// In API routes, server actions, and server components
import { getAuthConfig, getLLMConfig } from '@/config/server.config';

// Access sensitive authentication settings
const authConfig = await getAuthConfig();
const jwtSecret = authConfig.jwtSecret;

// Access LLM API configuration
const llmConfig = await getLLMConfig();
const openAIKey = llmConfig.openAIKey;
```

### Client-Side Configuration Access

```typescript
// In client components and hooks
import { getFeatureFlags, clientConfig } from '@/config/client.config';

// Access feature flags for conditional rendering
const featureFlags = getFeatureFlags();
if (featureFlags.googleLogin) {
	// Show Google login button
}

// Access public app settings
const appName = clientConfig.appName;
```

### Backward Compatibility

```typescript
// Existing code continues to work
import { getAuthConfig, getFeatureFlags } from '@/config';

// These imports still work but now route to appropriate config files
const authConfig = await getAuthConfig(); // From server.config.ts
const featureFlags = getFeatureFlags(); // From client.config.ts
```

### Environment-Specific Settings

-   **Development**: Local database, debug logging, relaxed security
-   **Staging**: Production-like setup with test data
-   **Production**: Optimized performance, strict security, monitoring

## Development Guidelines

### General Principles

-   Always validate configuration values at startup
-   Use TypeScript interfaces for configuration structure
-   Provide clear error messages for missing configuration
-   Document environment variables and their purposes
-   Use configuration factories for complex setup logic
-   Implement configuration hot-reloading for development

### Security Guidelines

-   **Never expose sensitive data to client**: Use server.config.ts for secrets
-   **Use NEXT*PUBLIC* prefix**: For environment variables that need client access
-   **Validate server-side access**: Ensure server configs aren't called from client
-   **Separate concerns**: Keep authentication secrets separate from public settings

### Import Guidelines

-   **New code**: Import directly from `server.config.ts` or `client.config.ts`
-   **Legacy code**: Can continue using imports from `config.ts`
-   **Client components**: Only import from `client.config.ts`
-   **Server code**: Import from `server.config.ts` for sensitive data

### Environment Variables

-   **Server-only**: Standard environment variable names (e.g., `JWT_SECRET`)
-   **Client-accessible**: Use `NEXT_PUBLIC_` prefix (e.g., `NEXT_PUBLIC_APP_NAME`)
-   **Feature flags**: Use `NEXT_PUBLIC_FEATURE_` prefix for client-side toggles
