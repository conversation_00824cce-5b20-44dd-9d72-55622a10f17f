'use client';

// ============================================================================
// TYPES
// ============================================================================

export interface ClientConfig {
	appName: string;
	appVersion: string;
	apiBaseUrl: string;
	environment: string;
}

export interface ClientAuthConfig {
	jwtCookieName: string;
	google: {
		clientId: string;
	};
}

export interface FeatureFlags {
	googleLogin: boolean;
}

// ============================================================================
// CLIENT CONFIGURATION FUNCTIONS
// ============================================================================

/**
 * Get client-side application configuration
 * Contains public application settings safe for client-side use
 */
export function getClientConfig(): ClientConfig {
	return {
		appName: process.env.NEXT_PUBLIC_APP_NAME || 'Vocab',
		appVersion: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
		apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '',
		environment: process.env.NODE_ENV || 'development',
	};
}

/**
 * Get client-side authentication configuration
 * Contains only public authentication settings safe for client-side use
 */
export function getClientAuthConfig(): ClientAuthConfig {
	return {
		jwtCookieName: process.env.NEXT_PUBLIC_JWT_COOKIE_NAME || 'auth_token',
		google: {
			clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
		},
	};
}

/**
 * Get feature flags configuration
 * Contains feature toggles for conditional UI rendering
 */
export function getFeatureFlags(): FeatureFlags {
	return {
		googleLogin: process.env.NEXT_PUBLIC_FEATURE_GOOGLE_LOGIN === 'true',
	};
}

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

/**
 * Pre-configured client config instance
 */
export const clientConfig = getClientConfig();

/**
 * Pre-configured client auth config instance
 */
export const clientAuthConfig = getClientAuthConfig();

/**
 * Pre-configured feature flags instance
 */
export const featureFlags = getFeatureFlags();
