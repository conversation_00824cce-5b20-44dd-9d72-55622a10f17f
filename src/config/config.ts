// ============================================================================
// CONFIGURATION RE-EXPORTS
// ============================================================================
// This file maintains backward compatibility by re-exporting configurations
// from the separated server and client config files.

// Re-export server configurations
export {
	getServerConfig,
	getAuthConfig,
	getRedisConfig,
	getLLMConfig,
	getLLMOptimizationConfig,
	type ServerConfig,
	type AuthConfig,
	type RedisConfig,
	type LLMConfig,
	type LLMOptimizationConfig,
} from './server.config';

// Re-export client configurations
export {
	getClientConfig,
	getClientAuthConfig,
	getFeatureFlags,
	clientConfig,
	clientAuthConfig,
	featureFlags,
	type ClientConfig,
	type ClientAuthConfig,
	type FeatureFlags,
} from './client.config';

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================
// Note: For new code, prefer importing directly from server.config.ts or client.config.ts
// This file exists for backward compatibility with existing imports.
