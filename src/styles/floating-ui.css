/* ============================================================================
   FLOATING UI STYLES
   ============================================================================ */

/* Base floating UI styles */
.floating-ui-manager {
	position: relative;
	z-index: 1000;
	pointer-events: none;
}

.floating-ui-element {
	pointer-events: auto;
	will-change: transform, opacity;
}

/* Priority-based z-index */
.floating-ui-priority-critical {
	z-index: 1300;
}

.floating-ui-priority-high {
	z-index: 1200;
}

.floating-ui-priority-medium {
	z-index: 1100;
}

.floating-ui-priority-low {
	z-index: 1000;
}

/* Type-specific styles */
.floating-ui-settings {
	/* Settings-specific styles */
}

.floating-ui-guidance {
	/* Guidance-specific styles */
}

.floating-ui-notification {
	/* Notification-specific styles */
}

.floating-ui-tooltip {
	/* Tooltip-specific styles */
}

.floating-ui-modal {
	/* Modal-specific styles */
}

.floating-ui-dropdown {
	/* Dropdown-specific styles */
}

.floating-ui-custom {
	/* Custom element styles */
}

/* Collision detection */
.floating-ui-element.floating-ui-collision {
	box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
	animation: collision-pulse 1s ease-in-out infinite;
}

@keyframes collision-pulse {
	0%, 100% {
		box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
	}
	50% {
		box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3);
	}
}

/* Portal container */
.floating-ui-portal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1000;
}

.floating-ui-wrapper {
	position: relative;
	width: 100%;
	height: 100%;
}

/* Animation classes */
.floating-ui-fade-enter {
	opacity: 0;
}

.floating-ui-fade-enter-active {
	opacity: 1;
	transition: opacity 200ms ease-in-out;
}

.floating-ui-fade-exit {
	opacity: 1;
}

.floating-ui-fade-exit-active {
	opacity: 0;
	transition: opacity 200ms ease-in-out;
}

.floating-ui-slide-enter {
	opacity: 0;
	transform: translateY(20px);
}

.floating-ui-slide-enter-active {
	opacity: 1;
	transform: translateY(0);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-slide-exit {
	opacity: 1;
	transform: translateY(0);
}

.floating-ui-slide-exit-active {
	opacity: 0;
	transform: translateY(-20px);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-scale-enter {
	opacity: 0;
	transform: scale(0.9);
}

.floating-ui-scale-enter-active {
	opacity: 1;
	transform: scale(1);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-scale-exit {
	opacity: 1;
	transform: scale(1);
}

.floating-ui-scale-exit-active {
	opacity: 0;
	transform: scale(0.9);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-bounce-enter {
	opacity: 0;
	transform: scale(0.3);
}

.floating-ui-bounce-enter-active {
	opacity: 1;
	transform: scale(1);
	transition: opacity 300ms ease-out, transform 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.floating-ui-bounce-exit {
	opacity: 1;
	transform: scale(1);
}

.floating-ui-bounce-exit-active {
	opacity: 0;
	transform: scale(0.3);
	transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.floating-ui-element {
		max-width: calc(100vw - 32px);
		max-height: calc(100vh - 32px);
	}

	/* Adjust positioning for mobile */
	.floating-ui-element[data-floating-type="settings"] {
		bottom: 16px !important;
		right: 16px !important;
	}

	.floating-ui-element[data-floating-type="guidance"] {
		bottom: 80px !important;
		right: 16px !important;
		max-width: calc(100vw - 32px);
	}

	.floating-ui-element[data-floating-type="notification"] {
		top: 16px !important;
		right: 16px !important;
		left: 16px !important;
		width: auto !important;
	}

	.floating-ui-element[data-floating-type="modal"] {
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		max-width: calc(100vw - 32px);
		max-height: calc(100vh - 32px);
	}
}

@media (max-width: 480px) {
	.floating-ui-element {
		max-width: calc(100vw - 16px);
		max-height: calc(100vh - 16px);
	}

	.floating-ui-element[data-floating-type="settings"] {
		bottom: 8px !important;
		right: 8px !important;
	}

	.floating-ui-element[data-floating-type="guidance"] {
		bottom: 72px !important;
		right: 8px !important;
		left: 8px !important;
		max-width: calc(100vw - 16px);
	}

	.floating-ui-element[data-floating-type="notification"] {
		top: 8px !important;
		right: 8px !important;
		left: 8px !important;
	}
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
	.floating-ui-element.floating-ui-collision {
		box-shadow: 0 0 0 2px rgba(248, 113, 113, 0.6);
	}

	@keyframes collision-pulse {
		0%, 100% {
			box-shadow: 0 0 0 2px rgba(248, 113, 113, 0.6);
		}
		50% {
			box-shadow: 0 0 0 4px rgba(248, 113, 113, 0.4);
		}
	}
}

/* High contrast mode */
@media (prefers-contrast: high) {
	.floating-ui-element {
		border: 2px solid currentColor;
	}

	.floating-ui-element.floating-ui-collision {
		border-color: #ff0000;
		box-shadow: 0 0 0 4px #ff0000;
	}
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
	.floating-ui-element,
	.floating-ui-fade-enter-active,
	.floating-ui-fade-exit-active,
	.floating-ui-slide-enter-active,
	.floating-ui-slide-exit-active,
	.floating-ui-scale-enter-active,
	.floating-ui-scale-exit-active,
	.floating-ui-bounce-enter-active,
	.floating-ui-bounce-exit-active {
		transition: none !important;
		animation: none !important;
	}

	.floating-ui-element.floating-ui-collision {
		animation: none !important;
	}
}

/* Focus management */
.floating-ui-element:focus-within {
	outline: 2px solid hsl(var(--ring));
	outline-offset: 2px;
}

/* Print styles */
@media print {
	.floating-ui-manager,
	.floating-ui-element,
	.floating-ui-portal {
		display: none !important;
	}
}

/* Component-specific styles */
.floating-settings-panel {
	/* Settings panel specific styles */
}

.floating-guidance-panel {
	/* Guidance panel specific styles */
}

/* Utility classes */
.floating-ui-hidden {
	display: none !important;
}

.floating-ui-visible {
	display: block !important;
}

.floating-ui-no-pointer-events {
	pointer-events: none !important;
}

.floating-ui-pointer-events {
	pointer-events: auto !important;
}
