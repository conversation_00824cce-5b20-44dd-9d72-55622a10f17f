/* Pinterest-style Masonry Grid Layout */
.masonry-grid {
	/* Modern browsers with masonry support */
	grid-template-rows: masonry;

	/* Responsive column configuration */
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));

	/* Ensure items start at the top */
	align-items: start;

	/* Consistent gap */
	gap: 1rem;
}

/* Responsive breakpoints for better control */
@media (max-width: 640px) {
	.masonry-grid {
		grid-template-columns: 1fr;
	}
}

@media (min-width: 641px) and (max-width: 1024px) {
	.masonry-grid {
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
	}
}

@media (min-width: 1025px) and (max-width: 1280px) {
	.masonry-grid {
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	}
}

@media (min-width: 1281px) {
	.masonry-grid {
		grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	}
}

/* Fallback for browsers without masonry support */
@supports not (grid-template-rows: masonry) {
	.masonry-grid {
		/* Use CSS columns as fallback */
		display: block;
		column-count: auto;
		column-width: 300px;
		column-gap: 1rem;
		column-fill: balance;
	}

	.masonry-grid > * {
		/* Prevent items from breaking across columns */
		break-inside: avoid;
		page-break-inside: avoid;
		display: inline-block;
		width: 100%;
		margin-bottom: 1rem;
	}
}

/* Responsive column count for fallback */
@supports not (grid-template-rows: masonry) {
	@media (max-width: 640px) {
		.masonry-grid {
			column-count: 1;
		}
	}

	@media (min-width: 641px) and (max-width: 1024px) {
		.masonry-grid {
			column-count: 2;
			column-width: auto;
		}
	}

	@media (min-width: 1025px) and (max-width: 1536px) {
		.masonry-grid {
			column-count: 3;
			column-width: auto;
		}
	}

	@media (min-width: 1537px) {
		.masonry-grid {
			column-count: 4;
			column-width: auto;
		}
	}
}

/* Animation support for masonry items */
.masonry-grid > * {
	transition: all 0.3s ease;
}

/* Ensure proper spacing and alignment */
.masonry-grid-item {
	break-inside: avoid;
	page-break-inside: avoid;
	margin-bottom: 1rem;
}

/* Loading state for masonry grid */
.masonry-grid-loading {
	opacity: 0.7;
	pointer-events: none;
}

/* Smooth transitions for expand/collapse */
.masonry-grid .word-card {
	transition: all 0.3s ease;
	transform-origin: center;
}

.masonry-grid .word-card:hover {
	transform: translateY(-4px) scale(1.02);
	box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
	z-index: 10;
}

/* Ensure smooth expand/collapse animations */
.masonry-grid .word-card[data-expanded='true'] {
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.masonry-grid .word-card[data-expanded='false'] {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pinterest-style pin effect on hover */
.masonry-grid .word-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
	border-radius: inherit;
}

.masonry-grid .word-card:hover::before {
	opacity: 1;
}

/* Staggered animation for initial load */
.masonry-grid > *:nth-child(1) {
	animation-delay: 0ms;
}
.masonry-grid > *:nth-child(2) {
	animation-delay: 50ms;
}
.masonry-grid > *:nth-child(3) {
	animation-delay: 100ms;
}
.masonry-grid > *:nth-child(4) {
	animation-delay: 150ms;
}
.masonry-grid > *:nth-child(5) {
	animation-delay: 200ms;
}
.masonry-grid > *:nth-child(6) {
	animation-delay: 250ms;
}
.masonry-grid > *:nth-child(7) {
	animation-delay: 300ms;
}
.masonry-grid > *:nth-child(8) {
	animation-delay: 350ms;
}
