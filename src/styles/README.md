# Styles Directory

This directory contains CSS files and styling utilities that complement the Tailwind CSS framework with custom styles and component-specific styling.

## Structure

```
styles/
└── floating-ui.css             # Floating UI component styles
```

## Style Categories

### Floating UI Styles (`floating-ui.css`)
- Custom CSS for floating UI components and overlays
- Advanced positioning and animation styles
- Z-index management for layered UI elements
- Responsive behavior for floating elements
- Accessibility-focused styling for screen readers

## Key Features

### Floating UI Styling
- **Advanced Positioning**: CSS for complex floating element positioning
- **Animation and Transitions**: Smooth animations for floating UI interactions
- **Responsive Design**: Mobile-friendly floating UI behavior
- **Accessibility**: Screen reader and keyboard navigation support
- **Performance**: Optimized CSS for smooth rendering and animations

### Custom CSS Integration
- **Tailwind Complement**: Custom styles that work alongside Tailwind CSS
- **Component-Specific**: Styles for specific UI components and interactions
- **Browser Compatibility**: Cross-browser compatible CSS implementations
- **Performance Optimization**: Efficient CSS for optimal rendering performance

## Design Principles

### Styling Architecture
- **Utility-First**: Primarily use Tailwind CSS utilities
- **Custom Enhancements**: Custom CSS only when Tailwind is insufficient
- **Component Isolation**: Scoped styles for specific components
- **Performance**: Minimal CSS footprint and optimized selectors
- **Maintainability**: Clear organization and documentation of custom styles

### Floating UI Design
- **Layering**: Proper z-index management for overlay elements
- **Positioning**: Precise positioning for floating elements
- **Responsiveness**: Adaptive behavior across different screen sizes
- **Accessibility**: Keyboard navigation and screen reader support
- **Animation**: Smooth and performant animations and transitions

## Usage Patterns

### Floating UI Classes
```css
/* Example floating UI styles */
.floating-element {
  /* Custom positioning and styling */
}

.floating-overlay {
  /* Overlay background and positioning */
}

.floating-animation {
  /* Animation and transition styles */
}
```

### Integration with Components
```typescript
// Component using floating UI styles
import '@/styles/floating-ui.css';

function FloatingComponent() {
  return (
    <div className="floating-element">
      {/* Component content */}
    </div>
  );
}
```

## Styling Guidelines

### CSS Organization
- Use semantic class names that describe purpose
- Group related styles together
- Include comments for complex styling logic
- Follow consistent naming conventions
- Optimize for performance and maintainability

### Tailwind Integration
- Use Tailwind utilities as the primary styling approach
- Add custom CSS only when Tailwind cannot achieve the desired result
- Use CSS custom properties for dynamic values
- Maintain consistency with Tailwind's design system
- Document any deviations from standard Tailwind patterns

### Responsive Design
- Mobile-first responsive design approach
- Use CSS media queries for complex responsive behavior
- Ensure floating UI works across all device sizes
- Test on various screen sizes and orientations
- Consider touch interactions for mobile devices

### Accessibility Considerations
- Ensure sufficient color contrast for all text
- Provide focus indicators for interactive elements
- Support keyboard navigation for floating UI
- Include appropriate ARIA attributes in CSS
- Test with screen readers and assistive technologies

## Development Guidelines

### Custom CSS Best Practices
- Minimize custom CSS in favor of Tailwind utilities
- Use CSS custom properties for theming and dynamic values
- Implement efficient selectors for performance
- Include fallbacks for older browsers when necessary
- Document complex CSS logic and browser-specific hacks

### Performance Optimization
- Minimize CSS file size and complexity
- Use efficient selectors and avoid deep nesting
- Optimize animations for 60fps performance
- Consider CSS containment for performance isolation
- Profile CSS performance impact on rendering

### Browser Compatibility
- Test across major browsers and versions
- Provide fallbacks for newer CSS features
- Use progressive enhancement for advanced features
- Consider polyfills for critical functionality
- Document browser support requirements

### Maintenance and Updates
- Keep custom CSS minimal and well-documented
- Regularly review and refactor custom styles
- Update styles to match design system changes
- Remove unused CSS and optimize for performance
- Maintain consistency with overall design patterns
