# Contexts Directory

This directory contains React Context providers for global state management across the application, implementing a centralized state architecture.

## Structure

```
contexts/
├── index.ts                      # Context exports and utilities
├── auth-context.tsx              # Authentication state management
├── collections-context.tsx       # Collections data management
├── error-context.tsx             # Error handling and reporting
├── floating-ui-context.tsx       # Floating UI state management
├── keywords-context.tsx          # Keywords and search state
├── last-seen-word-context.tsx    # Spaced repetition tracking
├── llm-context.tsx               # AI/LLM integration state
├── loading-context.tsx           # Loading state management
├── media-query-context.tsx       # Responsive design state
├── simple-floating-context.tsx   # Simple floating UI management
├── toast-context.tsx             # Toast notification system
├── translation-context.tsx       # Internationalization state
└── translations/                 # Translation files and utilities
```

## Context Categories

### Authentication (`auth-context.tsx`)
- User authentication state
- Login/logout functionality
- User profile and preferences
- Authentication provider management
- Session management and persistence

### Data Management
- **Collections Context**: Vocabulary collection state and operations
- **Keywords Context**: Search and keyword management
- **Last Seen Word Context**: Spaced repetition and learning progress

### UI State Management
- **Loading Context**: Global loading state coordination
- **Toast Context**: Notification and message system
- **Floating UI Context**: Modal and overlay management
- **Simple Floating Context**: Lightweight floating UI elements
- **Media Query Context**: Responsive design state

### System Integration
- **Translation Context**: Internationalization and localization
- **Error Context**: Error handling and reporting
- **LLM Context**: AI service integration and state

## Key Features

### State Management Architecture
- **Global State**: Application-wide state coordination
- **Context Composition**: Hierarchical context provider setup
- **Type Safety**: TypeScript interfaces for all context values
- **Performance**: Optimized re-rendering and state updates

### Authentication System
- Multi-provider authentication (Telegram, Google, Username/Password)
- JWT token management and refresh
- User session persistence
- Role-based access control

### Loading State Management
- Granular loading state control
- Hierarchical loading scopes
- Loading state isolation by feature
- Performance-optimized loading indicators

### Internationalization
- Multi-language support (English, Vietnamese)
- Dynamic language switching
- Translation key management
- Locale-specific formatting

### Error Handling
- Centralized error reporting
- Error categorization and classification
- User-friendly error messages
- Error recovery mechanisms

## Design Principles

### Context Design
- **Single Responsibility**: Each context has a focused purpose
- **Composition**: Contexts can be composed and nested
- **Performance**: Minimize unnecessary re-renders
- **Type Safety**: Comprehensive TypeScript typing
- **Testability**: Easy to mock and test

### State Management
- **Immutability**: State updates through immutable patterns
- **Predictability**: Clear state update flows
- **Debugging**: Easy to trace state changes
- **Persistence**: Appropriate state persistence strategies

## Usage Patterns

### Context Provider Setup
```typescript
// In layout.tsx or app root
<AuthProvider>
  <LoadingProvider>
    <ToastProvider>
      <TranslationProvider>
        {children}
      </TranslationProvider>
    </ToastProvider>
  </LoadingProvider>
</AuthProvider>
```

### Context Consumption
```typescript
import { useAuth, useLoading, useToast } from '@/contexts';

function MyComponent() {
  const { user, login, logout } = useAuth();
  const { isLoading, setLoading } = useLoading();
  const { showToast } = useToast();
  
  // Component logic
}
```

## Development Guidelines

- Use TypeScript interfaces for context value types
- Implement proper error boundaries for context providers
- Optimize context value objects to prevent unnecessary re-renders
- Use custom hooks for context consumption
- Provide default values and error handling
- Document context APIs and usage patterns
- Test context providers and consumers thoroughly
