'use client';

// Removed direct backend API imports - now using HTTP requests
import { AUTH_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { UnauthorizedError, createErrorContext, normalizeError } from '@/lib/error-handling';
import { Provider } from '@prisma/client';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useErrorHandler } from './error-context';
import { useLoadingError, useScopedLoading } from './loading-context';

type AuthContextType = {
	user: any | null;
	isLoading: boolean;
	error: Error | null;
	getUser: () => Promise<void>;
	providerLogin: (provider: Provider, provider_id: string) => Promise<void>;
	logout: () => Promise<void>;
	getUserByProvider: (provider: Provider, providerId: string) => Promise<any | null>;
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
	const [user, setUser] = useState<any | null>(null);
	const [error, setError] = useState<Error | null>(null);
	const hasAttemptedAuth = useRef(false);

	const { getLoading } = useScopedLoading(LOADING_SCOPES.AUTH);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.AUTH);
	const { handleError } = useErrorHandler('AuthContext');

	const isLoading = useMemo(
		() =>
			getLoading(AUTH_LOADING_KEYS.GET_USER) ||
			getLoading(AUTH_LOADING_KEYS.PROVIDER_LOGIN) ||
			getLoading(AUTH_LOADING_KEYS.LOGOUT) ||
			getLoading(AUTH_LOADING_KEYS.GET_USER_BY_PROVIDER),
		[getLoading]
	);

	const handleApiError = useCallback(
		(err: unknown, defaultMessage: string, action?: string) => {
			const normalizedError = normalizeError(
				err,
				defaultMessage,
				createErrorContext('AuthContext', action)
			);
			handleError(normalizedError, action);
			return normalizedError;
		},
		[handleError]
	);

	const getUser = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, AUTH_LOADING_KEYS.GET_USER);
		start();

		try {
			const response = await fetch('/api/user/current');
			if (!response.ok) {
				if (response.status === 401) {
					setUser(null);
					end();
					return;
				}
				throw new UnauthorizedError('Failed to fetch current user');
			}
			const result = await response.json();
			setUser(result);
			end();
		} catch (err) {
			end(handleApiError(err, 'Failed to get current user', 'get_user'));
		}
	}, [loadingErrorHelper, handleApiError]);

	const providerLogin = useCallback(
		async (provider: Provider, provider_id: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				AUTH_LOADING_KEYS.PROVIDER_LOGIN
			);
			start();

			try {
				const response = await fetch('/api/auth/provider-login', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ provider, providerId: provider_id }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to login with provider');
				}

				await getUser();
				end();
			} catch (err) {
				end(handleApiError(err, 'Failed to login with provider', 'provider_login'));
			}
		},
		[getUser, loadingErrorHelper, handleApiError]
	);

	const logout = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, AUTH_LOADING_KEYS.LOGOUT);
		start();

		try {
			const response = await fetch('/api/auth/logout', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to logout');
			}

			setUser(null);
			end();
		} catch (err) {
			end(handleApiError(err, 'Failed to logout'));
		}
	}, [loadingErrorHelper, handleApiError]);

	const getUserByProvider = useCallback(
		async (provider: Provider, providerId: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				AUTH_LOADING_KEYS.GET_USER_BY_PROVIDER
			);
			start();

			try {
				const response = await fetch('/api/user/by-provider', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ provider, providerId }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to get user by provider');
				}

				const result = await response.json();
				end();
				return result;
			} catch (err) {
				end(handleApiError(err, 'Failed to get user by provider'));
				return null;
			}
		},
		[loadingErrorHelper, handleApiError]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	// Auto-load user on mount if not already attempted
	useEffect(() => {
		const initAuth = async () => {
			// Only attempt auth once and when not already loading
			if (!hasAttemptedAuth.current && !user && !isLoading) {
				hasAttemptedAuth.current = true;
				await getUser();
			}
		};

		initAuth();
	}, [user, isLoading, getUser]);

	const value = useMemo(
		() => ({
			user,
			isLoading,
			error,
			getUser,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoadingState: getLoading,
		}),
		[
			user,
			isLoading,
			error,
			getUser,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoading,
		]
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext() {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuthContext must be used within an AuthProvider');
	}
	return context;
}

export function useAuth() {
	return useAuthContext();
}
