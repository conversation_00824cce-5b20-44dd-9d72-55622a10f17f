'use client';

import {
	AppError,
	createErrorContext,
	errorLogger,
	getUserFriendlyMessage,
	logAndNormalizeError,
	normalizeError,
} from '@/lib/error-handling';
import { createContext, ReactNode, useCallback, useContext, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface ErrorState {
	errors: AppError[];
	globalError: AppError | null;
	isOffline: boolean;
}

export interface ErrorContextType {
	// Error state
	errorState: ErrorState;

	// Error management
	addError: (error: unknown, source?: string, context?: Record<string, any>) => AppError;
	removeError: (errorId: string) => void;
	clearErrors: () => void;
	setGlobalError: (error: AppError | null) => void;

	// Error utilities
	handleError: (error: unknown, source?: string, context?: Record<string, any>) => AppError;
	getErrorById: (errorId: string) => AppError | undefined;
	getErrorsBySource: (source: string) => AppError[];

	// Network status
	setOfflineStatus: (isOffline: boolean) => void;
}

// ============================================================================
// CONTEXT
// ============================================================================

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export function useError(): ErrorContextType {
	const context = useContext(ErrorContext);
	if (!context) {
		throw new Error('useError must be used within an ErrorProvider');
	}
	return context;
}

// ============================================================================
// PROVIDER
// ============================================================================

interface ErrorProviderProps {
	children: ReactNode;
	maxErrors?: number;
}

export function ErrorProvider({ children, maxErrors = 10 }: ErrorProviderProps) {
	const [errorState, setErrorState] = useState<ErrorState>({
		errors: [],
		globalError: null,
		isOffline: false,
	});

	// Add error to the error list
	const addError = useCallback(
		(error: unknown, source?: string, context?: Record<string, any>): AppError => {
			const errorContext = createErrorContext(source, 'error_occurred', context);
			const normalizedError = logAndNormalizeError(
				error,
				'Error occurred',
				source,
				errorContext
			);

			setErrorState((prev) => {
				const newErrors = [...prev.errors, normalizedError];

				// Keep only the most recent errors
				if (newErrors.length > maxErrors) {
					return {
						...prev,
						errors: newErrors.slice(-maxErrors),
					};
				}

				return {
					...prev,
					errors: newErrors,
				};
			});

			return normalizedError;
		},
		[maxErrors]
	);

	// Remove specific error
	const removeError = useCallback((errorId: string) => {
		setErrorState((prev) => ({
			...prev,
			errors: prev.errors.filter((error) => error.id !== errorId),
		}));
	}, []);

	// Clear all errors
	const clearErrors = useCallback(() => {
		setErrorState((prev) => ({
			...prev,
			errors: [],
			globalError: null,
		}));
	}, []);

	// Set global error (for critical errors that should block the UI)
	const setGlobalError = useCallback((error: AppError | null) => {
		setErrorState((prev) => ({
			...prev,
			globalError: error,
		}));

		if (error) {
			errorLogger.error('Global error set', error, undefined, 'ErrorProvider');
		}
	}, []);

	// Handle error with automatic categorization
	const handleError = useCallback(
		(error: unknown, source?: string, context?: Record<string, any>): AppError => {
			const errorContext = createErrorContext(source, 'handle_error', context);
			const normalizedError = normalizeError(error, 'An error occurred', errorContext);

			// Log the error
			errorLogger.error(
				`Error handled in ${source || 'unknown source'}`,
				normalizedError,
				context,
				source
			);

			// Add to error list for non-critical errors
			if (normalizedError.severity !== 'critical') {
				addError(normalizedError, source, context);
			} else {
				// Set as global error for critical errors
				setGlobalError(normalizedError);
			}

			return normalizedError;
		},
		[addError, setGlobalError]
	);

	// Get error by ID
	const getErrorById = useCallback(
		(errorId: string): AppError | undefined => {
			return errorState.errors.find((error) => error.id === errorId);
		},
		[errorState.errors]
	);

	// Get errors by source
	const getErrorsBySource = useCallback(
		(source: string): AppError[] => {
			return errorState.errors.filter(
				(error) => error.context?.application?.component === source
			);
		},
		[errorState.errors]
	);

	// Set offline status
	const setOfflineStatus = useCallback((isOffline: boolean) => {
		setErrorState((prev) => ({
			...prev,
			isOffline,
		}));

		if (isOffline) {
			errorLogger.warn('Application went offline', { isOffline }, 'ErrorProvider');
		} else {
			errorLogger.info('Application came back online', { isOffline }, 'ErrorProvider');
		}
	}, []);

	const contextValue: ErrorContextType = {
		errorState,
		addError,
		removeError,
		clearErrors,
		setGlobalError,
		handleError,
		getErrorById,
		getErrorsBySource,
		setOfflineStatus,
	};

	return <ErrorContext.Provider value={contextValue}>{children}</ErrorContext.Provider>;
}

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook for handling errors in components
 */
export function useErrorHandler(source?: string) {
	const { handleError, addError, removeError } = useError();

	const handleComponentError = useCallback(
		(error: unknown, action?: string, context?: Record<string, any>) => {
			return handleError(error, source, { action, ...context });
		},
		[handleError, source]
	);

	const addComponentError = useCallback(
		(error: unknown, context?: Record<string, any>) => {
			return addError(error, source, context);
		},
		[addError, source]
	);

	return {
		handleError: handleComponentError,
		addError: addComponentError,
		removeError,
	};
}

/**
 * Hook for getting user-friendly error messages
 */
export function useErrorMessages() {
	const { errorState } = useError();

	const getDisplayMessage = useCallback((error: AppError): string => {
		return getUserFriendlyMessage(error);
	}, []);

	const getDisplayErrors = useCallback(() => {
		return errorState.errors.map((error) => ({
			id: error.id,
			message: getDisplayMessage(error),
			severity: error.severity,
			category: error.category,
			timestamp: error.timestamp,
		}));
	}, [errorState.errors, getDisplayMessage]);

	return {
		getDisplayMessage,
		getDisplayErrors,
		hasErrors: errorState.errors.length > 0,
		globalError: errorState.globalError,
		isOffline: errorState.isOffline,
	};
}
