'use client';

import { createContext, useContext, useEffect, useMemo, useState } from 'react';

type MediaQueryContextType = {
	isMobile: boolean;
	isTablet: boolean;
	isDesktop: boolean;
	matchesQuery: (query: string) => boolean;
};

const MediaQueryContext = createContext<MediaQueryContextType | undefined>(undefined);

export function MediaQueryProvider({ children }: { children: React.ReactNode }) {
	const [isMobile, setIsMobile] = useState(false);
	const [isTablet, setIsTablet] = useState(false);
	const [isDesktop, setIsDesktop] = useState(false);

	useEffect(() => {
		const mobileQuery = window.matchMedia('(max-width: 768px)');
		const tabletQuery = window.matchMedia('(min-width: 769px) and (max-width: 1024px)');
		const desktopQuery = window.matchMedia('(min-width: 1025px)');

		const updateMobile = (e: MediaQueryListEvent) => setIsMobile(e.matches);
		const updateTablet = (e: MediaQueryListEvent) => setIsTablet(e.matches);
		const updateDesktop = (e: MediaQueryListEvent) => setIsDesktop(e.matches);

		// Set initial values
		setIsMobile(mobileQuery.matches);
		setIsTablet(tabletQuery.matches);
		setIsDesktop(desktopQuery.matches);

		// Add listeners
		mobileQuery.addEventListener('change', updateMobile);
		tabletQuery.addEventListener('change', updateTablet);
		desktopQuery.addEventListener('change', updateDesktop);

		// Cleanup
		return () => {
			mobileQuery.removeEventListener('change', updateMobile);
			tabletQuery.removeEventListener('change', updateTablet);
			desktopQuery.removeEventListener('change', updateDesktop);
		};
	}, []);

	const matchesQuery = (query: string): boolean => {
		if (typeof window === 'undefined') return false;
		return window.matchMedia(query).matches;
	};

	const value = useMemo(
		() => ({
			isMobile,
			isTablet,
			isDesktop,
			matchesQuery,
		}),
		[isMobile, isTablet, isDesktop]
	);

	return <MediaQueryContext.Provider value={value}>{children}</MediaQueryContext.Provider>;
}

export function useMediaQueryContext() {
	const context = useContext(MediaQueryContext);
	if (context === undefined) {
		throw new Error('useMediaQueryContext must be used within a MediaQueryProvider');
	}
	return context;
}

// Export hook for backward compatibility
export function useMediaQuery(query: string) {
	const [matches, setMatches] = useState(false);

	useEffect(() => {
		if (typeof window === 'undefined') return;

		const mediaQuery = window.matchMedia(query);
		setMatches(mediaQuery.matches);

		const listener = (event: MediaQueryListEvent) => {
			setMatches(event.matches);
		};

		mediaQuery.addEventListener('change', listener);

		return () => {
			mediaQuery.removeEventListener('change', listener);
		};
	}, [query]);

	return matches;
}
