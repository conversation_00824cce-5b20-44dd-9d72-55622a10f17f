'use client';

import React, {
	createContext,
	useContext,
	useCallback,
	useReducer,
	useEffect,
	useRef,
	ReactNode,
} from 'react';
import {
	FloatingUIContextType,
	FloatingUIState,
	FloatingElement,
	FloatingUIType,
	FloatingPriority,
	FloatingCoordinates,
	DEFAULT_FLOATING_CONFIG,
	FloatingUIConfig,
} from '@/types/floating-ui';

// ============================================================================
// FLOATING UI REDUCER
// ============================================================================

type FloatingUIAction =
	| { type: 'REGISTER'; element: Omit<FloatingElement, 'visible'> }
	| { type: 'UNREGISTER'; id: string }
	| { type: 'SHOW'; id: string }
	| { type: 'HIDE'; id: string }
	| { type: 'UPDATE'; id: string; updates: Partial<FloatingElement> }
	| { type: 'UPDATE_VIEWPORT'; width: number; height: number }
	| { type: 'UPDATE_COLLISIONS'; collisions: Map<string, string[]> }
	| { type: 'HIDE_ALL'; except?: string[] }
	| { type: 'SHOW_ALL' };

function floatingUIReducer(state: FloatingUIState, action: FloatingUIAction): FloatingUIState {
	switch (action.type) {
		case 'REGISTER': {
			const element: FloatingElement = { ...action.element, visible: false };
			const newElements = new Map(state.elements);
			newElements.set(element.id, element);

			return {
				...state,
				elements: newElements,
			};
		}

		case 'UNREGISTER': {
			const newElements = new Map(state.elements);
			newElements.delete(action.id);

			const newActiveElements = state.activeElements.filter((id) => id !== action.id);
			const newCollisions = new Map(state.collisions);
			newCollisions.delete(action.id);

			return {
				...state,
				elements: newElements,
				activeElements: newActiveElements,
				collisions: newCollisions,
			};
		}

		case 'SHOW': {
			const element = state.elements.get(action.id);
			if (!element) return state;

			const newElements = new Map(state.elements);
			newElements.set(action.id, { ...element, visible: true });

			const newActiveElements = state.activeElements.includes(action.id)
				? state.activeElements
				: [...state.activeElements, action.id];

			return {
				...state,
				elements: newElements,
				activeElements: newActiveElements,
			};
		}

		case 'HIDE': {
			const element = state.elements.get(action.id);
			if (!element) return state;

			const newElements = new Map(state.elements);
			newElements.set(action.id, { ...element, visible: false });

			const newActiveElements = state.activeElements.filter((id) => id !== action.id);

			return {
				...state,
				elements: newElements,
				activeElements: newActiveElements,
			};
		}

		case 'UPDATE': {
			const element = state.elements.get(action.id);
			if (!element) return state;

			const newElements = new Map(state.elements);
			newElements.set(action.id, { ...element, ...action.updates });

			return {
				...state,
				elements: newElements,
			};
		}

		case 'UPDATE_VIEWPORT': {
			return {
				...state,
				viewport: {
					width: action.width,
					height: action.height,
				},
			};
		}

		case 'UPDATE_COLLISIONS': {
			return {
				...state,
				collisions: action.collisions,
			};
		}

		case 'HIDE_ALL': {
			const newElements = new Map();
			const except = action.except || [];

			state.elements.forEach((element, id) => {
				if (except.includes(id)) {
					newElements.set(id, element);
				} else {
					newElements.set(id, { ...element, visible: false });
				}
			});

			const newActiveElements = state.activeElements.filter((id) => except.includes(id));

			return {
				...state,
				elements: newElements,
				activeElements: newActiveElements,
			};
		}

		case 'SHOW_ALL': {
			const newElements = new Map();
			const newActiveElements: string[] = [];

			state.elements.forEach((element, id) => {
				newElements.set(id, { ...element, visible: true });
				newActiveElements.push(id);
			});

			return {
				...state,
				elements: newElements,
				activeElements: newActiveElements,
			};
		}

		default:
			return state;
	}
}

// ============================================================================
// FLOATING UI CONTEXT
// ============================================================================

const FloatingUIContext = createContext<FloatingUIContextType | null>(null);

interface FloatingUIProviderProps {
	children: ReactNode;
	config?: Partial<FloatingUIConfig>;
}

export function FloatingUIProvider({ children, config = {} }: FloatingUIProviderProps) {
	const mergedConfig = { ...DEFAULT_FLOATING_CONFIG, ...config };

	const [state, dispatch] = useReducer(floatingUIReducer, {
		elements: new Map(),
		activeElements: [],
		collisions: new Map(),
		viewport: {
			width: typeof window !== 'undefined' ? window.innerWidth : 1024,
			height: typeof window !== 'undefined' ? window.innerHeight : 768,
		},
	});

	// Use ref to avoid stale closures
	const stateRef = useRef(state);
	stateRef.current = state;

	// Update viewport size on resize
	useEffect(() => {
		const handleResize = () => {
			dispatch({
				type: 'UPDATE_VIEWPORT',
				width: window.innerWidth,
				height: window.innerHeight,
			});
		};

		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, []);

	// Calculate element rectangle
	const calculateElementRect = useCallback((element: FloatingElement) => {
		const coords = element.coordinates || {};
		const dims = element.dimensions || {};

		return {
			top: coords.top || 0,
			left: coords.left || 0,
			right: (coords.right || 0) + (dims.width || 100),
			bottom: (coords.bottom || 0) + (dims.height || 100),
			width: dims.width || 100,
			height: dims.height || 100,
		};
	}, []);

	// Check if two rectangles collide
	const rectsCollide = useCallback((rect1: any, rect2: any, margin: number = 0) => {
		return !(
			rect1.right + margin < rect2.left ||
			rect2.right + margin < rect1.left ||
			rect1.bottom + margin < rect2.top ||
			rect2.bottom + margin < rect1.top
		);
	}, []);

	// Collision detection utility
	const detectCollisions = useCallback(
		(elementId: string): string[] => {
			const currentState = stateRef.current;
			const element = currentState.elements.get(elementId);
			if (!element || !element.visible || !element.collisionDetection) return [];

			const collisions: string[] = [];
			const elementRect = calculateElementRect(element);

			currentState.activeElements.forEach((otherId) => {
				if (otherId === elementId) return;

				const otherElement = currentState.elements.get(otherId);
				if (!otherElement || !otherElement.visible) return;

				const otherRect = calculateElementRect(otherElement);
				if (rectsCollide(elementRect, otherRect, mergedConfig.collisionMargin)) {
					collisions.push(otherId);
				}
			});

			return collisions;
		},
		[calculateElementRect, rectsCollide, mergedConfig.collisionMargin]
	);

	// Context actions
	const register = useCallback((element: Omit<FloatingElement, 'visible'>) => {
		dispatch({ type: 'REGISTER', element });
	}, []);

	const unregister = useCallback((id: string) => {
		dispatch({ type: 'UNREGISTER', id });
	}, []);

	const show = useCallback((id: string) => {
		const element = stateRef.current.elements.get(id);
		if (element?.onShow) element.onShow();
		dispatch({ type: 'SHOW', id });
	}, []);

	const hide = useCallback((id: string) => {
		const element = stateRef.current.elements.get(id);
		if (element?.onHide) element.onHide();
		dispatch({ type: 'HIDE', id });
	}, []);

	const toggle = useCallback((id: string) => {
		const element = stateRef.current.elements.get(id);
		if (!element) return;

		if (element.visible) {
			const hideElement = stateRef.current.elements.get(id);
			if (hideElement?.onHide) hideElement.onHide();
			dispatch({ type: 'HIDE', id });
		} else {
			const showElement = stateRef.current.elements.get(id);
			if (showElement?.onShow) showElement.onShow();
			dispatch({ type: 'SHOW', id });
		}
	}, []);

	const update = useCallback((id: string, updates: Partial<FloatingElement>) => {
		dispatch({ type: 'UPDATE', id, updates });
	}, []);

	const bringToFront = useCallback(
		(id: string) => {
			const maxZIndex = Math.max(
				...Array.from(stateRef.current.elements.values()).map((el) => el.zIndex || 0)
			);
			dispatch({
				type: 'UPDATE',
				id,
				updates: { zIndex: maxZIndex + mergedConfig.zIndexStep },
			});
		},
		[mergedConfig.zIndexStep]
	);

	const sendToBack = useCallback(
		(id: string) => {
			const minZIndex = Math.min(
				...Array.from(stateRef.current.elements.values()).map((el) => el.zIndex || 0)
			);
			dispatch({
				type: 'UPDATE',
				id,
				updates: { zIndex: minZIndex - mergedConfig.zIndexStep },
			});
		},
		[mergedConfig.zIndexStep]
	);

	const hideAll = useCallback((except?: string[]) => {
		dispatch({ type: 'HIDE_ALL', except });
	}, []);

	const showAll = useCallback(() => {
		dispatch({ type: 'SHOW_ALL' });
	}, []);

	const getElement = useCallback((id: string) => {
		return stateRef.current.elements.get(id);
	}, []);

	const getVisibleElements = useCallback(() => {
		return Array.from(stateRef.current.elements.values()).filter((el) => el.visible);
	}, []);

	const getElementsByType = useCallback((type: FloatingUIType) => {
		return Array.from(stateRef.current.elements.values()).filter((el) => el.type === type);
	}, []);

	const getElementsByPriority = useCallback((priority: FloatingPriority) => {
		return Array.from(stateRef.current.elements.values()).filter(
			(el) => el.priority === priority
		);
	}, []);

	const checkCollisions = useCallback(
		(elementId: string) => {
			return detectCollisions(elementId);
		},
		[detectCollisions]
	);

	const calculateOptimalPosition = useCallback(
		(element: FloatingElement): FloatingCoordinates => {
			// Simple implementation - can be enhanced with more sophisticated algorithms
			const currentCoords = element.coordinates || {};
			const offset = 50;

			return {
				...currentCoords,
				top: (currentCoords.top || 0) + offset,
				left: (currentCoords.left || 0) + offset,
			};
		},
		[]
	);

	const resolveCollisions = useCallback(
		(elementId: string) => {
			const collisions = detectCollisions(elementId);
			if (collisions.length === 0) return;

			const currentState = stateRef.current;
			const element = currentState.elements.get(elementId);
			if (!element) return;

			// Call collision callback if provided
			if (element.onCollision) {
				const collidingElements = collisions
					.map((id) => currentState.elements.get(id))
					.filter(Boolean) as FloatingElement[];
				element.onCollision(collidingElements);
			}

			// Auto-resolve collisions for non-persistent elements
			if (!element.persistent) {
				const optimalPosition = calculateOptimalPosition(element);
				dispatch({
					type: 'UPDATE',
					id: elementId,
					updates: { coordinates: optimalPosition },
				});
			}
		},
		[detectCollisions, calculateOptimalPosition]
	);

	const contextValue: FloatingUIContextType = {
		state,
		register,
		unregister,
		show,
		hide,
		toggle,
		update,
		bringToFront,
		sendToBack,
		hideAll,
		showAll,
		getElement,
		getVisibleElements,
		getElementsByType,
		getElementsByPriority,
		checkCollisions,
		resolveCollisions,
		calculateOptimalPosition,
	};

	return <FloatingUIContext.Provider value={contextValue}>{children}</FloatingUIContext.Provider>;
}

export function useFloatingUI(): FloatingUIContextType {
	const context = useContext(FloatingUIContext);
	if (!context) {
		throw new Error('useFloatingUI must be used within a FloatingUIProvider');
	}
	return context;
}
