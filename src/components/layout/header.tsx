'use client';

import React from 'react';
import Link from 'next/link';
import { Navigation } from '@/components/ui/navigation';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { BookOpen } from 'lucide-react';

export function Header() {
	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="container mx-auto flex h-14 items-center justify-between px-4">
				{/* Logo */}
				<Link href="/" className="flex items-center space-x-2">
					<BookOpen className="h-6 w-6" />
					<span className="font-bold text-lg">Vocab</span>
				</Link>

				{/* Navigation */}
				<Navigation variant="header" />

				{/* Right side actions */}
				<div className="flex items-center space-x-2">
					<ThemeToggle />
				</div>
			</div>
		</header>
	);
}
