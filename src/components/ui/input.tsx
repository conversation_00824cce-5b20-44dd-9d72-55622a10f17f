'use client';

import React from 'react';
import { cn } from '@/lib';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	'aria-label'?: string;
	'aria-describedby'?: string;
	'aria-invalid'?: boolean;
	'aria-required'?: boolean;
	'aria-errormessage'?: string;
	placeholder?: string;
	wrapperClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	(
		{
			wrapperClassName,
			className,
			type,
			'aria-label': ariaLabel,
			'aria-describedby': ariaDescribedby,
			'aria-invalid': ariaInvalid,
			'aria-required': ariaRequired,
			'aria-errormessage': ariaErrormessage,
			placeholder,
			...props
		},
		ref
	) => {
		const inputId = React.useId();
		const [isFocused, setIsFocused] = React.useState(false);

		return (
			<div className={cn('relative', wrapperClassName)}>
				<input
					type={type}
					className={cn(
						'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
						className
					)}
					ref={ref}
					id={inputId}
					aria-label={ariaLabel}
					aria-describedby={ariaDescribedby}
					aria-invalid={ariaInvalid}
					aria-required={ariaRequired}
					aria-errormessage={ariaErrormessage}
					placeholder={placeholder}
					onFocus={() => setIsFocused(true)}
					onBlur={() => setIsFocused(false)}
					{...props}
				/>
				{isFocused && (
					<span className="sr-only" aria-live="polite">
						{placeholder ? `Enter ${placeholder}` : 'Input field focused'}
					</span>
				)}
			</div>
		);
	}
);
Input.displayName = 'Input';

export { Input };
