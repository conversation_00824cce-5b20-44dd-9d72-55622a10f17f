'use client';

import { Card, CardContent, CardHeader } from './card';

interface CollectionsSkeletonProps {
	count?: number;
	showHeader?: boolean;
	className?: string;
}

export function CollectionsSkeleton({
	count = 3,
	showHeader = true,
	className = '',
}: CollectionsSkeletonProps) {
	return (
		<div className={`space-y-8 ${className}`}>
			{showHeader && (
				<div className="text-center space-y-4">
					<div className="h-10 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
					<div className="h-6 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
				</div>
			)}

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{[...Array(count)].map((_, i) => (
					<Card key={i} className="animate-pulse hover:shadow-lg transition-shadow">
						<CardHeader className="pb-3">
							<div className="space-y-2">
								<div className="h-6 w-32 bg-muted rounded animate-pulse" />
								<div className="h-4 w-24 bg-muted rounded animate-pulse" />
							</div>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<div className="h-4 w-20 bg-muted rounded animate-pulse" />
									<div className="h-4 w-12 bg-muted rounded animate-pulse" />
								</div>
								<div className="space-y-2">
									<div className="h-3 w-full bg-muted rounded animate-pulse" />
									<div className="h-3 w-3/4 bg-muted rounded animate-pulse" />
								</div>
								<div className="flex justify-between items-center pt-2">
									<div className="h-8 w-20 bg-muted rounded animate-pulse" />
									<div className="flex gap-2">
										<div className="h-8 w-8 bg-muted rounded animate-pulse" />
										<div className="h-8 w-8 bg-muted rounded animate-pulse" />
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

// Compact version for smaller spaces
export function CollectionsSkeletonCompact({ count = 2 }: { count?: number }) {
	return (
		<div className="space-y-4">
			{[...Array(count)].map((_, i) => (
				<Card key={i} className="p-4 animate-pulse">
					<div className="flex items-center justify-between">
						<div className="space-y-2">
							<div className="h-5 w-32 bg-muted rounded animate-pulse" />
							<div className="h-3 w-24 bg-muted rounded animate-pulse" />
						</div>
						<div className="flex gap-2">
							<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							<div className="h-8 w-8 bg-muted rounded animate-pulse" />
						</div>
					</div>
				</Card>
			))}
		</div>
	);
}

// Grid version for collection overview
export function CollectionsSkeletonGrid({ count = 6 }: { count?: number }) {
	return (
		<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
			{[...Array(count)].map((_, i) => (
				<Card key={i} className="p-4 animate-pulse">
					<div className="space-y-3">
						<div className="h-5 w-full bg-muted rounded animate-pulse" />
						<div className="h-3 w-3/4 bg-muted rounded animate-pulse" />
						<div className="flex justify-between items-center">
							<div className="h-3 w-16 bg-muted rounded animate-pulse" />
							<div className="h-6 w-12 bg-muted rounded animate-pulse" />
						</div>
					</div>
				</Card>
			))}
		</div>
	);
}
