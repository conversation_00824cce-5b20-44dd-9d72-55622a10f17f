'use client';

import { cn } from '@/lib';
import React from 'react';

interface KeyboardNavigationProps {
	children: React.ReactNode;
	className?: string;
	orientation?: 'horizontal' | 'vertical';
	onFocusChange?: (index: number) => void;
}

export const KeyboardNavigation = React.forwardRef<HTMLDivElement, KeyboardNavigationProps>(
	({ children, className, orientation = 'horizontal', onFocusChange }, ref) => {
		const containerRef = React.useRef<HTMLDivElement>(null);
		const [focusedIndex, setFocusedIndex] = React.useState(-1);

		React.useEffect(() => {
			if (!containerRef.current) return;

			const container = containerRef.current;
			const focusableElements = container.querySelectorAll(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
			);

			const handleKeyDown = (e: KeyboardEvent) => {
				if (
					!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(
						e.key
					)
				) {
					return;
				}

				e.preventDefault();
				const currentIndex = Array.from(focusableElements).indexOf(
					document.activeElement as Element
				);

				let newIndex = currentIndex;

				if (orientation === 'horizontal') {
					if (e.key === 'ArrowLeft') {
						newIndex =
							currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
					} else if (e.key === 'ArrowRight') {
						newIndex =
							currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
					}
				} else {
					if (e.key === 'ArrowUp') {
						newIndex =
							currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
					} else if (e.key === 'ArrowDown') {
						newIndex =
							currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
					}
				}

				if (e.key === 'Home') {
					newIndex = 0;
				} else if (e.key === 'End') {
					newIndex = focusableElements.length - 1;
				}

				const element = focusableElements[newIndex] as HTMLElement;
				if (element) {
					element.focus();
					setFocusedIndex(newIndex);
					onFocusChange?.(newIndex);
				}
			};

			container.addEventListener('keydown', handleKeyDown);
			return () => container.removeEventListener('keydown', handleKeyDown);
		}, [orientation, onFocusChange]);

		return (
			<div
				ref={containerRef}
				className={cn('keyboard-navigation', className)}
				role={orientation === 'horizontal' ? 'toolbar' : 'menu'}
				aria-orientation={orientation}
			>
				{children}
			</div>
		);
	}
);

KeyboardNavigation.displayName = 'KeyboardNavigation';
