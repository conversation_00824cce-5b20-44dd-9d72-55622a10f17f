'use client';

import { Card, CardContent, CardHeader } from './card';

export function StatsSkeleton() {
	return (
		<div className="container mx-auto py-6 space-y-8">
			{/* Header */}
			<div className="text-center space-y-4">
				<div className="h-8 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
				<div className="h-5 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
			</div>

			{/* Today's Stats Cards */}
			<div>
				<div className="h-6 w-48 bg-muted rounded animate-pulse mb-4" />
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					{[...Array(3)].map((_, i) => (
						<Card key={i} className="animate-pulse">
							<CardHeader className="pb-3">
								<div className="flex items-center justify-between">
									<div className="h-4 w-24 bg-muted rounded animate-pulse" />
									<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								</div>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="h-8 w-16 bg-muted rounded animate-pulse" />
									<div className="h-3 w-20 bg-muted rounded animate-pulse" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>

			{/* Overall Stats Cards */}
			<div>
				<div className="h-6 w-40 bg-muted rounded animate-pulse mb-4" />
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					{[...Array(3)].map((_, i) => (
						<Card key={i} className="animate-pulse">
							<CardHeader className="pb-3">
								<div className="flex items-center justify-between">
									<div className="h-4 w-28 bg-muted rounded animate-pulse" />
									<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								</div>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="h-8 w-20 bg-muted rounded animate-pulse" />
									<div className="h-3 w-24 bg-muted rounded animate-pulse" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>

			{/* Charts Section */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
				{/* Activity Chart */}
				<Card className="animate-pulse">
					<CardHeader>
						<div className="h-6 w-32 bg-muted rounded animate-pulse" />
					</CardHeader>
					<CardContent>
						<div className="h-64 w-full bg-muted rounded animate-pulse" />
					</CardContent>
				</Card>

				{/* Progress Chart */}
				<Card className="animate-pulse">
					<CardHeader>
						<div className="h-6 w-40 bg-muted rounded animate-pulse" />
					</CardHeader>
					<CardContent>
						<div className="h-64 w-full bg-muted rounded animate-pulse" />
					</CardContent>
				</Card>
			</div>

			{/* Recent Activity */}
			<Card className="animate-pulse">
				<CardHeader>
					<div className="h-6 w-36 bg-muted rounded animate-pulse" />
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="flex items-center justify-between py-2">
								<div className="flex items-center gap-3">
									<div className="h-8 w-8 bg-muted rounded-full animate-pulse" />
									<div className="space-y-1">
										<div className="h-4 w-32 bg-muted rounded animate-pulse" />
										<div className="h-3 w-24 bg-muted rounded animate-pulse" />
									</div>
								</div>
								<div className="h-4 w-16 bg-muted rounded animate-pulse" />
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

// Compact stats skeleton for smaller spaces
export function StatsSkeletonCompact() {
	return (
		<div className="space-y-6">
			{/* Stats Cards */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				{[...Array(4)].map((_, i) => (
					<Card key={i} className="p-4 animate-pulse">
						<div className="space-y-2">
							<div className="h-3 w-16 bg-muted rounded animate-pulse" />
							<div className="h-6 w-12 bg-muted rounded animate-pulse" />
						</div>
					</Card>
				))}
			</div>

			{/* Mini Chart */}
			<Card className="p-4 animate-pulse">
				<div className="h-32 w-full bg-muted rounded animate-pulse" />
			</Card>
		</div>
	);
}
