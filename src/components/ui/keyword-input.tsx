import { Button, Input, Label } from '@/components/ui';

interface KeywordInputProps {
	keywords: string[];
	currentKeyword: string;
	label?: string;
	placeholder?: string;
	onKeywordsChange: (keywords: string[]) => void;
	onCurrentKeywordChange: (keyword: string) => void;
}

export function KeywordInput({
	keywords,
	currentKeyword,
	label = 'Keywords',
	placeholder = 'Enter keyword and press Enter or Add',
	onKeywordsChange,
	onCurrentKeywordChange,
}: KeywordInputProps) {
	const handleAddKeyword = () => {
		if (currentKeyword.trim() && !keywords.includes(currentKeyword.trim())) {
			onKeywordsChange([...keywords, currentKeyword.trim()]);
			onCurrentKeywordChange('');
		}
	};

	const handleRemoveKeyword = (keywordToRemove: string) => {
		onKeywordsChange(keywords.filter((keyword) => keyword !== keywordToRemove));
	};

	return (
		<div>
			<Label>{label}</Label>
			<div className="flex gap-2 mt-1">
				<Input
					value={currentKeyword}
					onChange={(e) => onCurrentKeywordChange(e.target.value)}
					placeholder={placeholder}
					onKeyDown={(e) => e.key === 'Enter' && handleAddKeyword()}
				/>
				<Button onClick={handleAddKeyword} variant="secondary">
					Add
				</Button>
			</div>
			<div className="flex flex-wrap gap-2 mt-2">
				{keywords.map((keyword) => (
					<div key={keyword} className="relative bg-primary/10 px-3 py-1 rounded-full">
						<span>{keyword}</span>
						<button
							onClick={() => handleRemoveKeyword(keyword)}
							className="absolute -top-1 -right-1 flex items-center justify-center rounded-full transition-all duration-150 w-4 h-4 text-xs bg-primary text-background hover:bg-primary/90 shadow-sm hover:shadow-md focus:shadow-md opacity-90 hover:opacity-100 focus:opacity-100"
							aria-label={`Delete ${keyword}`}
						>
							×
						</button>
					</div>
				))}
			</div>
		</div>
	);
}
