'use client';

import { Card, CardContent, CardHeader } from './card';

export function HomeSkeleton() {
	return (
		<div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
			<div className="container mx-auto px-4 py-8 space-y-16">
				{/* Hero Section Skeleton */}
				<section className="text-center space-y-8">
					<div className="space-y-4">
						<div className="h-16 w-96 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[600px] bg-muted rounded-lg animate-pulse mx-auto" />
						<div className="h-6 w-[500px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						<div className="h-12 w-48 bg-muted rounded-xl animate-pulse" />
						<div className="h-12 w-40 bg-muted rounded-xl animate-pulse" />
					</div>
				</section>

				{/* Collections Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{[...Array(3)].map((_, i) => (
							<Card key={i} className="animate-pulse">
								<CardHeader>
									<div className="h-6 w-32 bg-muted rounded animate-pulse" />
									<div className="h-4 w-24 bg-muted rounded animate-pulse" />
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</section>

				{/* Practice Types Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-80 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[500px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						{[...Array(4)].map((_, i) => (
							<Card key={i} className="text-center p-6 animate-pulse">
								<div className="space-y-4">
									<div className="h-12 w-12 bg-muted rounded-full animate-pulse mx-auto" />
									<div className="h-6 w-24 bg-muted rounded animate-pulse mx-auto" />
									<div className="space-y-2">
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-3/4 bg-muted rounded animate-pulse mx-auto" />
									</div>
								</div>
							</Card>
						))}
					</div>
				</section>

				{/* Paragraphs Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-72 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[450px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
						{[...Array(2)].map((_, i) => (
							<Card key={i} className="p-6 animate-pulse">
								<div className="space-y-4">
									<div className="h-8 w-48 bg-muted rounded animate-pulse" />
									<div className="space-y-2">
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
									</div>
									<div className="h-10 w-32 bg-muted rounded animate-pulse" />
								</div>
							</Card>
						))}
					</div>
				</section>

				{/* Gamification Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[400px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						{[...Array(3)].map((_, i) => (
							<Card key={i} className="text-center p-6 animate-pulse">
								<div className="space-y-4">
									<div className="h-16 w-16 bg-muted rounded-full animate-pulse mx-auto" />
									<div className="h-6 w-32 bg-muted rounded animate-pulse mx-auto" />
									<div className="space-y-2">
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-2/3 bg-muted rounded animate-pulse mx-auto" />
									</div>
								</div>
							</Card>
						))}
					</div>
				</section>

				{/* Learning Path Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-56 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[350px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<div className="space-y-6">
						{[...Array(3)].map((_, i) => (
							<Card key={i} className="p-6 animate-pulse">
								<div className="flex items-center gap-6">
									<div className="h-16 w-16 bg-muted rounded-full animate-pulse flex-shrink-0" />
									<div className="flex-1 space-y-3">
										<div className="h-6 w-48 bg-muted rounded animate-pulse" />
										<div className="space-y-2">
											<div className="h-4 w-full bg-muted rounded animate-pulse" />
											<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
										</div>
									</div>
								</div>
							</Card>
						))}
					</div>
				</section>

				{/* Why Choose Section Skeleton */}
				<section className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 border border-primary/10">
					<div className="space-y-6">
						<div className="h-8 w-64 bg-muted rounded animate-pulse mx-auto" />
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
							{[...Array(3)].map((_, i) => (
								<div key={i} className="space-y-2">
									<div className="h-12 w-16 bg-muted rounded animate-pulse mx-auto" />
									<div className="h-4 w-32 bg-muted rounded animate-pulse mx-auto" />
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Feedback Section Skeleton */}
				<section className="space-y-8">
					<div className="text-center space-y-4">
						<div className="h-10 w-48 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-[300px] bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
					<Card className="max-w-2xl mx-auto p-6">
						<div className="space-y-4">
							<div className="h-32 w-full bg-muted rounded animate-pulse" />
							<div className="flex justify-end">
								<div className="h-10 w-24 bg-muted rounded animate-pulse" />
							</div>
						</div>
					</Card>
				</section>
			</div>
		</div>
	);
}
