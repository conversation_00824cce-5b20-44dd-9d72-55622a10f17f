'use client';

import {
	AppError,
	ErrorSeverity,
	getUserFriendlyMessage,
	isRetryableError,
} from '@/lib/error-handling';
import { AlertCircle, AlertTriangle, Info, RefreshCw, X, XCircle } from 'lucide-react';
import { useState } from 'react';
import { Translate } from '@/components/ui';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorDisplayProps {
	error: AppError | Error | null;
	onDismiss?: () => void;
	onRetry?: () => void;
	showDetails?: boolean;
	variant?: 'inline' | 'toast' | 'banner' | 'modal';
	className?: string;
}

interface ErrorListProps {
	errors: AppError[];
	onDismiss?: (errorId: string) => void;
	onRetry?: (error: AppError) => void;
	maxVisible?: number;
}

// ============================================================================
// ENHANCED ERROR DISPLAY
// ============================================================================

export function ErrorDisplay({
	error,
	onDismiss,
	onRetry,
	showDetails = false,
	variant = 'inline',
	className = '',
}: ErrorDisplayProps) {
	const [isExpanded, setIsExpanded] = useState(false);
	const [isRetrying, setIsRetrying] = useState(false);

	if (!error) return null;

	// Convert regular Error to AppError for consistent handling
	const appError =
		error instanceof AppError ? error : new AppError(error.message, 'UNKNOWN_ERROR');

	const message = error instanceof AppError ? getUserFriendlyMessage(error) : error.message;

	const canRetry = error instanceof AppError && isRetryableError(error) && onRetry;

	const handleRetry = async () => {
		if (!onRetry || isRetrying) return;

		setIsRetrying(true);
		try {
			await onRetry();
		} finally {
			setIsRetrying(false);
		}
	};

	const getErrorIcon = () => {
		if (!(error instanceof AppError)) {
			return <AlertCircle className="h-5 w-5" />;
		}

		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
				return <XCircle className="h-5 w-5" />;
			case ErrorSeverity.HIGH:
				return <AlertCircle className="h-5 w-5" />;
			case ErrorSeverity.MEDIUM:
				return <AlertTriangle className="h-5 w-5" />;
			case ErrorSeverity.LOW:
				return <Info className="h-5 w-5" />;
			default:
				return <AlertCircle className="h-5 w-5" />;
		}
	};

	const getErrorStyles = () => {
		const baseStyles = 'border rounded-md p-4';

		if (!(error instanceof AppError)) {
			return `${baseStyles} bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200`;
		}

		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
				return `${baseStyles} bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-700 text-red-900 dark:text-red-100`;
			case ErrorSeverity.HIGH:
				return `${baseStyles} bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200`;
			case ErrorSeverity.MEDIUM:
				return `${baseStyles} bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200`;
			case ErrorSeverity.LOW:
				return `${baseStyles} bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200`;
			default:
				return `${baseStyles} bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800 text-gray-800 dark:text-gray-200`;
		}
	};

	const variantStyles = {
		inline: '',
		toast: 'fixed top-4 right-4 z-50 max-w-md shadow-lg',
		banner: 'w-full',
		modal: 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50',
	};

	return (
		<div className={`${getErrorStyles()} ${variantStyles[variant]} ${className}`}>
			<div className="flex items-start">
				<div className="flex-shrink-0">{getErrorIcon()}</div>
				<div className="ml-3 flex-1">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<p className="text-sm font-medium">{message}</p>
							{error instanceof AppError && error.id && (
								<p className="text-xs opacity-75 mt-1">Error ID: {error.id}</p>
							)}
						</div>
						<div className="flex items-center space-x-2 ml-4">
							{canRetry && (
								<button
									onClick={handleRetry}
									disabled={isRetrying}
									className="inline-flex items-center text-xs font-medium hover:underline disabled:opacity-50"
								>
									<RefreshCw
										className={`h-3 w-3 mr-1 ${
											isRetrying ? 'animate-spin' : ''
										}`}
									/>
									{isRetrying ? 'Retrying...' : 'Retry'}
								</button>
							)}
							{onDismiss && (
								<button
									onClick={onDismiss}
									className="inline-flex items-center text-xs font-medium hover:underline"
								>
									<X className="h-3 w-3 mr-1" />
									<Translate text="ui.dismiss" />
								</button>
							)}
						</div>
					</div>

					{showDetails && error instanceof AppError && (
						<div className="mt-3">
							<button
								onClick={() => setIsExpanded(!isExpanded)}
								className="text-xs font-medium hover:underline"
							>
								{isExpanded ? 'Hide Details' : 'Show Details'}
							</button>
							{isExpanded && (
								<div className="mt-2 text-xs bg-black bg-opacity-10 dark:bg-white dark:bg-opacity-10 rounded p-2">
									<pre className="whitespace-pre-wrap overflow-auto">
										{JSON.stringify(appError.toLogObject(), null, 2)}
									</pre>
								</div>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

// ============================================================================
// ERROR LIST COMPONENT
// ============================================================================

export function ErrorList({ errors, onDismiss, onRetry, maxVisible = 5 }: ErrorListProps) {
	const [showAll, setShowAll] = useState(false);

	if (errors.length === 0) return null;

	const visibleErrors = showAll ? errors : errors.slice(0, maxVisible);
	const hasMore = errors.length > maxVisible;

	return (
		<div className="space-y-2">
			{visibleErrors.map((error) => (
				<ErrorDisplay
					key={error.id}
					error={error}
					onDismiss={onDismiss ? () => onDismiss(error.id) : undefined}
					onRetry={onRetry ? () => onRetry(error) : undefined}
					variant="inline"
				/>
			))}

			{hasMore && !showAll && (
				<button
					onClick={() => setShowAll(true)}
					className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
				>
					Show {errors.length - maxVisible} more errors
				</button>
			)}

			{showAll && hasMore && (
				<button
					onClick={() => setShowAll(false)}
					className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
				>
					Show fewer errors
				</button>
			)}
		</div>
	);
}
