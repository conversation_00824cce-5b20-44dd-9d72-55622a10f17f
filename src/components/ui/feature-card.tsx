'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON><PERSON>,
	CardTitle,
	Translate,
} from '@/components/ui';
import { LucideIcon, ArrowRight, Lock, CheckCircle2, AlertCircle } from 'lucide-react';
import Link from 'next/link';

interface SubFeature {
	titleKey: string;
	icon: LucideIcon;
	subLink: string;
	disabled?: boolean;
	disabledReason?: string;
}

interface FeatureCardProps {
	titleKey: string;
	descriptionKey: string;
	icon: LucideIcon;
	link: string;
	subFeatures?: SubFeature[];
	// New props for visual cues
	status?: 'available' | 'locked' | 'new' | 'recommended';
	badgeText?: string;
	wordCount?: number;
	requiredWords?: number;
	tutorialTarget?: string;
}

export function FeatureCard({
	titleKey,
	descriptionKey,
	icon: Icon,
	link,
	subFeatures,
	status = 'available',
	badgeText,
	wordCount = 0,
	requiredWords,
	tutorialTarget,
}: FeatureCardProps) {
	const isLocked = status === 'locked' || (requiredWords && wordCount < requiredWords);
	const isNew = status === 'new';
	const isRecommended = status === 'recommended';

	const getStatusColor = () => {
		if (isLocked) return 'border-muted bg-muted/20';
		if (isNew) return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
		if (isRecommended)
			return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20';
		return 'border-border/50 bg-gradient-to-br from-background to-muted/20';
	};

	const getIconColor = () => {
		if (isLocked) return 'bg-muted text-muted-foreground';
		if (isNew) return 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400';
		if (isRecommended)
			return 'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400';
		return 'bg-primary/10 border border-primary/20 text-primary';
	};

	return (
		<Card
			className={`
				group relative overflow-hidden transition-all duration-300 flex flex-col h-full
				${getStatusColor()}
				${!isLocked ? 'hover:shadow-xl hover:shadow-primary/5 hover:border-primary/30' : 'opacity-75'}
			`}
			data-tutorial={tutorialTarget}
		>
			{/* Background decoration */}
			{!isLocked && (
				<>
					<div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
					<div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary/10 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
				</>
			)}

			{/* Status badges */}
			{(badgeText || isNew || isRecommended || isLocked) && (
				<div className="absolute top-3 right-3 z-10">
					{isLocked && (
						<Badge variant="secondary" className="bg-muted text-muted-foreground">
							<Lock className="h-3 w-3 mr-1" />
							Locked
						</Badge>
					)}
					{isNew && (
						<Badge className="bg-green-100 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
							<CheckCircle2 className="h-3 w-3 mr-1" />
							New
						</Badge>
					)}
					{isRecommended && (
						<Badge className="bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-800">
							<AlertCircle className="h-3 w-3 mr-1" />
							Recommended
						</Badge>
					)}
					{badgeText && !isNew && !isRecommended && !isLocked && (
						<Badge variant="secondary">{badgeText}</Badge>
					)}
				</div>
			)}

			<CardHeader className="relative pb-4">
				<div className="flex items-center justify-between mb-3">
					<div className={`p-3 rounded-xl transition-all duration-300 ${getIconColor()}`}>
						<Icon
							className={`h-6 w-6 transition-transform duration-300 ${
								!isLocked ? 'group-hover:scale-110' : ''
							}`}
						/>
					</div>
				</div>
				<CardTitle
					className={`text-xl font-bold transition-colors duration-300 ${
						!isLocked ? 'group-hover:text-primary' : 'text-muted-foreground'
					}`}
				>
					<Translate text={titleKey} />
				</CardTitle>
				<p className="text-sm text-muted-foreground leading-relaxed">
					<Translate text={descriptionKey} />
				</p>

				{/* Progress indicator for locked features */}
				{isLocked && requiredWords && (
					<div className="mt-3 space-y-2">
						<div className="flex justify-between text-xs text-muted-foreground">
							<span>Progress</span>
							<span>
								{wordCount}/{requiredWords} words
							</span>
						</div>
						<div className="w-full bg-muted rounded-full h-1.5">
							<div
								className="bg-primary h-1.5 rounded-full transition-all duration-300"
								style={{
									width: `${Math.min((wordCount / requiredWords) * 100, 100)}%`,
								}}
							/>
						</div>
					</div>
				)}
			</CardHeader>

			<CardContent className="relative flex-grow flex flex-col">
				{subFeatures && subFeatures.length > 0 ? (
					<>
						<div className="flex flex-col gap-2 flex-grow">
							{subFeatures.map((sub, index) => {
								const isDisabled = sub.disabled;
								const content = (
									<div
										className={`group/item flex flex-col gap-2 p-3 rounded-lg transition-all duration-200 ${
											isDisabled
												? 'bg-muted/20 border border-muted/30 cursor-not-allowed opacity-60'
												: 'bg-muted/30 hover:bg-muted/50 border border-transparent hover:border-primary/20 cursor-pointer'
										}`}
										style={{ animationDelay: `${index * 50}ms` }}
										title={isDisabled ? sub.disabledReason : undefined}
									>
										<div className="flex items-center gap-3">
											<div
												className={`p-1.5 rounded-md border transition-all duration-200 ${
													isDisabled
														? 'bg-muted/50 border-muted/50'
														: 'bg-background border-border/50 group-hover/item:border-primary/30 group-hover/item:bg-primary/5'
												}`}
											>
												<sub.icon
													className={`h-4 w-4 transition-colors duration-200 ${
														isDisabled
															? 'text-muted-foreground/50'
															: 'text-muted-foreground group-hover/item:text-primary'
													}`}
												/>
											</div>
											<span
												className={`text-sm font-medium flex-1 transition-colors duration-200 ${
													isDisabled
														? 'text-muted-foreground/50'
														: 'group-hover/item:text-primary'
												}`}
											>
												<Translate text={sub.titleKey} />
											</span>
											<ArrowRight
												className={`h-4 w-4 transition-all duration-200 ${
													isDisabled
														? 'text-muted-foreground/30'
														: 'text-muted-foreground/50 group-hover/item:text-primary group-hover/item:translate-x-1'
												}`}
											/>
										</div>
										{isDisabled && sub.disabledReason && (
											<div className="flex items-center gap-2 ml-11">
												<Lock className="h-3 w-3 text-muted-foreground/50" />
												<span className="text-xs text-muted-foreground/70">
													{sub.disabledReason}
												</span>
											</div>
										)}
									</div>
								);

								return isDisabled ? (
									<div
										key={sub.titleKey}
										data-testid={`sub-feature-${sub.titleKey}`}
										data-disabled="true"
										data-disabled-reason={sub.disabledReason}
									>
										{content}
									</div>
								) : (
									<Link
										key={sub.titleKey}
										href={`${link}${sub.subLink}`}
										passHref
										data-testid={`sub-feature-${sub.titleKey}`}
									>
										{content}
									</Link>
								);
							})}
						</div>
					</>
				) : (
					<div className="mt-auto">
						{isLocked ? (
							<Button
								disabled
								className="w-full bg-muted text-muted-foreground cursor-not-allowed"
							>
								<Lock className="mr-2 h-4 w-4" />
								<Translate text="collections.overview.go_to_section" />
							</Button>
						) : (
							<Link href={link} passHref>
								<Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground group-hover:shadow-lg group-hover:shadow-primary/25 transition-all duration-300">
									<Translate text="collections.overview.go_to_section" />
									<ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
								</Button>
							</Link>
						)}
					</div>
				)}
			</CardContent>
		</Card>
	);
}
