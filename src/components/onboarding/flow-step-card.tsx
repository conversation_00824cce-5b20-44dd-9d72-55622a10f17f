'use client';

import { <PERSON><PERSON>, Card, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { Lock } from 'lucide-react';
import { LucideIcon } from 'lucide-react';

interface FlowStepCardProps {
	stepNumber: number;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'available' | 'locked';
	statusMessage?: string;
	statusValues?: Record<string, any>;
	children: React.ReactNode;
	className?: string;
}

export function FlowStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	children,
	className = '',
}: FlowStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'available':
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					badge: null,
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					badge: { text: 'Locked', color: 'bg-muted text-muted-foreground' },
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					badge: null,
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: stepNumber * 0.1 }}
			className={className}
		>
			<Card
				className={`
				relative overflow-hidden transition-all duration-300
				${config.borderColor} ${config.bgColor}
				${status === 'available' ? 'hover:shadow-md' : ''}
			`}
			>
				{/* Step indicator */}
				<div className="absolute top-3 sm:top-4 left-3 sm:left-4 z-10">
					<div
						className={`
						w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center
						${config.stepBg}
					`}
					>
						{status === 'available' ? (
							<StepIcon className={`h-4 w-4 sm:h-5 sm:w-5 ${config.stepIconColor}`} />
						) : (
							<span className="text-xs sm:text-sm font-bold text-white">
								{stepNumber}
							</span>
						)}
					</div>
				</div>

				{/* Status badge */}
				{config.badge && (
					<div className="absolute top-3 sm:top-4 right-3 sm:right-4 z-10">
						<Badge className={`text-xs ${config.badge.color}`}>
							{config.badge.text}
						</Badge>
					</div>
				)}

				<CardContent className="pt-12 sm:pt-16 pb-4 sm:pb-6 px-3 sm:px-6">
					{/* Header */}
					<div className="mb-3 sm:mb-4">
						<h3
							className={`text-lg sm:text-xl font-bold mb-1 sm:mb-2 ${config.titleColor}`}
						>
							<Translate text={titleKey} />
						</h3>
						<p className={`text-sm leading-relaxed ${config.messageColor}`}>
							<Translate text={messageKey} />
						</p>
					</div>

					{/* Feature content */}
					<div
						className={`
						${status === 'locked' ? 'opacity-60 pointer-events-none' : ''}
					`}
					>
						{children}
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
