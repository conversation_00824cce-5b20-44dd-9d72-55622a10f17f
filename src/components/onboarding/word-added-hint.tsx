'use client';

import { Badge, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { RefreshCw, Target, CheckCircle2 } from 'lucide-react';
import Link from 'next/link';

interface WordAddedHintProps {
	collectionId: string;
	isFirstWord?: boolean;
	canTakeQuiz?: boolean;
	className?: string;
}

export function WordAddedHint({
	collectionId,
	isFirstWord = false,
	canTakeQuiz = false,
	className = '',
}: WordAddedHintProps) {
	if (!isFirstWord && !canTakeQuiz) return null;

	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.9 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.9 }}
			transition={{ duration: 0.3 }}
			className={`mt-3 p-3 rounded-lg bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800 ${className}`}
		>
			<div className="flex items-center gap-2 mb-2">
				<CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
				<span className="text-sm font-medium text-green-800 dark:text-green-200">
					{isFirstWord ? (
						<Translate text="words.progress_milestone.first_word" />
					) : canTakeQuiz ? (
						<Translate text="words.progress_milestone.quiz_unlocked" />
					) : (
						<Translate text="words.success_banner.ready_to_review" />
					)}
				</span>
			</div>

			<div className="flex flex-wrap gap-2">
				<Link href={`/collections/${collectionId}/vocabulary/review`}>
					<Badge
						variant="secondary"
						className="bg-green-100 text-green-700 border-green-300 hover:bg-green-200 transition-colors cursor-pointer dark:bg-green-900/30 dark:text-green-300 dark:border-green-700"
					>
						<RefreshCw className="h-3 w-3 mr-1" />
						<Translate text="words.next_steps.review_words" />
					</Badge>
				</Link>

				{canTakeQuiz && (
					<Link href={`/collections/${collectionId}/vocabulary/mcq`}>
						<Badge
							variant="secondary"
							className="bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 transition-colors cursor-pointer dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700"
						>
							<Target className="h-3 w-3 mr-1" />
							<Translate text="words.next_steps.take_quiz" />
						</Badge>
					</Link>
				)}
			</div>
		</motion.div>
	);
}
