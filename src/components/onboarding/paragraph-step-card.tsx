'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { Lock, ArrowRight } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface ParagraphStepCardProps {
	stepNumber: string;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'available' | 'locked';
	href: string;
	className?: string;
	lockedReason?: string;
}

export function ParagraphStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	href,
	className = '',
	lockedReason,
}: ParagraphStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'available':
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					buttonVariant: 'outline' as const,
					disabled: false,
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					buttonVariant: 'outline' as const,
					disabled: true,
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					buttonVariant: 'outline' as const,
					disabled: false,
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	const content = (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className={className}
		>
			<Card
				className={`
				relative overflow-hidden transition-all duration-300 group
				${config.borderColor} ${config.bgColor}
				${!config.disabled ? 'hover:shadow-md cursor-pointer' : 'opacity-75'}
			`}
			>
				<CardContent className="p-3 sm:p-4">
					<div className="flex items-center gap-2 sm:gap-4">
						{/* Step indicator */}
						<div
							className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full ${config.stepBg} flex items-center justify-center`}
						>
							<StepIcon className={`h-4 w-4 sm:h-5 sm:w-5 ${config.stepIconColor}`} />
						</div>

						{/* Content */}
						<div className="flex-1 min-w-0">
							<h3 className={`font-semibold text-sm ${config.titleColor}`}>
								<Translate text={titleKey} />
							</h3>
							<p className={`text-xs ${config.messageColor} mt-1`}>
								<Translate text={messageKey} />
							</p>
							{status === 'locked' && lockedReason && (
								<div className="flex items-center gap-1 mt-1">
									<Lock className="h-3 w-3 text-muted-foreground/50" />
									<span className="text-xs text-muted-foreground/70">
										{lockedReason}
									</span>
								</div>
							)}
						</div>

						{/* Action button */}
						<div className="flex-shrink-0">
							<Button
								variant={config.buttonVariant}
								size="sm"
								disabled={config.disabled}
								className={`
									transition-all duration-200 text-xs sm:text-sm
									${!config.disabled ? 'group-hover:translate-x-1' : ''}
								`}
							>
								{!config.disabled && (
									<ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
								)}
								{status === 'locked' && <Lock className="h-3 w-3 sm:h-4 sm:w-4" />}
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);

	if (config.disabled) {
		return content;
	}

	return (
		<Link href={href} className="block">
			{content}
		</Link>
	);
}
