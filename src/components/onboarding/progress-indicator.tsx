'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { Trophy, Target, BookOpen, Zap, Star, Award, TrendingUp } from 'lucide-react';

interface Milestone {
	id: string;
	threshold: number;
	titleKey: string;
	descriptionKey: string;
	icon: React.ComponentType<any>;
	badgeKey?: string;
	color: string;
}

interface ProgressIndicatorProps {
	wordCount: number;
	reviewCount: number;
	quizCount: number;
	showBadges?: boolean;
}

export function ProgressIndicator({
	wordCount,
	reviewCount,
	quizCount,
	showBadges = true,
}: ProgressIndicatorProps) {
	const { t } = useTranslation();

	const milestones: Milestone[] = [
		{
			id: 'first-words',
			threshold: 1,
			titleKey: 'collections.badge.first_words',
			descriptionKey: 'collections.quick_start.step1_desc',
			icon: BookOpen,
			badgeKey: 'collections.badge.first_words',
			color: 'text-blue-600',
		},
		{
			id: 'quiz-ready',
			threshold: 10,
			titleKey: 'collections.badge.quiz_ready',
			descriptionKey: 'collections.progress.milestone_10_words',
			icon: Target,
			badgeKey: 'collections.badge.quiz_ready',
			color: 'text-green-600',
		},
		{
			id: 'advanced-features',
			threshold: 50,
			titleKey: 'collections.status.active_learner',
			descriptionKey: 'collections.progress.milestone_50_words',
			icon: Star,
			color: 'text-purple-600',
		},
		{
			id: 'review-master',
			threshold: 100,
			titleKey: 'collections.badge.review_master',
			descriptionKey: 'collections.quick_start.step3_desc',
			icon: Award,
			badgeKey: 'collections.badge.review_master',
			color: 'text-orange-600',
		},
	];

	const currentMilestone =
		milestones.find((m) => wordCount < m.threshold) || milestones[milestones.length - 1];
	const completedMilestones = milestones.filter((m) => wordCount >= m.threshold);
	const nextMilestone = milestones.find((m) => wordCount < m.threshold);

	const progressToNext = nextMilestone
		? Math.min((wordCount / nextMilestone.threshold) * 100, 100)
		: 100;

	const userLevel = getUserLevel(wordCount, reviewCount, quizCount);

	return (
		<div className="space-y-6">
			{/* Current Status Card */}
			<Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-background">
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-3">
						<div className="p-2 rounded-lg bg-primary/10">
							<TrendingUp className="h-5 w-5 text-primary" />
						</div>
						<Translate text="collections.progress.title" />
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* User Level Badge */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<div className={`p-2 rounded-lg ${userLevel.bgColor}`}>
								<userLevel.icon className={`h-5 w-5 ${userLevel.color}`} />
							</div>
							<div>
								<h3 className="font-medium">
									<Translate text={userLevel.titleKey} />
								</h3>
								<p className="text-sm text-muted-foreground">
									{wordCount} words • {reviewCount} reviews • {quizCount} quizzes
								</p>
							</div>
						</div>
						<Badge variant="secondary" className={userLevel.color}>
							<Translate text={userLevel.statusKey} />
						</Badge>
					</div>

					{/* Progress to Next Milestone */}
					{nextMilestone && (
						<div className="space-y-3">
							<div className="flex items-center justify-between text-sm">
								<span className="text-muted-foreground">
									<Translate text="collections.progress.next_milestone" />
								</span>
								<span className="font-medium">
									{wordCount}/{nextMilestone.threshold} words
								</span>
							</div>
							<div className="w-full bg-muted rounded-full h-3">
								<motion.div
									className="bg-gradient-to-r from-primary to-primary/80 h-3 rounded-full"
									initial={{ width: 0 }}
									animate={{ width: `${progressToNext}%` }}
									transition={{ duration: 0.8, ease: 'easeOut' }}
								/>
							</div>
							<p className="text-sm text-muted-foreground">
								<Translate text={nextMilestone.descriptionKey} />
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Milestones Grid */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				{milestones.map((milestone, index) => {
					const isCompleted = wordCount >= milestone.threshold;
					const isCurrent = milestone.id === currentMilestone?.id;
					const Icon = milestone.icon;

					return (
						<motion.div
							key={milestone.id}
							initial={{ opacity: 0, scale: 0.9 }}
							animate={{ opacity: 1, scale: 1 }}
							transition={{ delay: index * 0.1 }}
						>
							<Card
								className={`
								relative overflow-hidden transition-all duration-300
								${
									isCompleted
										? 'border-primary/30 bg-gradient-to-br from-primary/10 to-primary/5 shadow-lg shadow-primary/10'
										: isCurrent
											? 'border-orange-300 bg-gradient-to-br from-orange-50 to-background dark:from-orange-900/20 dark:border-orange-600'
											: 'border-muted bg-muted/30'
								}
							`}
							>
								{/* Completion indicator */}
								{isCompleted && (
									<div className="absolute top-2 right-2">
										<div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center">
											<Trophy className="h-3 w-3 text-primary-foreground" />
										</div>
									</div>
								)}

								<CardContent className="p-4 text-center">
									<div
										className={`
										w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center
										${isCompleted ? 'bg-primary/20' : isCurrent ? 'bg-orange-100 dark:bg-orange-900/30' : 'bg-muted'}
									`}
									>
										<Icon
											className={`
											h-6 w-6
											${
												isCompleted
													? 'text-primary'
													: isCurrent
														? 'text-orange-600 dark:text-orange-400'
														: 'text-muted-foreground'
											}
										`}
										/>
									</div>
									<h4
										className={`
										text-sm font-medium mb-1
										${
											isCompleted
												? 'text-primary'
												: isCurrent
													? 'text-orange-600 dark:text-orange-400'
													: 'text-muted-foreground'
										}
									`}
									>
										<Translate text={milestone.titleKey} />
									</h4>
									<p className="text-xs text-muted-foreground">
										{milestone.threshold} words
									</p>
									{isCompleted && milestone.badgeKey && showBadges && (
										<Badge
											variant="secondary"
											className="mt-2 text-xs bg-primary/10 text-primary border-primary/20"
										>
											<Translate text={milestone.badgeKey} />
										</Badge>
									)}
								</CardContent>
							</Card>
						</motion.div>
					);
				})}
			</div>
		</div>
	);
}

function getUserLevel(wordCount: number, reviewCount: number, quizCount: number) {
	if (wordCount === 0) {
		return {
			titleKey: 'collections.status.new_user',
			statusKey: 'collections.status.new_user',
			icon: BookOpen,
			color: 'text-blue-600',
			bgColor: 'bg-blue-100 dark:bg-blue-900/30',
		};
	}

	if (wordCount < 10) {
		return {
			titleKey: 'collections.status.getting_started',
			statusKey: 'collections.status.getting_started',
			icon: Zap,
			color: 'text-orange-600',
			bgColor: 'bg-orange-100 dark:bg-orange-900/30',
		};
	}

	return {
		titleKey: 'collections.status.active_learner',
		statusKey: 'collections.status.active_learner',
		icon: Star,
		color: 'text-purple-600',
		bgColor: 'bg-purple-100 dark:bg-purple-900/30',
	};
}
