'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { BookOpen, Sparkles, Target, X, ArrowRight, Zap, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface WelcomeBannerProps {
	collectionId: string;
	wordCount: number;
	onDismiss?: () => void;
	isFirstTime?: boolean;
}

export function WelcomeBanner({
	collectionId,
	wordCount,
	onDismiss,
	isFirstTime = false,
}: WelcomeBannerProps) {
	const [isVisible, setIsVisible] = useState(true);

	const handleDismiss = () => {
		setIsVisible(false);
		onDismiss?.();
	};

	if (!isVisible) return null;

	const quickActions = [
		{
			icon: Zap,
			titleKey: 'collections.tabs.generate_words',
			href: `/collections/${collectionId}/vocabulary/generate`,
			variant: 'default' as const,
			primary: true,
		},
		{
			icon: Book<PERSON><PERSON>,
			titleKey: 'collections.tabs.my_words_list',
			href: `/collections/${collectionId}/vocabulary/my-words`,
			variant: 'outline' as const,
		},
		{
			icon: TrendingUp,
			titleKey: 'collections.stats.title',
			href: `/collections/${collectionId}/stats`,
			variant: 'ghost' as const,
		},
	];

	return (
		<motion.div
			initial={{ opacity: 0, y: -20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, y: -20 }}
			transition={{ duration: 0.5 }}
			className="mb-8"
		>
			<Card className="relative overflow-hidden border-primary/20 bg-gradient-to-br from-primary/5 via-background to-primary/5">
				{/* Background decoration */}
				<div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/10 to-transparent rounded-full blur-2xl" />
				<div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-secondary/10 to-transparent rounded-full blur-xl" />

				{/* Dismiss button */}
				<Button
					variant="ghost"
					size="sm"
					onClick={handleDismiss}
					className="absolute top-4 right-4 h-8 w-8 p-0 hover:bg-primary/10"
				>
					<X className="h-4 w-4" />
				</Button>

				<CardContent className="relative p-8">
					<div className="flex items-start gap-6">
						{/* Welcome icon */}
						<div className="flex-shrink-0">
							<div className="p-4 rounded-2xl bg-primary/10 border border-primary/20">
								<Sparkles className="h-8 w-8 text-primary" />
							</div>
						</div>

						{/* Content */}
						<div className="flex-1 space-y-4">
							<div>
								<h2 className="text-2xl font-bold text-foreground mb-2">
									<Translate text="collections.welcome.title" />
								</h2>
								<p className="text-muted-foreground text-lg">
									<Translate text="collections.welcome.subtitle" />
								</p>
								{isFirstTime && (
									<p className="text-sm text-primary mt-2 font-medium">
										<Translate text="collections.welcome.first_time_message" />
									</p>
								)}
							</div>

							{/* Quick actions */}
							<div className="flex flex-wrap gap-3">
								{quickActions.map((action) => (
									<Link key={action.titleKey} href={action.href}>
										<Button
											variant={action.variant}
											size="sm"
											className={`
												flex items-center gap-2 transition-all duration-200
												${
													action.primary
														? 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg shadow-primary/25'
														: ''
												}
											`}
										>
											<action.icon className="h-4 w-4" />
											<Translate text={action.titleKey} />
											{action.primary && <ArrowRight className="h-4 w-4" />}
										</Button>
									</Link>
								))}
							</div>

							{/* Progress hint */}
							{wordCount === 0 && (
								<div className="flex items-center gap-2 text-sm text-muted-foreground">
									<Target className="h-4 w-4" />
									<span>
										<Translate text="collections.progress.milestone_10_words" />
									</span>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
