'use client';

import { useToast } from '@/contexts/toast-context';
import { AppError, getUserFriendlyMessage } from '@/lib/error-handling';
import { AlertCircle, RefreshCw, WifiOff } from 'lucide-react';
import Image from 'next/image';
import { ReactNode, useCallback, useEffect, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface FallbackProps {
	children?: ReactNode;
	fallback?: ReactNode;
	error?: AppError | Error | null;
	onRetry?: () => void | Promise<void>;
	className?: string;
}

interface DataFallbackProps extends FallbackProps {
	isLoading?: boolean;
	isEmpty?: boolean;
	emptyMessage?: string;
	emptyIcon?: ReactNode;
}

interface ImageFallbackProps {
	src: string;
	alt: string;
	fallbackSrc?: string;
	placeholder?: ReactNode;
	className?: string;
	onError?: (error: Event) => void;
}

// ============================================================================
// GENERIC FALLBACK WRAPPER
// ============================================================================

export function FallbackWrapper({
	children,
	fallback,
	error,
	onRetry,
	className = '',
}: FallbackProps) {
	const [isRetrying, setIsRetrying] = useState(false);

	const handleRetry = useCallback(async () => {
		if (!onRetry || isRetrying) return;

		setIsRetrying(true);
		try {
			await onRetry();
		} finally {
			setIsRetrying(false);
		}
	}, [onRetry, isRetrying]);

	if (error) {
		if (fallback) {
			return <div className={className}>{fallback}</div>;
		}

		const message = error instanceof AppError ? getUserFriendlyMessage(error) : error.message;

		return (
			<div
				className={`flex flex-col items-center justify-center p-6 text-center ${className}`}
			>
				<AlertCircle className="h-8 w-8 text-gray-400 mb-3" />
				<p className="text-gray-600 dark:text-gray-300 mb-4">{message}</p>
				{onRetry && (
					<button
						onClick={handleRetry}
						disabled={isRetrying}
						className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md transition-colors"
					>
						<RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
						{isRetrying ? 'Retrying...' : 'Try Again'}
					</button>
				)}
			</div>
		);
	}

	return <div className={className}>{children}</div>;
}

// ============================================================================
// DATA FALLBACK COMPONENT
// ============================================================================

export function DataFallback({
	children,
	fallback,
	error,
	onRetry,
	isLoading = false,
	isEmpty = false,
	emptyMessage = 'No data available',
	emptyIcon,
	className = '',
}: DataFallbackProps) {
	// Show loading state
	if (isLoading) {
		return (
			<div className={`flex items-center justify-center p-6 ${className}`}>
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	// Show error state
	if (error) {
		return (
			<FallbackWrapper
				error={error}
				onRetry={onRetry}
				fallback={fallback}
				className={className}
			/>
		);
	}

	// Show empty state
	if (isEmpty) {
		return (
			<div
				className={`flex flex-col items-center justify-center p-6 text-center ${className}`}
			>
				{emptyIcon || (
					<div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
				)}
				<p className="text-gray-500 dark:text-gray-400">{emptyMessage}</p>
			</div>
		);
	}

	// Show children (success state)
	return <div className={className}>{children}</div>;
}

// ============================================================================
// IMAGE FALLBACK COMPONENT
// ============================================================================

export function ImageFallback({
	src,
	alt,
	fallbackSrc,
	placeholder,
	className = '',
	onError,
}: ImageFallbackProps) {
	const [currentSrc, setCurrentSrc] = useState(src);
	const [hasError, setHasError] = useState(false);
	const [isLoading, setIsLoading] = useState(true);

	const handleError = useCallback(
		(event: React.SyntheticEvent<HTMLImageElement, Event>) => {
			if (onError) {
				onError(event.nativeEvent);
			}

			// Try fallback source if available and not already tried
			if (fallbackSrc && currentSrc !== fallbackSrc) {
				setCurrentSrc(fallbackSrc);
				return;
			}

			// No fallback available or fallback also failed
			setHasError(true);
			setIsLoading(false);
		},
		[onError, fallbackSrc, currentSrc]
	);

	const handleLoad = useCallback(() => {
		setIsLoading(false);
		setHasError(false);
	}, []);

	// Show placeholder while loading
	if (isLoading && !hasError) {
		return (
			<div className={`bg-gray-200 dark:bg-gray-700 animate-pulse ${className}`}>
				{placeholder || (
					<div className="flex items-center justify-center h-full">
						<div className="h-8 w-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
					</div>
				)}
				<Image
					src={currentSrc}
					alt={alt}
					onLoad={handleLoad}
					onError={handleError}
					className="hidden"
					width={100}
					height={100}
				/>
			</div>
		);
	}

	// Show error state
	if (hasError) {
		return (
			<div
				className={`bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${className}`}
			>
				<div className="text-center p-4">
					<AlertCircle className="h-6 w-6 text-gray-400 mx-auto mb-2" />
					<p className="text-xs text-gray-500">Failed to load image</p>
				</div>
			</div>
		);
	}

	// Show successful image
	return (
		<Image
			src={currentSrc}
			alt={alt}
			onError={handleError}
			onLoad={handleLoad}
			className={className}
			width={100}
			height={100}
		/>
	);
}

// ============================================================================
// NETWORK STATUS FALLBACK
// ============================================================================

interface NetworkFallbackProps {
	children: ReactNode;
	offlineMessage?: string;
	onRetry?: () => void;
}

export function NetworkFallback({
	children,
	offlineMessage = 'You appear to be offline. Some features may not be available.',
	onRetry,
}: NetworkFallbackProps) {
	const [isOnline, setIsOnline] = useState(true); // Always start with true for SSR
	const [isHydrated, setIsHydrated] = useState(false);
	const { showSuccess, showWarning } = useToast();

	// Listen for online/offline events
	useEffect(() => {
		// Set the actual network status after hydration
		setIsHydrated(true);
		setIsOnline(navigator.onLine);

		const handleOnline = () => {
			setIsOnline(true);
			if (isHydrated) {
				showSuccess('Connection restored', { duration: 3000 });
			}
		};

		const handleOffline = () => {
			setIsOnline(false);
			if (isHydrated) {
				showWarning(
					'Connection lost',
					'You appear to be offline. Some features may not be available.',
					0
				);
			}
		};

		window.addEventListener('online', handleOnline);
		window.addEventListener('offline', handleOffline);

		return () => {
			window.removeEventListener('online', handleOnline);
			window.removeEventListener('offline', handleOffline);
		};
	}, [isHydrated]);

	// During SSR or before hydration, always show online state
	if (!isHydrated || isOnline) {
		return <>{children}</>;
	}

	// Show offline state only after hydration and when actually offline
	return (
		<div className="relative">
			{/* Offline Banner */}
			<div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 p-3">
				<div className="flex items-center justify-between">
					<div className="flex items-center">
						<WifiOff className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
						<p className="text-sm text-yellow-800 dark:text-yellow-200">
							{offlineMessage}
						</p>
					</div>
					{onRetry && (
						<button
							onClick={onRetry}
							className="text-sm text-yellow-800 dark:text-yellow-200 hover:underline"
						>
							Retry
						</button>
					)}
				</div>
			</div>

			{/* Dimmed content */}
			<div className="opacity-50 pointer-events-none">{children}</div>
		</div>
	);
}

// ============================================================================
// FEATURE FALLBACK (for progressive enhancement)
// ============================================================================

interface FeatureFallbackProps {
	children: ReactNode;
	fallback: ReactNode;
	condition: boolean;
	featureName?: string;
}

export function FeatureFallback({
	children,
	fallback,
	condition,
	featureName,
}: FeatureFallbackProps) {
	if (!condition) {
		if (featureName && process.env.NODE_ENV === 'development') {
			console.warn(`Feature "${featureName}" is not available, showing fallback`);
		}
		return <>{fallback}</>;
	}

	return <>{children}</>;
}

// ============================================================================
// LAZY LOADING FALLBACK
// ============================================================================

interface LazyFallbackProps {
	children: ReactNode;
	fallback?: ReactNode;
	error?: Error | null;
	onRetry?: () => void;
}

export function LazyFallback({ children, fallback, error, onRetry }: LazyFallbackProps) {
	if (error) {
		return <FallbackWrapper error={error} onRetry={onRetry} fallback={fallback} />;
	}

	// Default loading fallback
	if (!children) {
		return (
			fallback || (
				<div className="flex items-center justify-center p-6">
					<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				</div>
			)
		);
	}

	return <>{children}</>;
}
