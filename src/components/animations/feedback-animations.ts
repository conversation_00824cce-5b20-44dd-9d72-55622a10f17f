import { Variants } from 'framer-motion';

// Feedback modal animations
export const feedbackModalVariants: Variants = {
	hidden: {
		opacity: 0,
		scale: 0.9,
		y: 20,
	},
	visible: {
		opacity: 1,
		scale: 1,
		y: 0,
		transition: {
			duration: 0.3,
			ease: 'easeOut',
			staggerChildren: 0.1,
		},
	},
	exit: {
		opacity: 0,
		scale: 0.9,
		y: 20,
		transition: {
			duration: 0.2,
			ease: 'easeIn',
		},
	},
};

// Feedback form animations
export const feedbackFormVariants: Variants = {
	hidden: {
		opacity: 0,
		y: 10,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
			staggerChildren: 0.05,
		},
	},
};

// Feedback field animations
export const feedbackFieldVariants: Variants = {
	hidden: {
		opacity: 0,
		y: 5,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.15,
			ease: 'easeOut',
		},
	},
	focus: {
		scale: 1.01,
		transition: {
			duration: 0.1,
		},
	},
};

// Feedback button animations
export const feedbackButtonVariants: Variants = {
	idle: {
		scale: 1,
	},
	hover: {
		scale: 1.02,
		transition: {
			duration: 0.1,
		},
	},
	tap: {
		scale: 0.98,
		transition: {
			duration: 0.05,
		},
	},
	loading: {
		scale: 1,
		opacity: 0.7,
		transition: {
			duration: 0.2,
		},
	},
};

// Success/Error state animations
export const feedbackStateVariants: Variants = {
	hidden: {
		opacity: 0,
		scale: 0.8,
		y: 10,
	},
	visible: {
		opacity: 1,
		scale: 1,
		y: 0,
		transition: {
			duration: 0.3,
			ease: 'easeOut',
		},
	},
	exit: {
		opacity: 0,
		scale: 0.8,
		y: -10,
		transition: {
			duration: 0.2,
		},
	},
};

// Floating feedback button animations
export const floatingFeedbackButtonVariants: Variants = {
	idle: {
		scale: 1,
		rotate: 0,
	},
	hover: {
		scale: 1.1,
		rotate: 5,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
	tap: {
		scale: 0.9,
		transition: {
			duration: 0.1,
		},
	},
};

// Feedback backdrop animations
export const feedbackBackdropVariants: Variants = {
	hidden: {
		opacity: 0,
	},
	visible: {
		opacity: 1,
		transition: {
			duration: 0.2,
		},
	},
	exit: {
		opacity: 0,
		transition: {
			duration: 0.15,
		},
	},
};
