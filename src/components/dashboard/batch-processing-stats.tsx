'use client';

import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Progress,
} from '@/components/ui';
import { BatchStats } from '@/lib/api/token-monitor.api';
import { Activity, CheckCircle, Clock, Layers, Settings, Zap } from 'lucide-react';

interface BatchProcessingStatsProps {
	batchStats: BatchStats;
}

export function BatchProcessingStats({ batchStats }: BatchProcessingStatsProps) {
	const isProcessing = batchStats.processing;
	const queueLength = batchStats.queueLength;
	const maxBatchSizes = batchStats.config.maxBatchSize;
	const maxWaitTime = batchStats.config.maxWaitTime;
	const maxTokensPerBatch = batchStats.config.maxTokensPerBatch;

	// Calculate efficiency metrics
	const totalMaxBatchSize = Object.values(maxBatchSizes).reduce((sum, size) => sum + size, 0);
	const averageBatchSize = totalMaxBatchSize / Object.keys(maxBatchSizes).length;

	return (
		<div className="space-y-4">
			{/* Batch Processing Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Queue Status</CardTitle>
						<Activity className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="flex items-center space-x-2">
							<div className="text-2xl font-bold">{queueLength}</div>
							<Badge variant={isProcessing ? 'default' : 'secondary'}>
								{isProcessing ? 'Processing' : 'Idle'}
							</Badge>
						</div>
						<p className="text-xs text-muted-foreground">Requests in queue</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Max Wait Time</CardTitle>
						<Clock className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{maxWaitTime / 1000}s</div>
						<p className="text-xs text-muted-foreground">Before batch processing</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Token Limit</CardTitle>
						<Zap className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{(maxTokensPerBatch / 1000).toFixed(1)}K
						</div>
						<p className="text-xs text-muted-foreground">Tokens per batch</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Avg Batch Size</CardTitle>
						<Layers className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{averageBatchSize.toFixed(1)}</div>
						<p className="text-xs text-muted-foreground">Requests per batch</p>
					</CardContent>
				</Card>
			</div>

			{/* Current Queue Status */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Activity className="h-5 w-5 mr-2" />
						Current Queue Status
					</CardTitle>
					<CardDescription>Real-time batch processing queue information</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium">Processing Status</span>
							<div className="flex items-center space-x-2">
								<div
									className={`w-2 h-2 rounded-full ${
										isProcessing ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
									}`}
								/>
								<span className="text-sm">{isProcessing ? 'Active' : 'Idle'}</span>
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex justify-between text-sm">
								<span>Queue Length</span>
								<span>{queueLength} requests</span>
							</div>
							<Progress
								value={
									queueLength > 0 ? Math.min((queueLength / 20) * 100, 100) : 0
								}
								className="h-2"
							/>
							<p className="text-xs text-muted-foreground">
								{queueLength === 0
									? 'Queue is empty'
									: queueLength < 5
									? 'Low queue volume'
									: queueLength < 15
									? 'Moderate queue volume'
									: 'High queue volume'}
							</p>
						</div>

						{queueLength === 0 && !isProcessing && (
							<div className="flex items-center justify-center py-8 text-muted-foreground">
								<CheckCircle className="h-8 w-8 mr-2" />
								<span>No requests in queue - system is idle</span>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Batch Configuration */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Settings className="h-5 w-5 mr-2" />
						Batch Configuration
					</CardTitle>
					<CardDescription>
						Current batch processing settings for different operations
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{Object.entries(maxBatchSizes).map(([operation, batchSize]) => {
							const operationName = operation.replace(/([A-Z])/g, ' $1').trim();
							const efficiency = (batchSize / 20) * 100; // Assuming max possible is 20

							return (
								<div key={operation} className="space-y-2">
									<div className="flex justify-between items-center">
										<span className="text-sm font-medium">{operationName}</span>
										<div className="flex items-center space-x-2">
											<span className="text-sm">{batchSize} requests</span>
											<Badge
												variant={
													batchSize >= 15
														? 'default'
														: batchSize >= 10
														? 'secondary'
														: 'outline'
												}
											>
												{batchSize >= 15
													? 'High'
													: batchSize >= 10
													? 'Medium'
													: 'Low'}{' '}
												Efficiency
											</Badge>
										</div>
									</div>
									<Progress value={efficiency} className="h-1" />
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>

			{/* Batch Processing Benefits */}
			<Card>
				<CardHeader>
					<CardTitle>Batch Processing Benefits</CardTitle>
					<CardDescription>How batch processing optimizes your API usage</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-3">
							<h4 className="font-medium">Efficiency Gains</h4>
							{Object.entries(maxBatchSizes).map(([operation, batchSize]) => {
								const reduction = ((batchSize - 1) / batchSize) * 100;
								return (
									<div key={operation} className="flex justify-between text-sm">
										<span>{operation.replace(/([A-Z])/g, ' $1').trim()}</span>
										<span className="font-medium text-green-600">
											{reduction.toFixed(0)}% reduction
										</span>
									</div>
								);
							})}
						</div>

						<div className="space-y-3">
							<h4 className="font-medium">Performance Metrics</h4>
							<div className="space-y-2">
								<div className="flex justify-between text-sm">
									<span>Max Wait Time</span>
									<span>{maxWaitTime / 1000}s</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Token Limit</span>
									<span>{maxTokensPerBatch.toLocaleString()}</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Operations Supported</span>
									<span>{Object.keys(maxBatchSizes).length}</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Average Batch Size</span>
									<span>{averageBatchSize.toFixed(1)}</span>
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Optimization Recommendations */}
			<Card>
				<CardHeader>
					<CardTitle>Optimization Recommendations</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{queueLength > 15 && (
							<div className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
								<Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-yellow-800 dark:text-yellow-200">
										High Queue Volume
									</h4>
									<p className="text-sm text-yellow-700 dark:text-yellow-300">
										Consider reducing max wait time or increasing batch sizes
										for better throughput.
									</p>
								</div>
							</div>
						)}

						{averageBatchSize < 5 && (
							<div className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
								<Layers className="h-5 w-5 text-blue-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-blue-800 dark:text-blue-200">
										Low Batch Efficiency
									</h4>
									<p className="text-sm text-blue-700 dark:text-blue-300">
										Increase batch sizes for operations to improve API call
										efficiency.
									</p>
								</div>
							</div>
						)}

						{queueLength === 0 && !isProcessing && averageBatchSize >= 10 && (
							<div className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-950 rounded-lg">
								<CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-green-800 dark:text-green-200">
										Optimal Performance
									</h4>
									<p className="text-sm text-green-700 dark:text-green-300">
										Batch processing is running efficiently with good batch
										sizes and low queue times.
									</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
