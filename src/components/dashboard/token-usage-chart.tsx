'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CostAnalysis } from '@/lib/api/token-monitor.api';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	CartesianGrid,
	Legend,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from 'recharts';

interface TokenUsageChartProps {
	analysis: CostAnalysis;
}

export function TokenUsageChart({ analysis }: TokenUsageChartProps) {
	// Prepare data for the chart
	const chartData = [
		{
			period: 'Daily',
			totalTokens: analysis.daily.totalTokens,
			tokensSaved: analysis.daily.optimizationSavings.tokensSaved,
			requests: analysis.daily.requestCount,
		},
		{
			period: 'Weekly',
			totalTokens: analysis.weekly.totalTokens,
			tokensSaved: analysis.weekly.optimizationSavings.tokensSaved,
			requests: analysis.weekly.requestCount,
		},
		{
			period: 'Monthly',
			totalTokens: analysis.monthly.totalTokens,
			tokensSaved: analysis.monthly.optimizationSavings.tokensSaved,
			requests: analysis.monthly.requestCount,
		},
	];

	// Prepare operation breakdown data
	const operationData = Object.entries(analysis.byOperation)
		.map(([operation, stats]) => ({
			operation: operation.replace(/([A-Z])/g, ' $1').trim(),
			tokens: stats.totalTokens,
			cost: stats.totalCost,
			requests: stats.requestCount,
			savings: stats.optimizationSavings.tokensSaved,
		}))
		.sort((a, b) => b.tokens - a.tokens);

	const CustomTooltip = ({ active, payload, label }: any) => {
		if (active && payload && payload.length) {
			return (
				<div className="bg-background border rounded-lg p-3 shadow-lg">
					<p className="font-medium">{label}</p>
					{payload.map((entry: any, index: number) => (
						<p key={index} style={{ color: entry.color }}>
							{entry.name}: {entry.value.toLocaleString()}
							{entry.name.includes('Tokens') && ' tokens'}
							{entry.name.includes('requests') && ' requests'}
						</p>
					))}
				</div>
			);
		}
		return null;
	};

	return (
		<div className="space-y-4">
			{/* Period Overview */}
			<Card>
				<CardHeader>
					<CardTitle>Token Usage Overview</CardTitle>
					<CardDescription>
						Token consumption and savings across different time periods
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={300}>
						<BarChart
							data={chartData}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
						>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="period" />
							<YAxis />
							<Tooltip content={<CustomTooltip />} />
							<Legend />
							<Bar
								dataKey="totalTokens"
								fill="#3b82f6"
								name="Total Tokens"
								radius={[4, 4, 0, 0]}
							/>
							<Bar
								dataKey="tokensSaved"
								fill="#10b981"
								name="Tokens Saved"
								radius={[4, 4, 0, 0]}
							/>
						</BarChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>

			{/* Operation Breakdown */}
			<Card>
				<CardHeader>
					<CardTitle>Token Usage by Operation</CardTitle>
					<CardDescription>
						Breakdown of token consumption across different LLM operations
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={400}>
						<BarChart
							data={operationData}
							margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
							layout="horizontal"
						>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis type="number" />
							<YAxis
								type="category"
								dataKey="operation"
								width={120}
								tick={{ fontSize: 12 }}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Legend />
							<Bar
								dataKey="tokens"
								fill="#8b5cf6"
								name="Tokens Used"
								radius={[0, 4, 4, 0]}
							/>
							<Bar
								dataKey="savings"
								fill="#f59e0b"
								name="Tokens Saved"
								radius={[0, 4, 4, 0]}
							/>
						</BarChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>

			{/* Efficiency Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium">Average Efficiency</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{(
								(1 - analysis.daily.optimizationSavings.compressionRatio) *
								100
							).toFixed(1)}
							%
						</div>
						<p className="text-xs text-muted-foreground">Token reduction rate</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium">
							Most Efficient Operation
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-lg font-bold">
							{operationData.length > 0 &&
								operationData.reduce((max, op) =>
									op.savings > max.savings ? op : max
								).operation}
						</div>
						<p className="text-xs text-muted-foreground">
							{operationData.length > 0 &&
								operationData
									.reduce((max, op) => (op.savings > max.savings ? op : max))
									.savings.toLocaleString()}{' '}
							tokens saved
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium">Total Requests</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{analysis.daily.requestCount.toLocaleString()}
						</div>
						<p className="text-xs text-muted-foreground">Today</p>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
