'use client';

import { useFloatingUI } from '@/contexts/floating-ui-context';
import { cn } from '@/lib/utils';
import {
	DEFAULT_FLOATING_CONFIG,
	FloatingAnimationConfig,
	FloatingElement,
	FloatingUIManagerProps,
} from '@/types/floating-ui';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useEffect, useMemo } from 'react';

// ============================================================================
// FLOATING UI MANAGER
// ============================================================================

export function FloatingUIManager({
	children,
	config = {},
	className,
	style,
}: FloatingUIManagerProps) {
	const { state, resolveCollisions } = useFloatingUI();
	const mergedConfig = { ...DEFAULT_FLOATING_CONFIG, ...config };

	// Get visible elements sorted by z-index
	const visibleElements = useMemo(() => {
		return Array.from(state.elements.values())
			.filter((element) => element.visible)
			.sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));
	}, [state.elements]);

	// Check for collisions when elements change
	useEffect(() => {
		visibleElements.forEach((element) => {
			if (element.collisionDetection) {
				resolveCollisions(element.id);
			}
		});
	}, [visibleElements, resolveCollisions]);

	return (
		<div className={cn('floating-ui-manager', className)} style={style}>
			{children}

			{/* Render floating elements */}
			<AnimatePresence mode="sync">
				{visibleElements.map((element) => (
					<FloatingElementRenderer
						key={element.id}
						element={element}
						config={mergedConfig}
					/>
				))}
			</AnimatePresence>
		</div>
	);
}

// ============================================================================
// FLOATING ELEMENT RENDERER
// ============================================================================

interface FloatingElementRendererProps {
	element: FloatingElement;
	config: typeof DEFAULT_FLOATING_CONFIG;
}

function FloatingElementRenderer({ element, config }: FloatingElementRendererProps) {
	const { checkCollisions } = useFloatingUI();

	// Calculate position styles
	const positionStyles = useMemo(() => {
		const coords = element.coordinates || {};
		const dims = element.dimensions || {};

		return {
			position: 'fixed' as const,
			top: coords.top !== undefined ? `${coords.top}px` : undefined,
			bottom: coords.bottom !== undefined ? `${coords.bottom}px` : undefined,
			left: coords.left !== undefined ? `${coords.left}px` : undefined,
			right: coords.right !== undefined ? `${coords.right}px` : undefined,
			width: dims.width !== undefined ? `${dims.width}px` : undefined,
			height: dims.height !== undefined ? `${dims.height}px` : undefined,
			minWidth: dims.minWidth !== undefined ? `${dims.minWidth}px` : undefined,
			minHeight: dims.minHeight !== undefined ? `${dims.minHeight}px` : undefined,
			maxWidth: dims.maxWidth !== undefined ? `${dims.maxWidth}px` : undefined,
			maxHeight: dims.maxHeight !== undefined ? `${dims.maxHeight}px` : undefined,
			zIndex: element.zIndex || config.priorityZIndex[element.priority],
			...element.style,
		};
	}, [element, config]);

	// Animation configuration
	const animationConfig = useMemo(() => {
		const animation = element.animation || config.animationDefaults;
		return {
			...config.animationDefaults,
			...animation,
		};
	}, [element.animation, config.animationDefaults]);

	// Get animation variants
	const getAnimationVariants = (animConfig: FloatingAnimationConfig) => {
		switch (animConfig.type) {
			case 'fade':
				return {
					initial: { opacity: 0 },
					animate: { opacity: 1 },
					exit: { opacity: 0 },
				};

			case 'slide':
				return {
					initial: { opacity: 0, y: 20 },
					animate: { opacity: 1, y: 0 },
					exit: { opacity: 0, y: -20 },
				};

			case 'scale':
				return {
					initial: { opacity: 0, scale: 0.9 },
					animate: { opacity: 1, scale: 1 },
					exit: { opacity: 0, scale: 0.9 },
				};

			case 'bounce':
				return {
					initial: { opacity: 0, scale: 0.3 },
					animate: {
						opacity: 1,
						scale: 1,
					},
					exit: { opacity: 0, scale: 0.3 },
				};

			case 'none':
				return {
					initial: {},
					animate: {},
					exit: {},
				};

			default:
				return {
					initial: { opacity: 0 },
					animate: { opacity: 1 },
					exit: { opacity: 0 },
				};
		}
	};

	const variants = getAnimationVariants(animationConfig);

	// Check for collisions
	const hasCollisions = checkCollisions(element.id).length > 0;

	return (
		<motion.div
			initial={variants.initial}
			animate={variants.animate}
			exit={variants.exit}
			transition={
				animationConfig.type === 'bounce'
					? {
							type: 'spring',
							stiffness: 300,
							damping: 20,
							duration: animationConfig.duration
								? animationConfig.duration / 1000
								: 0.4,
						}
					: {
							duration: animationConfig.duration
								? animationConfig.duration / 1000
								: 0.2,
							delay: animationConfig.delay ? animationConfig.delay / 1000 : 0,
							ease: 'easeInOut' as const,
						}
			}
			style={positionStyles}
			className={cn(
				'floating-ui-element',
				`floating-ui-${element.type}`,
				`floating-ui-priority-${element.priority}`,
				hasCollisions && 'floating-ui-collision',
				element.className
			)}
			data-floating-id={element.id}
			data-floating-type={element.type}
			data-floating-priority={element.priority}
		>
			{element.content}
		</motion.div>
	);
}

// ============================================================================
// FLOATING UI PORTAL
// ============================================================================

interface FloatingUIPortalProps {
	children: React.ReactNode;
	containerId?: string;
}

export function FloatingUIPortal({
	children,
	containerId = 'floating-ui-portal',
}: FloatingUIPortalProps) {
	useEffect(() => {
		// Create portal container if it doesn't exist
		if (!document.getElementById(containerId)) {
			const container = document.createElement('div');
			container.id = containerId;
			container.className = 'floating-ui-portal';
			container.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				pointer-events: none;
				z-index: 1000;
			`;
			document.body.appendChild(container);
		}
	}, [containerId]);

	// Use React Portal if available, otherwise render normally
	if (typeof window !== 'undefined') {
		const container = document.getElementById(containerId);
		if (container) {
			return <div style={{ pointerEvents: 'auto' }}>{children}</div>;
		}
	}

	return <>{children}</>;
}

// ============================================================================
// FLOATING UI WRAPPER
// ============================================================================

interface FloatingUIWrapperProps {
	children: React.ReactNode;
	className?: string;
	style?: React.CSSProperties;
}

export function FloatingUIWrapper({ children, className, style }: FloatingUIWrapperProps) {
	return (
		<div
			className={cn('floating-ui-wrapper', className)}
			style={{
				position: 'relative',
				width: '100%',
				height: '100%',
				...style,
			}}
		>
			{children}
			<FloatingUIManager />
		</div>
	);
}

// ============================================================================
// FLOATING UI STYLES
// ============================================================================

export const floatingUIStyles = `
.floating-ui-manager {
	position: relative;
	z-index: 1000;
}

.floating-ui-element {
	pointer-events: auto;
}

.floating-ui-element.floating-ui-collision {
	box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
}

.floating-ui-priority-critical {
	z-index: 1300;
}

.floating-ui-priority-high {
	z-index: 1200;
}

.floating-ui-priority-medium {
	z-index: 1100;
}

.floating-ui-priority-low {
	z-index: 1000;
}

.floating-ui-portal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1000;
}

.floating-ui-wrapper {
	position: relative;
	width: 100%;
	height: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.floating-ui-element {
		max-width: calc(100vw - 32px);
		max-height: calc(100vh - 32px);
	}
}

/* Animation classes */
.floating-ui-fade-enter {
	opacity: 0;
}

.floating-ui-fade-enter-active {
	opacity: 1;
	transition: opacity 200ms ease-in-out;
}

.floating-ui-fade-exit {
	opacity: 1;
}

.floating-ui-fade-exit-active {
	opacity: 0;
	transition: opacity 200ms ease-in-out;
}

.floating-ui-slide-enter {
	opacity: 0;
	transform: translateY(20px);
}

.floating-ui-slide-enter-active {
	opacity: 1;
	transform: translateY(0);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-slide-exit {
	opacity: 1;
	transform: translateY(0);
}

.floating-ui-slide-exit-active {
	opacity: 0;
	transform: translateY(-20px);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-scale-enter {
	opacity: 0;
	transform: scale(0.9);
}

.floating-ui-scale-enter-active {
	opacity: 1;
	transform: scale(1);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}

.floating-ui-scale-exit {
	opacity: 1;
	transform: scale(1);
}

.floating-ui-scale-exit-active {
	opacity: 0;
	transform: scale(0.9);
	transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
}
`;
