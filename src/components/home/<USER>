'use client';

import { Translate } from '@/components/ui';
import { BarChart3, Calendar, Trophy } from 'lucide-react';
import { FeatureCard } from './feature-card';

export function GamificationSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="home.gamification" />
			</h2>
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<FeatureCard
					icon={Calendar}
					titleKey="home.streak"
					descriptionKey="home.streak_description"
				/>
				<FeatureCard
					icon={Trophy}
					titleKey="home.achievements"
					descriptionKey="home.achievements_description"
				/>
				<FeatureCard
					icon={BarChart3}
					titleKey="home.leaderboard"
					descriptionKey="home.leaderboard_description"
				/>
			</div>
		</div>
	);
}
