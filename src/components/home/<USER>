'use client';

import { Translate } from '@/components/ui';
import { BookText, Pencil } from 'lucide-react';
import { FeatureCard } from './feature-card';

export function ReadingWritingSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="home.reading_writing" />
			</h2>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<FeatureCard
					icon={BookText}
					titleKey="home.reading"
					descriptionKey="home.reading_description"
				/>
				<FeatureCard
					icon={Pencil}
					titleKey="home.writing"
					descriptionKey="home.writing_description"
				/>
			</div>
		</div>
	);
}
