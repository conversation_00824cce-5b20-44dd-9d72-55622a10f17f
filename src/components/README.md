# Components Directory

This directory contains reusable UI components organized by feature and functionality, following a component-driven development approach.

## Structure

```
components/
├── auth/                  # Authentication-related components
├── dashboard/             # Dashboard and analytics components
├── error/                 # Error handling and display components
├── fallback/              # Fallback and loading state components
├── floating-ui/           # Floating UI and overlay components
├── home/                  # Home page specific components
├── layout/                # Layout and navigation components
├── onboarding/            # User onboarding flow components
└── ui/                    # Base UI primitives and design system
```

## Component Categories

### Authentication (`auth/`)
- Login and registration forms
- Authentication provider integrations
- User profile and settings components
- Password reset and recovery flows

### Dashboard (`dashboard/`)
- Analytics and progress visualization
- Data tables and charts
- Statistics and metrics display
- User activity tracking components

### Error Handling (`error/`)
- Error boundary components
- Error message display
- Retry mechanisms and fallbacks
- User-friendly error states

### Fallback (`fallback/`)
- Loading skeletons and spinners
- Empty states and placeholders
- Offline mode indicators
- Graceful degradation components

### Floating UI (`floating-ui/`)
- Modal dialogs and overlays
- Tooltips and popovers
- Dropdown menus and selects
- Floating action buttons and panels

### Home (`home/`)
- Hero sections and landing content
- Feature showcases and highlights
- Call-to-action components
- Marketing and promotional elements

### Layout (`layout/`)
- Navigation bars and menus
- Sidebar and drawer components
- Header and footer elements
- Page layout containers

### Onboarding (`onboarding/`)
- Welcome screens and tutorials
- Step-by-step guidance flows
- Feature introduction components
- User setup and configuration

### UI Primitives (`ui/`)
- Base design system components
- Buttons, inputs, and form elements
- Typography and text components
- Icons and visual elements
- Theme and styling utilities

## Design Principles

### Component Guidelines
- **Single Responsibility**: Each component has a focused purpose
- **Composition**: Prefer composition over complex prop drilling
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Type Safety**: TypeScript interfaces for all component props
- **Reusability**: Generic components that can be used across features

### Styling Approach
- **Tailwind CSS**: Utility-first styling approach
- **Design Tokens**: Consistent spacing, colors, and typography
- **Responsive Design**: Mobile-first responsive components
- **Dark Mode**: Theme-aware components with dark mode support

### State Management
- **Local State**: Component-level state for UI interactions
- **Context Integration**: Integration with global state contexts
- **Custom Hooks**: Reusable logic through custom React hooks
- **Event Handling**: Proper event delegation and handling

## Development Conventions

- Use PascalCase for component names
- Export components as named exports
- Include TypeScript interfaces for props
- Add proper accessibility attributes
- Use Framer Motion for animations when needed
- Group related components in subdirectories
- Include JSDoc comments for complex components

## Testing Strategy

- Unit tests for component logic
- Integration tests for component interactions
- Accessibility testing with screen readers
- Visual regression testing for UI consistency
- Performance testing for rendering optimization
