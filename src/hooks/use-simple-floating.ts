'use client';

import { useCallback, useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';

interface SimpleFloatingOptions {
	autoShow?: boolean;
	position?: {
		top?: number;
		bottom?: number;
		left?: number;
		right?: number;
	};
	zIndex?: number;
	className?: string;
}

export function useSimpleFloating(
	id: string,
	content: React.ReactNode,
	options: SimpleFloatingOptions = {}
) {
	const [isVisible, setIsVisible] = useState(options.autoShow || false);

	const show = useCallback(() => {
		setIsVisible(true);
	}, []);

	const hide = useCallback(() => {
		setIsVisible(false);
	}, []);

	const toggle = useCallback(() => {
		setIsVisible((prev) => !prev);
	}, []);

	// Create portal element
	useEffect(() => {
		if (typeof window === 'undefined') return;

		let portalContainer = document.getElementById('simple-floating-portal');
		if (!portalContainer) {
			portalContainer = document.createElement('div');
			portalContainer.id = 'simple-floating-portal';
			portalContainer.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				pointer-events: none;
				z-index: 1000;
			`;
			document.body.appendChild(portalContainer);
		}

		return () => {
			// Cleanup on unmount
			const container = document.getElementById('simple-floating-portal');
			if (container && container.children.length === 0) {
				container.remove();
			}
		};
	}, []);

	// Render floating element
	useEffect(() => {
		if (typeof window === 'undefined') return;

		const portalContainer = document.getElementById('simple-floating-portal');
		if (!portalContainer) return;

		// Remove existing element
		const existingElement = document.getElementById(`floating-${id}`);
		if (existingElement) {
			existingElement.remove();
		}

		if (isVisible) {
			// Create new element
			const floatingElement = document.createElement('div');
			floatingElement.id = `floating-${id}`;
			floatingElement.style.cssText = `
				position: fixed;
				${options.position?.top !== undefined ? `top: ${options.position.top}px;` : ''}
				${options.position?.bottom !== undefined ? `bottom: ${options.position.bottom}px;` : ''}
				${options.position?.left !== undefined ? `left: ${options.position.left}px;` : ''}
				${options.position?.right !== undefined ? `right: ${options.position.right}px;` : ''}
				z-index: ${options.zIndex || 1001};
				pointer-events: auto;
			`;

			if (options.className) {
				floatingElement.className = options.className;
			}

			portalContainer.appendChild(floatingElement);

			// Use React to render content
			const root = createRoot(floatingElement);
			root.render(content as React.ReactElement);
		}

		return () => {
			const element = document.getElementById(`floating-${id}`);
			if (element) {
				element.remove();
			}
		};
	}, [id, isVisible, content, options.position, options.zIndex, options.className]);

	return {
		show,
		hide,
		toggle,
		isVisible,
	};
}
