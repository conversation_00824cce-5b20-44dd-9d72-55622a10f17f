'use client';

import { AppError, errorLogger } from '@/lib/error-handling';
import { retryApiCall } from '@/lib/retry';
import { useCallback, useEffect, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface DegradationLevel {
	level: 'full' | 'partial' | 'minimal' | 'offline';
	features: string[];
	description: string;
}

export interface FeatureConfig {
	name: string;
	required: boolean;
	fallback?: () => any;
	test?: () => Promise<boolean>;
}

export interface GracefulDegradationOptions {
	features: FeatureConfig[];
	onDegradation?: (level: DegradationLevel) => void;
	recheckInterval?: number;
}

// ============================================================================
// GRACEFUL DEGRADATION HOOK
// ============================================================================

export function useGracefulDegradation(options: GracefulDegradationOptions) {
	const { features, onDegradation, recheckInterval = 60000 } = options;

	const [currentLevel, setCurrentLevel] = useState<DegradationLevel>({
		level: 'full',
		features: features.map((f) => f.name),
		description: 'All features available',
	});

	const [availableFeatures, setAvailableFeatures] = useState<Set<string>>(
		new Set(features.map((f) => f.name))
	);

	const [isChecking, setIsChecking] = useState(false);

	// Test feature availability
	const testFeature = useCallback(async (feature: FeatureConfig): Promise<boolean> => {
		if (!feature.test) return true; // Assume available if no test

		try {
			return await retryApiCall(feature.test, { maxAttempts: 2 });
		} catch (error) {
			errorLogger.warn(
				`Feature test failed: ${feature.name}`,
				{ feature: feature.name, error },
				'GracefulDegradation'
			);
			return false;
		}
	}, []);

	// Check all features
	const checkFeatures = useCallback(async (): Promise<DegradationLevel> => {
		setIsChecking(true);
		const available = new Set<string>();
		const unavailable: string[] = [];

		// Test each feature
		for (const feature of features) {
			const isAvailable = await testFeature(feature);

			if (isAvailable) {
				available.add(feature.name);
			} else {
				unavailable.push(feature.name);

				// If it's a required feature, this is a significant degradation
				if (feature.required) {
					errorLogger.error(
						`Required feature unavailable: ${feature.name}`,
						new AppError(
							`Feature ${feature.name} is not available`,
							'FEATURE_UNAVAILABLE'
						),
						{ feature: feature.name },
						'GracefulDegradation'
					);
				}
			}
		}

		setAvailableFeatures(available);

		// Determine degradation level
		const level = determineDegradationLevel(available, features);

		errorLogger.info(
			'Feature availability check completed',
			{
				level: level.level,
				available: Array.from(available),
				unavailable,
			},
			'GracefulDegradation'
		);

		setIsChecking(false);
		return level;
	}, [features, testFeature]);

	// Update degradation level
	const updateDegradationLevel = useCallback(async () => {
		const newLevel = await checkFeatures();

		if (newLevel.level !== currentLevel.level) {
			setCurrentLevel(newLevel);

			if (onDegradation) {
				onDegradation(newLevel);
			}
		}
	}, [checkFeatures, currentLevel.level, onDegradation]);

	// Initial check and periodic rechecks
	useEffect(() => {
		updateDegradationLevel();

		if (recheckInterval > 0) {
			const interval = setInterval(updateDegradationLevel, recheckInterval);
			return () => clearInterval(interval);
		}
	}, [updateDegradationLevel, recheckInterval]);

	// Check if a specific feature is available
	const isFeatureAvailable = useCallback(
		(featureName: string): boolean => {
			return availableFeatures.has(featureName);
		},
		[availableFeatures]
	);

	// Get fallback for a feature
	const getFeatureFallback = useCallback(
		(featureName: string) => {
			const feature = features.find((f) => f.name === featureName);
			return feature?.fallback?.() || null;
		},
		[features]
	);

	// Force recheck
	const recheckFeatures = useCallback(() => {
		updateDegradationLevel();
	}, [updateDegradationLevel]);

	return {
		currentLevel,
		availableFeatures: Array.from(availableFeatures),
		isChecking,
		isFeatureAvailable,
		getFeatureFallback,
		recheckFeatures,
	};
}

// ============================================================================
// FEATURE AVAILABILITY HOOK
// ============================================================================

export function useFeatureAvailability(featureName: string, testFn?: () => Promise<boolean>) {
	const [isAvailable, setIsAvailable] = useState(true);
	const [isChecking, setIsChecking] = useState(false);
	const [lastChecked, setLastChecked] = useState<Date | null>(null);

	const checkAvailability = useCallback(async () => {
		if (!testFn) return true;

		setIsChecking(true);
		try {
			const available = await testFn();
			setIsAvailable(available);
			setLastChecked(new Date());
			return available;
		} catch (error) {
			errorLogger.warn(
				`Feature availability check failed: ${featureName}`,
				{ feature: featureName, error },
				'FeatureAvailability'
			);
			setIsAvailable(false);
			return false;
		} finally {
			setIsChecking(false);
		}
	}, [featureName, testFn]);

	useEffect(() => {
		checkAvailability();
	}, [checkAvailability]);

	return {
		isAvailable,
		isChecking,
		lastChecked,
		recheck: checkAvailability,
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function determineDegradationLevel(
	availableFeatures: Set<string>,
	allFeatures: FeatureConfig[]
): DegradationLevel {
	const requiredFeatures = allFeatures.filter((f) => f.required);
	const availableRequired = requiredFeatures.filter((f) => availableFeatures.has(f.name));

	const totalFeatures = allFeatures.length;
	const availableCount = availableFeatures.size;
	const availabilityRatio = availableCount / totalFeatures;

	// No features available
	if (availableCount === 0) {
		return {
			level: 'offline',
			features: [],
			description: 'No features available - offline mode',
		};
	}

	// Missing required features
	if (availableRequired.length < requiredFeatures.length) {
		return {
			level: 'minimal',
			features: Array.from(availableFeatures),
			description: 'Core features unavailable - minimal functionality',
		};
	}

	// Less than 70% of features available
	if (availabilityRatio < 0.7) {
		return {
			level: 'partial',
			features: Array.from(availableFeatures),
			description: 'Some features unavailable - partial functionality',
		};
	}

	// All or most features available
	return {
		level: 'full',
		features: Array.from(availableFeatures),
		description: 'All features available',
	};
}

// ============================================================================
// PREDEFINED FEATURE CONFIGS
// ============================================================================

export const COMMON_FEATURES: Record<string, FeatureConfig> = {
	api: {
		name: 'api',
		required: true,
		test: async () => {
			const response = await fetch('/api/auth/status', { method: 'HEAD' });
			return response.ok;
		},
	},

	auth: {
		name: 'auth',
		required: true,
		test: async () => {
			const response = await fetch('/api/auth/status', { method: 'HEAD' });
			return response.ok;
		},
	},

	storage: {
		name: 'storage',
		required: false,
		test: async () => {
			try {
				localStorage.setItem('test', 'test');
				localStorage.removeItem('test');
				return true;
			} catch {
				return false;
			}
		},
		fallback: () => new Map(), // In-memory storage
	},

	notifications: {
		name: 'notifications',
		required: false,
		test: async () => {
			return 'Notification' in window && Notification.permission !== 'denied';
		},
		fallback: () => console.log, // Console logging
	},

	geolocation: {
		name: 'geolocation',
		required: false,
		test: async () => {
			return 'geolocation' in navigator;
		},
	},

	camera: {
		name: 'camera',
		required: false,
		test: async () => {
			try {
				const stream = await navigator.mediaDevices.getUserMedia({ video: true });
				stream.getTracks().forEach((track) => track.stop());
				return true;
			} catch {
				return false;
			}
		},
	},
};
