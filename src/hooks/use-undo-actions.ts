'use client';

import { useError<PERSON>andler } from '@/contexts/error-context';
import { createErrorContext, normalizeError } from '@/lib/error-handling';
import { useCallback, useRef, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface UndoAction {
	id: string;
	type: string;
	description: string;
	timestamp: number;
	data: any;
	undoFn: () => Promise<void>;
	redoFn?: () => Promise<void>;
	timeout?: number; // Auto-expire time in ms
}

export interface UndoState {
	actions: UndoAction[];
	isUndoing: boolean;
	isRedoing: boolean;
}

// ============================================================================
// UNDO ACTIONS HOOK
// ============================================================================

export function useUndoActions(maxActions: number = 10) {
	const [state, setState] = useState<UndoState>({
		actions: [],
		isUndoing: false,
		isRedoing: false,
	});

	const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());
	const { handleError } = useErrorHandler('useUndoActions');

	// Add new undo action
	const addUndoAction = useCallback(
		(action: Omit<UndoAction, 'id' | 'timestamp'>) => {
			const id = crypto.randomUUID();
			const timestamp = Date.now();

			const newAction: UndoAction = {
				...action,
				id,
				timestamp,
			};

			setState((prev) => {
				const newActions = [newAction, ...prev.actions].slice(0, maxActions);
				return {
					...prev,
					actions: newActions,
				};
			});

			// Set auto-expire timeout if specified
			if (action.timeout) {
				const timeoutId = setTimeout(() => {
					removeAction(id);
				}, action.timeout);

				timeoutRefs.current.set(id, timeoutId);
			}

			return id;
		},
		[maxActions]
	);

	// Remove action by ID
	const removeAction = useCallback((actionId: string) => {
		// Clear timeout if exists
		const timeoutId = timeoutRefs.current.get(actionId);
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutRefs.current.delete(actionId);
		}

		setState((prev) => ({
			...prev,
			actions: prev.actions.filter((action) => action.id !== actionId),
		}));
	}, []);

	// Execute undo action
	const executeUndo = useCallback(
		async (actionId: string) => {
			const action = state.actions.find((a) => a.id === actionId);
			if (!action) return false;

			setState((prev) => ({ ...prev, isUndoing: true }));

			try {
				await action.undoFn();
				removeAction(actionId);
				return true;
			} catch (error) {
				const normalizedError = normalizeError(
					error,
					`Failed to undo action: ${action.description}`,
					createErrorContext('useUndoActions', 'execute_undo', {
						actionId,
						actionType: action.type,
					})
				);
				handleError(normalizedError, 'execute_undo');
				return false;
			} finally {
				setState((prev) => ({ ...prev, isUndoing: false }));
			}
		},
		[state.actions, removeAction, handleError]
	);

	// Execute redo action
	const executeRedo = useCallback(
		async (action: UndoAction) => {
			if (!action.redoFn) return false;

			setState((prev) => ({ ...prev, isRedoing: true }));

			try {
				await action.redoFn();
				return true;
			} catch (error) {
				const normalizedError = normalizeError(
					error,
					`Failed to redo action: ${action.description}`,
					createErrorContext('useUndoActions', 'execute_redo', {
						actionId: action.id,
						actionType: action.type,
					})
				);
				handleError(normalizedError, 'execute_redo');
				return false;
			} finally {
				setState((prev) => ({ ...prev, isRedoing: false }));
			}
		},
		[handleError]
	);

	// Get most recent action
	const getLatestAction = useCallback(() => {
		return state.actions[0] || null;
	}, [state.actions]);

	// Get actions by type
	const getActionsByType = useCallback(
		(type: string) => {
			return state.actions.filter((action) => action.type === type);
		},
		[state.actions]
	);

	// Clear all actions
	const clearActions = useCallback(() => {
		// Clear all timeouts
		timeoutRefs.current.forEach((timeoutId) => clearTimeout(timeoutId));
		timeoutRefs.current.clear();

		setState((prev) => ({
			...prev,
			actions: [],
		}));
	}, []);

	// Clear expired actions
	const clearExpiredActions = useCallback(() => {
		const now = Date.now();
		setState((prev) => ({
			...prev,
			actions: prev.actions.filter((action) => {
				if (action.timeout && now - action.timestamp > action.timeout) {
					// Clear timeout ref
					const timeoutId = timeoutRefs.current.get(action.id);
					if (timeoutId) {
						clearTimeout(timeoutId);
						timeoutRefs.current.delete(action.id);
					}
					return false;
				}
				return true;
			}),
		}));
	}, []);

	return {
		// State
		actions: state.actions,
		isUndoing: state.isUndoing,
		isRedoing: state.isRedoing,
		hasActions: state.actions.length > 0,

		// Actions
		addUndoAction,
		removeAction,
		executeUndo,
		executeRedo,
		getLatestAction,
		getActionsByType,
		clearActions,
		clearExpiredActions,
	};
}

// ============================================================================
// UNDO ACTION BUILDERS
// ============================================================================

export const createWordAddedAction = (
	word: { term: string; id?: string },
	collectionId: string,
	removeWordFn: (wordId: string) => Promise<void>,
	addWordFn?: (word: any) => Promise<void>
): Omit<UndoAction, 'id' | 'timestamp'> => ({
	type: 'word_added',
	description: `Added "${word.term}" to collection`,
	data: { word, collectionId },
	undoFn: async () => {
		if (word.id) {
			await removeWordFn(word.id);
		}
	},
	redoFn: addWordFn ? async () => await addWordFn(word) : undefined,
	timeout: 10000, // 10 seconds to undo
});

export const createWordsAddedAction = (
	words: Array<{ term: string; id?: string }>,
	collectionId: string,
	removeWordsFn: (wordIds: string[]) => Promise<void>,
	addWordsFn?: (words: any[]) => Promise<void>
): Omit<UndoAction, 'id' | 'timestamp'> => ({
	type: 'words_added',
	description: `Added ${words.length} words to collection`,
	data: { words, collectionId },
	undoFn: async () => {
		const wordIds = words.filter((w) => w.id).map((w) => w.id!);
		if (wordIds.length > 0) {
			await removeWordsFn(wordIds);
		}
	},
	redoFn: addWordsFn ? async () => await addWordsFn(words) : undefined,
	timeout: 15000, // 15 seconds for bulk operations
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function formatUndoDescription(action: UndoAction): string {
	const timeAgo = Math.floor((Date.now() - action.timestamp) / 1000);

	if (timeAgo < 60) {
		return `${action.description} (${timeAgo}s ago)`;
	} else if (timeAgo < 3600) {
		return `${action.description} (${Math.floor(timeAgo / 60)}m ago)`;
	} else {
		return `${action.description} (${Math.floor(timeAgo / 3600)}h ago)`;
	}
}

export function isActionExpired(action: UndoAction): boolean {
	if (!action.timeout) return false;
	return Date.now() - action.timestamp > action.timeout;
}
