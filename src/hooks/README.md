# Hooks Directory

This directory contains custom React hooks that encapsulate reusable logic and state management patterns across the application.

## Structure

```
hooks/
├── index.ts                    # Hook exports and utilities
├── use-collections.ts          # Collections data management
├── use-dom-floating.ts         # DOM-based floating UI logic
├── use-floating-position.ts    # Floating element positioning
├── use-floating-ui.ts          # Advanced floating UI management
├── use-graceful-degradation.ts # Progressive enhancement patterns
├── use-media-query.ts          # Responsive design utilities
├── use-offline.ts              # Offline state detection
├── use-simple-floating.ts      # Simple floating UI logic
├── use-undo-actions.ts         # Undo/redo functionality
└── use-words.ts                # Word and vocabulary management
```

## Hook Categories

### Data Management Hooks
- **use-collections**: Collection CRUD operations and state management
- **use-words**: Vocabulary word operations and learning progress
- **use-undo-actions**: Undo/redo functionality for user actions

### UI and Interaction Hooks
- **use-floating-ui**: Advanced floating UI positioning and management
- **use-dom-floating**: DOM-based floating element logic
- **use-floating-position**: Precise positioning calculations
- **use-simple-floating**: Lightweight floating UI implementation

### System and Environment Hooks
- **use-media-query**: Responsive design and breakpoint detection
- **use-offline**: Network connectivity and offline state management
- **use-graceful-degradation**: Progressive enhancement patterns

## Key Features

### Data Management
- **State Synchronization**: Sync local state with server data
- **Optimistic Updates**: Immediate UI updates with rollback capability
- **Caching**: Intelligent data caching and invalidation
- **Error Handling**: Robust error handling and recovery

### Floating UI System
- **Positioning**: Advanced positioning algorithms for floating elements
- **Collision Detection**: Automatic repositioning to avoid viewport edges
- **Accessibility**: Keyboard navigation and screen reader support
- **Performance**: Optimized rendering and event handling

### Responsive Design
- **Breakpoint Detection**: CSS media query integration
- **Dynamic Layouts**: Responsive component behavior
- **Performance**: Efficient media query listening
- **SSR Compatibility**: Server-side rendering support

### Offline Support
- **Network Detection**: Real-time connectivity monitoring
- **Graceful Degradation**: Fallback behavior for offline scenarios
- **Data Synchronization**: Sync data when connection is restored
- **User Feedback**: Clear offline state indicators

## Design Principles

### Hook Design
- **Single Responsibility**: Each hook has a focused purpose
- **Reusability**: Generic hooks that work across components
- **Composability**: Hooks can be combined and composed
- **Performance**: Optimized for minimal re-renders
- **Type Safety**: Comprehensive TypeScript typing

### State Management
- **Local State**: Component-level state management
- **Side Effects**: Proper cleanup and effect management
- **Dependencies**: Clear dependency arrays and optimization
- **Error Boundaries**: Proper error handling and recovery

## Usage Patterns

### Data Management Hooks
```typescript
import { useCollections, useWords } from '@/hooks';

function MyComponent() {
  const { collections, createCollection, updateCollection } = useCollections();
  const { words, addWord, removeWord } = useWords();
  
  // Component logic
}
```

### UI Hooks
```typescript
import { useFloatingUI, useMediaQuery } from '@/hooks';

function ResponsiveFloatingComponent() {
  const { refs, floatingStyles } = useFloatingUI();
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  // Component logic
}
```

### System Hooks
```typescript
import { useOffline, useGracefulDegradation } from '@/hooks';

function OfflineAwareComponent() {
  const isOffline = useOffline();
  const { fallback, enhanced } = useGracefulDegradation();
  
  // Component logic
}
```

## Development Guidelines

### Hook Implementation
- Use TypeScript for all hook parameters and return values
- Implement proper cleanup in useEffect hooks
- Optimize dependencies to prevent unnecessary re-renders
- Handle loading and error states appropriately
- Provide clear and consistent APIs

### Performance Optimization
- Use useMemo and useCallback for expensive computations
- Implement proper dependency arrays
- Avoid creating objects in render cycles
- Use refs for values that don't trigger re-renders
- Implement debouncing for frequent updates

### Testing Strategy
- Unit tests for hook logic and state management
- Integration tests for hook interactions
- Performance tests for optimization verification
- Accessibility tests for UI-related hooks
- Error handling tests for edge cases
