import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
	env: {},
	reactStrictMode: false,
	images: {
		unoptimized: true,
	},
	serverExternalPackages: ['ioredis', 'node-cache'],
	webpack: (config, { isServer }) => {
		// Exclude server-only packages from client bundle
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				dns: false,
				tls: false,
				crypto: false,
				ioredis: false,
				'node-cache': false,
			};

			// Additional optimization: exclude server-only modules
			config.resolve.alias = {
				...config.resolve.alias,
				'@/backend/services/redis-cache.service': false,
				'@/backend/cache-init.server': false,
			};
		}
		return config;
	},
};

export default nextConfig;
