import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';
import globals from 'globals';
import js from '@eslint/js';
import pluginReact from 'eslint-plugin-react';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
	baseDirectory: __dirname,
});

export default [
	// Ignore everything except files in src directory
	{ files: ['src/**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
	{ files: ['src/**/*.{js,mjs,cjs,ts,jsx,tsx}'], languageOptions: { globals: globals.browser } },
	js.configs.recommended,
	pluginReact.configs.flat.recommended,
	...compat.extends('next/core-web-vitals', 'next/typescript'),
	{
		rules: {
			'no-unused-vars': 'off',
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-unused-vars-experimental': 'off', // Disable "is declared but its value is never read."
			'react-hooks/exhaustive-deps': 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-empty-object-type': 'off',
		},
	},
	// Specific rules for client-side files (components, pages, hooks, contexts)
	{
		files: [
			'src/components/**/*.{ts,tsx}',
			'src/app/**/page.{ts,tsx}',
			'src/app/**/layout.{ts,tsx}',
			'src/hooks/**/*.{ts,tsx}',
			'src/contexts/**/*.{ts,tsx}',
			'src/providers/**/*.{ts,tsx}',
		],
		rules: {
			// Prevent server-only imports in client components
			'no-restricted-imports': [
				'error',
				{
					patterns: [
						{
							group: ['**/redis-cache.service*', '**/cache-init.server*'],
							message:
								'Redis services should not be imported in client-side code. Use API routes instead.',
						},
						{
							group: ['ioredis'],
							message: 'ioredis should only be used in server-side code.',
						},
						{
							group: ['**/semantic-cache.service*'],
							message:
								'Semantic cache service should only be used in server-side code.',
						},
					],
				},
			],
		},
	},
];
