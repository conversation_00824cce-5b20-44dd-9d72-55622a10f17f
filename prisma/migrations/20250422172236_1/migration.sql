-- AlterTable
ALTER TABLE "Collection" ADD COLUMN     "word_ids" TEXT[];

-- AlterTable
ALTER TABLE "LastSeenWord" ADD COLUMN     "category_id" TEXT,
ADD COLUMN     "collection_id" TEXT;

-- AlterTable
ALTER TABLE "Word" ADD COLUMN     "audio_url" TEXT;

-- AddForeignKey
ALTER TABLE "LastSeenWord" ADD CONSTRAINT "LastSeenWord_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "Collection"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LastSeenWord" ADD CONSTRAINT "LastSeenWord_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;
