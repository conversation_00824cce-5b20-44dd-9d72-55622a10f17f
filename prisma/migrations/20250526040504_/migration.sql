/*
  Warnings:

  - You are about to drop the `Exercise` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropF<PERSON><PERSON>Key
ALTER TABLE "Exercise" DROP CONSTRAINT "Exercise_paragraph_id_fkey";

-- DropTable
DROP TABLE "Exercise";

-- DropEnum
DROP TYPE "ExerciseType";

-- CreateTable
CREATE TABLE "MultipleChoiceExercise" (
    "id" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "options" TEXT[],
    "answer" INTEGER NOT NULL,
    "explanation" TEXT,
    "paragraph_id" TEXT NOT NULL,

    CONSTRAINT "MultipleChoiceExercise_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "MultipleChoiceExercise_paragraph_id_idx" ON "MultipleChoiceExercise"("paragraph_id");

-- AddForeignKey
ALTER TABLE "MultipleChoiceExercise" ADD CONSTRAINT "MultipleChoiceExercise_paragraph_id_fkey" FOREIGN KEY ("paragraph_id") REFERENCES "Paragraph"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
