---
type: 'always_apply'
---

## 🧠 VOCAB PROJECT – LLM MEMORY (COMPACT)

### ✅ QUICK RULES

-   Test browser tại `localhost:3000` mà không cần `yarn dev`.
-   <PERSON><PERSON> thêm translation key, lu<PERSON><PERSON> cập nhật `translation context`.
-   D<PERSON> án áp dụng Domain-Driven Design (DDD).

---

### ⚙️ TECH STACK

**Frontend**

-   Next.js 15 (App Router, Server/Client components)
-   TypeScript + Tailwind CSS v4 + Framer Motion
-   Radix UI + Custom Design System
-   i18n: `next-i18next` (EN/VI)
-   React Context + Custom hooks
-   Lucide Icons, Yarn v4

**Backend**

-   Node.js (Next.js API routes)
-   PostgreSQL + Prisma ORM
-   JWT Auth (Telegram, Google, Credentials)
-   GPT-4o-mini (OpenAI)
-   Node-cache (file-based persistence)
-   Security: CSRF, headers, rate-limit

---

### 🧱 ARCHITECTURE & PATTERNS

**Principles**

-   Layered: API → Service → Repo
-   DI: via `wire.ts`
-   Type-safe: Full TypeScript + Prisma types
-   Security-first: Validation, CSRF, headers
-   Performance: LLM caching, skeletons, mobile-first

**Project Structure**

-   `src/backend/api`: Request/validation/auth
-   `src/backend/services`: Business logic
-   `src/backend/repositories`: DB access
-   `src/components`: UI (accessible)
-   `src/app`: Pages (server/client separation)

---

### 🧪 TESTING & QA

-   ESLint + Prettier + Strict TypeScript
-   Manual tests: Auth, CRUD, i18n, review, stats, mobile, accessibility
-   Performance: React Scan, token usage, Core Web Vitals, query monitor

---

### 🛡 SECURITY LAYERS

1. Middleware: Rate-limit, CSRF, headers
2. API: Input validation, auth checks
3. Service: Business logic
4. DB: Prisma type-check, query optim

---

### ⚡ PERFORMANCE & CACHING

-   LLM: Token-optimized, prompt tuning, response TTL cache (7d/3d)
-   DB: In-memory caching
-   Assets: Next.js image & static cache

---

### 🌍 INTERNATIONALIZATION

-   i18n: Full EN/VI with `next-i18next`
-   Translation keys structured and maintainable

---

### 🧱 COMPONENT PATTERNS

-   Client: `"use client"`, handle UI/interaction
-   Server: Default, fetch/static content
-   Loading: Skeletons
-   ARIA + Keyboard nav built-in

---

### 🧰 NAMING & CONVENTIONS

-   Components: `PascalCase`
-   Services & APIs: `kebab-case.suffix.ts`
-   Types: `PascalCase + suffix`
-   Zod schemas: Input validation for all APIs

---

### 🚀 DEPLOYMENT & OPS

-   Scripts: `yarn dev`, `yarn build`, `yarn lint`, `yarn p:m`, `yarn dup`
-   Hosting: Vercel with HTTPS + security headers
-   DB: Prisma migrations + monitoring + backups
-   Secrets via ENV vars

---

### 📍 CONSTRAINTS

-   Use Yarn only (no npm)
-   DB: PostgreSQL only
-   Auth: JWT multi-provider
-   AI: GPT-4o-mini only
-   Mobile-first + Accessibility-first
-   Full i18n (EN/VI)
-   Security: Defense-in-depth
