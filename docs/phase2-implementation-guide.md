# Phase 2 Implementation Guide - Advanced Optimizations

## Tổng Quan

Phase 2 triển khai các optimizations nâng cao để đạt mục tiêu giảm 50-60% tổng chi phí API:

### ✅ Đã Triển Khai

1. **BatchProcessorService** - Intelligent batch processing để combine multiple requests
2. **SemanticCacheService** - Semantic similarity matching cho cache hits
3. **ModelSelectorService** - Dynamic model routing dựa trên request complexity
4. **Enhanced LLM Service** - Tích hợp tất cả optimizations
5. **Advanced Monitoring** - Detailed analytics cho batch, cache, và model performance

## Tính Năng Mới

### 1. Batch Processing

#### Automatic Request Batching

```typescript
// Requests được tự động batch theo operation type
const words1 = llmService.generateWordDetails(['apple', 'banana']);
const words2 = llmService.generateWordDetails(['orange', 'grape']);
// <PERSON> requests này sẽ được combine thành 1 API call
```

#### Intelligent Grouping

- **Batchable Operations**: `generateWordDetails`, `evaluateAnswers`, `evaluateTranslation`
- **Smart Timing**: Max 2 seconds wait time hoặc khi có high priority requests
- **Token Limits**: Tự động split khi exceed token limits

#### Priority System

```typescript
// High priority - process immediately
await batchProcessor.addToBatch('generateWordDetails', params, 'high');

// Normal priority - wait for batching
await batchProcessor.addToBatch('generateWordDetails', params, 'normal');

// Low priority - batch with longer wait time
await batchProcessor.addToBatch('generateWordDetails', params, 'low');
```

### 2. Semantic Caching

#### Similarity Matching

```typescript
// Cache hit cho similar requests
const request1 = { keywords: ['technology', 'computer'], language: 'EN' };
const request2 = { keywords: ['tech', 'computing'], language: 'EN' };
// Request2 có thể hit cache của request1 với similarity > 80%
```

#### Multi-dimensional Similarity

- **Keyword Similarity** (40%): Jaccard index của keywords
- **Structural Similarity** (30%): Operation type và parameters structure
- **Semantic Similarity** (30%): Synonym và related word matching

#### Keyword Indexing

```typescript
// Tìm cache entries theo keyword
const entries = semanticCache.findByKeyword('technology');
// Returns cached results có chứa keyword 'technology'
```

### 3. Smart Model Selection

#### Complexity-based Routing

```typescript
// Simple operations -> gpt-4o-mini (cost-effective)
generateRandomTerms(); // -> gpt-4o-mini

// Complex evaluations -> gpt-4o (higher quality)
evaluateAnswers(); // -> gpt-4o

// Creative tasks -> gpt-4o (best creativity)
generateGrammarPractice(); // -> gpt-4o
```

#### Dynamic Selection Criteria

- **Token Count**: Larger requests get more powerful models
- **Quality Requirements**: Evaluation tasks get higher quality models
- **Speed Requirements**: Time-sensitive requests get faster models
- **Cost Constraints**: Budget-conscious requests get cheaper models

#### Adaptive Learning

```typescript
// Model performance được update based on actual usage
modelSelector.updateModelPerformance('gpt-4o-mini', actualLatency, qualityFeedback, success);
```

## Configuration

### Environment Variables

```bash
# Phase 2: Advanced Optimizations
LLM_SEMANTIC_CACHE_ENABLED=true
LLM_SEMANTIC_CACHE_THRESHOLD=0.8
LLM_SEMANTIC_MAX_KEYWORDS=20

# Batch Processing
LLM_BATCH_PROCESSING_ENABLED=true
LLM_BATCH_SIZE_WORD_DETAILS=20
LLM_BATCH_MAX_WAIT_TIME=2000
LLM_BATCH_MAX_TOKENS=8000

# Smart Model Selection
LLM_MODEL_SELECTION_ENABLED=true
LLM_COST_OPTIMIZATION_ENABLED=true
LLM_QUALITY_THRESHOLD=0.8
LLM_ADAPTIVE_LEARNING_ENABLED=true
```

### Batch Size Configuration

| Operation           | Max Batch Size | Reasoning                             |
| ------------------- | -------------- | ------------------------------------- |
| generateWordDetails | 20             | High efficiency, stable output        |
| evaluateAnswers     | 10             | Complex evaluation, moderate batching |
| evaluateTranslation | 15             | Good balance of efficiency/quality    |
| generateQuestions   | 5              | Creative task, smaller batches        |
| generateParagraph   | 3              | Large output, limited batching        |

## API Endpoints

### New Monitoring Endpoints

```bash
# Batch processing stats
GET /api/token-monitor/batch-stats

# Model selection stats
GET /api/token-monitor/model-stats

# Enhanced cache stats (includes semantic)
GET /api/token-monitor/cache-stats

# Cache cleanup
POST /api/token-monitor/cleanup-cache
```

### Example Responses

#### Batch Stats

```json
{
	"success": true,
	"data": {
		"queueLength": 5,
		"processing": false,
		"config": {
			"maxBatchSize": {
				"generateWordDetails": 20,
				"evaluateAnswers": 10
			},
			"maxWaitTime": 2000,
			"maxTokensPerBatch": 8000
		}
	}
}
```

#### Model Stats

```json
{
	"success": true,
	"data": {
		"models": {
			"gpt-4o-mini": {
				"performance": {
					"averageLatency": 1500,
					"reliability": 0.95,
					"qualityScore": 0.8
				},
				"costPer1kTokens": 0.000375
			}
		},
		"available": ["gpt-4o-mini", "gpt-4o", "gpt-4"],
		"summary": {
			"totalModels": 3,
			"averageCost": 0.015
		}
	}
}
```

## Performance Improvements

### Expected Results

| Metric         | Phase 1 | Phase 2 | Improvement     |
| -------------- | ------- | ------- | --------------- |
| Token Usage    | -30%    | -50%    | Additional -20% |
| API Calls      | -40%    | -70%    | Additional -30% |
| Response Time  | -40%    | -60%    | Additional -20% |
| Cache Hit Rate | 70%     | 85%     | +15%            |
| Cost Reduction | 35%     | 60%     | Additional -25% |

### Batch Processing Benefits

- **Word Details**: 20 individual calls → 1 batch call = 95% reduction
- **Evaluations**: 10 individual calls → 1 batch call = 90% reduction
- **Queue Management**: Smart prioritization reduces wait times
- **Token Efficiency**: Shared context reduces prompt overhead

### Semantic Cache Benefits

- **Similar Requests**: 80%+ similarity threshold catches variations
- **Keyword Matching**: Related terms trigger cache hits
- **Multilingual Support**: Cross-language semantic matching
- **Adaptive Thresholds**: Learning from usage patterns

### Model Selection Benefits

- **Cost Optimization**: Right model for right task
- **Quality Assurance**: Complex tasks get better models
- **Performance Tuning**: Adaptive learning improves selection
- **Budget Control**: Automatic cost-conscious routing

## Usage Examples

### Batch Processing

```typescript
// Multiple word detail requests get automatically batched
const promises = [
	llmService.generateWordDetails(['apple', 'banana']),
	llmService.generateWordDetails(['orange', 'grape']),
	llmService.generateWordDetails(['cherry', 'mango']),
];

// All 3 requests combined into 1 API call
const results = await Promise.all(promises);
```

### Semantic Caching

```typescript
// First request
const result1 = await llmService.generateParagraph({
	keywords: ['technology', 'innovation'],
	language: 'EN',
	difficulty: 'intermediate',
});

// Similar request hits semantic cache
const result2 = await llmService.generateParagraph({
	keywords: ['tech', 'innovation', 'digital'],
	language: 'EN',
	difficulty: 'intermediate',
});
// Cache hit with 85% similarity
```

### Model Selection

```typescript
// Simple vocabulary generation
const words = await llmService.generateRandomTerms({
	maxTerms: 10,
	keywords: ['basic'],
});
// Automatically uses gpt-4o-mini (cost-effective)

// Complex evaluation
const evaluation = await llmService.evaluateAnswers({
	paragraph: longText,
	questions: complexQuestions,
	answers: userAnswers,
});
// Automatically uses gpt-4o (higher quality)
```

## Monitoring & Analytics

### Real-time Metrics

```typescript
// Batch processing metrics
const batchStats = await fetch('/api/token-monitor/batch-stats');
console.log('Queue length:', batchStats.queueLength);
console.log('Processing:', batchStats.processing);

// Semantic cache metrics
const cacheStats = await fetch('/api/token-monitor/cache-stats');
console.log('Semantic hit rate:', cacheStats.semantic.hitRate);
console.log('Keyword index size:', cacheStats.semantic.totalKeywords);

// Model selection metrics
const modelStats = await fetch('/api/token-monitor/model-stats');
console.log('Model performance:', modelStats.models);
```

### Optimization Recommendations

System tự động generate suggestions:

```json
{
	"type": "batch",
	"operation": "generateWordDetails",
	"description": "High frequency operation with 50 requests. Increase batch size to reduce API calls.",
	"estimatedSavings": {
		"tokens": 15000,
		"cost": 0.045,
		"percentage": 75
	},
	"priority": "high"
}
```

## Troubleshooting

### Common Issues

1. **Batch Timeout**

    ```typescript
    // Increase timeout for complex operations
    await batchProcessor.addToBatch(operation, params, 'normal', 60000);
    ```

2. **Semantic Cache Miss**

    ```typescript
    // Lower similarity threshold
    const result = await semanticCache.getWithSemantic(key, params, 0.7);
    ```

3. **Model Selection Override**
    ```typescript
    // Force specific model
    const selection = modelSelector.selectModel(operation, {
    	...params,
    	forceModel: 'gpt-4o-mini',
    });
    ```

### Performance Tuning

```typescript
// Adjust batch sizes based on usage
batchProcessor.config.maxBatchSize.generateWordDetails = 30;

// Tune semantic similarity weights
semanticCache.config.keywordWeight = 0.5;
semanticCache.config.semanticWeight = 0.2;

// Update model performance manually
modelSelector.updateModelPerformance('gpt-4o-mini', 1200, 0.9, true);
```

## Next Steps

Phase 2 hoàn thành foundation cho advanced optimizations. Tiếp theo:

1. **Phase 3**: Custom model fine-tuning và hybrid approaches
2. **Predictive Analytics**: ML-based optimization suggestions
3. **User Behavior Analysis**: Personalized optimization strategies
4. **Enterprise Features**: Multi-tenant optimization và cost allocation

## Migration từ Phase 1

Phase 2 backward compatible với Phase 1. Để enable:

1. Update `.env` với Phase 2 settings
2. Restart application
3. Monitor new metrics qua API endpoints
4. Adjust configurations based on usage patterns

Tất cả existing code sẽ tự động benefit từ new optimizations.
