# Automated Infographic Generation Development Plan

## Overview

Implement an AI-powered system that automatically generates educational infographics, visual summaries, and interactive diagrams to enhance vocabulary learning through visual representation and data visualization.

## Technical Architecture

### Database Schema Extensions

#### Infographic Models

```prisma
model Infographic {
  id              String              @id @default(uuid())
  title           String
  description     String?
  type            InfographicType
  category        InfographicCategory
  language        Language            @default(EN)
  difficulty      Difficulty          @default(BEGINNER)
  content         Json                // Structured content data
  layout          Json                // Layout configuration
  style           Json                // Visual style settings
  generatedUrl    String?             // Generated infographic URL
  thumbnailUrl    String?             // Thumbnail image URL
  isPublic        Boolean             @default(false)
  isTemplate      Boolean             @default(false)
  viewCount       Int                 @default(0)
  downloadCount   Int                 @default(0)
  rating          Float               @default(0.0)
  ratingCount     Int                 @default(0)
  generationTime  Int?                // Time taken to generate (ms)
  createdBy       String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  creator         User?               @relation(fields: [createdBy], references: [id])
  words           InfographicWord[]
  collections     InfographicCollection[]
  ratings         InfographicRating[]
  shares          InfographicShare[]

  @@index([type, category])
  @@index([language, difficulty])
  @@index([isPublic, isTemplate])
  @@map("infographics")
}

model InfographicWord {
  id              String              @id @default(uuid())
  infographicId   String
  wordId          String
  position        Json                // Position in infographic
  visualStyle     Json                // Word-specific styling
  connections     Json?               // Connections to other words
  importance      Float               @default(0.5) // 0-1 importance score

  infographic     Infographic         @relation(fields: [infographicId], references: [id], onDelete: Cascade)
  word            Word                @relation(fields: [wordId], references: [id], onDelete: Cascade)

  @@unique([infographicId, wordId])
  @@index([infographicId])
  @@map("infographic_words")
}

model InfographicCollection {
  id              String              @id @default(uuid())
  infographicId   String
  collectionId    String
  includeWords    Boolean             @default(true)
  includeParagraphs Boolean           @default(false)
  customFilters   Json?               // Additional filters

  infographic     Infographic         @relation(fields: [infographicId], references: [id], onDelete: Cascade)
  collection      Collection          @relation(fields: [collectionId], references: [id], onDelete: Cascade)

  @@unique([infographicId, collectionId])
  @@map("infographic_collections")
}

model InfographicTemplate {
  id              String              @id @default(uuid())
  name            String
  description     String?
  type            InfographicType
  category        InfographicCategory
  layout          Json                // Template layout
  style           Json                // Template styling
  placeholders    Json                // Content placeholders
  isOfficial      Boolean             @default(false)
  usageCount      Int                 @default(0)
  createdBy       String?
  createdAt       DateTime            @default(now())

  creator         User?               @relation(fields: [createdBy], references: [id])

  @@index([type, category])
  @@index([isOfficial])
  @@map("infographic_templates")
}

model InfographicRating {
  id              String              @id @default(uuid())
  infographicId   String
  userId          String
  rating          Int                 // 1-5 scale
  feedback        String?
  createdAt       DateTime            @default(now())

  infographic     Infographic         @relation(fields: [infographicId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([infographicId, userId])
  @@index([infographicId])
  @@map("infographic_ratings")
}

model InfographicShare {
  id              String              @id @default(uuid())
  infographicId   String
  sharedBy        String
  sharedWith      String?             // Null for public shares
  shareType       ShareType           @default(VIEW)
  shareUrl        String?             // Generated share URL
  accessCount     Int                 @default(0)
  expiresAt       DateTime?
  createdAt       DateTime            @default(now())

  infographic     Infographic         @relation(fields: [infographicId], references: [id], onDelete: Cascade)
  sharer          User                @relation("SharedInfographics", fields: [sharedBy], references: [id])
  recipient       User?               @relation("ReceivedInfographics", fields: [sharedWith], references: [id])

  @@index([infographicId])
  @@index([sharedWith])
  @@map("infographic_shares")
}

model GenerationRequest {
  id              String              @id @default(uuid())
  userId          String
  type            InfographicType
  parameters      Json                // Generation parameters
  status          GenerationStatus    @default(PENDING)
  progress        Float               @default(0.0)
  resultId        String?             // Generated infographic ID
  errorMessage    String?
  estimatedTime   Int?                // Estimated completion time
  actualTime      Int?                // Actual generation time
  createdAt       DateTime            @default(now())
  completedAt     DateTime?

  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@map("generation_requests")
}

enum InfographicType {
  WORD_CLOUD
  MIND_MAP
  TIMELINE
  COMPARISON_CHART
  PROCESS_DIAGRAM
  VOCABULARY_TREE
  CONCEPT_MAP
  STORY_BOARD
  FLASHCARD_SET
  POSTER
  INTERACTIVE_DIAGRAM
}

enum InfographicCategory {
  VOCABULARY
  GRAMMAR
  PRONUNCIATION
  CULTURE
  ETYMOLOGY
  USAGE_EXAMPLES
  SYNONYMS_ANTONYMS
  WORD_FAMILIES
  THEMATIC_GROUPS
}

enum ShareType {
  VIEW
  DOWNLOAD
  EMBED
  COLLABORATE
}

enum GenerationStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}
```

#### User and Collection Model Extensions

```prisma
model User {
  // ... existing fields
  createdInfographics   Infographic[]
  infographicTemplates  InfographicTemplate[]
  infographicRatings    InfographicRating[]
  sharedInfographics    InfographicShare[]    @relation("SharedInfographics")
  receivedInfographics  InfographicShare[]    @relation("ReceivedInfographics")
  generationRequests    GenerationRequest[]
}

model Collection {
  // ... existing fields
  infographics          InfographicCollection[]
}

model Word {
  // ... existing fields
  infographicWords      InfographicWord[]
}
```

### Backend Implementation

#### Services

**Infographic Generation Service** (`src/backend/services/infographic-generation.service.ts`)

```typescript
export interface InfographicGenerationService {
	generateInfographic(
		userId: string,
		request: GenerateInfographicDto
	): Promise<GenerationRequest>;
	getGenerationStatus(requestId: string): Promise<GenerationRequest>;
	getInfographics(filters: InfographicFilters): Promise<Infographic[]>;
	getUserInfographics(userId: string): Promise<Infographic[]>;
	updateInfographic(
		userId: string,
		infographicId: string,
		updates: UpdateInfographicDto
	): Promise<Infographic>;
	shareInfographic(
		userId: string,
		infographicId: string,
		shareData: ShareInfographicDto
	): Promise<InfographicShare>;
	rateInfographic(
		userId: string,
		infographicId: string,
		rating: number,
		feedback?: string
	): Promise<InfographicRating>;
}

export class InfographicGenerationServiceImpl implements InfographicGenerationService {
	constructor(
		private getInfographicRepository: () => InfographicRepository,
		private getGenerationRequestRepository: () => GenerationRequestRepository,
		private getInfographicTemplateRepository: () => InfographicTemplateRepository,
		private getAIGenerationService: () => AIGenerationService,
		private getImageGenerationService: () => ImageGenerationService,
		private getDataVisualizationService: () => DataVisualizationService
	) {}

	async generateInfographic(
		userId: string,
		request: GenerateInfographicDto
	): Promise<GenerationRequest> {
		// Validate request parameters
		await this.validateGenerationRequest(request);

		// Create generation request
		const generationRequest = await this.getGenerationRequestRepository().create({
			userId,
			type: request.type,
			parameters: request.parameters,
			status: GenerationStatus.PENDING,
			estimatedTime: this.estimateGenerationTime(request.type, request.parameters),
		});

		// Queue for processing
		await this.queueGeneration(generationRequest);

		return generationRequest;
	}

	private async queueGeneration(request: GenerationRequest): Promise<void> {
		// Process generation asynchronously
		this.processGeneration(request).catch((error) => {
			console.error('Generation failed:', error);
			this.getGenerationRequestRepository().update(request.id, {
				status: GenerationStatus.FAILED,
				errorMessage: error.message,
				completedAt: new Date(),
			});
		});
	}

	private async processGeneration(request: GenerationRequest): Promise<void> {
		await this.getGenerationRequestRepository().update(request.id, {
			status: GenerationStatus.PROCESSING,
			progress: 0.1,
		});

		try {
			// Step 1: Gather content data
			const contentData = await this.gatherContentData(request.parameters);
			await this.updateProgress(request.id, 0.3);

			// Step 2: Select or create template
			const template = await this.selectTemplate(request.type, request.parameters);
			await this.updateProgress(request.id, 0.5);

			// Step 3: Generate layout
			const layout = await this.generateLayout(contentData, template);
			await this.updateProgress(request.id, 0.7);

			// Step 4: Apply styling and generate visual
			const infographic = await this.generateVisual(contentData, layout, template);
			await this.updateProgress(request.id, 0.9);

			// Step 5: Save and finalize
			const savedInfographic = await this.saveInfographic(request.userId, infographic);

			await this.getGenerationRequestRepository().update(request.id, {
				status: GenerationStatus.COMPLETED,
				progress: 1.0,
				resultId: savedInfographic.id,
				completedAt: new Date(),
				actualTime: Date.now() - new Date(request.createdAt).getTime(),
			});
		} catch (error) {
			throw error;
		}
	}

	private async gatherContentData(parameters: any): Promise<ContentData> {
		const data: ContentData = {
			words: [],
			definitions: [],
			examples: [],
			images: [],
			statistics: {},
		};

		if (parameters.collectionId) {
			// Gather data from collection
			const collection = await this.getCollectionRepository().findById(
				parameters.collectionId
			);
			if (collection) {
				data.words = await this.getWordRepository().findByIds(collection.word_ids);
				data.statistics.totalWords = data.words.length;
				data.statistics.averageDifficulty = this.calculateAverageDifficulty(data.words);
			}
		}

		if (parameters.wordIds) {
			// Gather data from specific words
			data.words = await this.getWordRepository().findByIds(parameters.wordIds);
		}

		// Enrich with additional data
		for (const word of data.words) {
			const definitions = await this.getDefinitionRepository().findByWordId(word.id);
			data.definitions.push(...definitions);

			const examples = await this.getExampleRepository().findByWordId(word.id);
			data.examples.push(...examples);

			const images = await this.getWordImageRepository().findByWordId(word.id);
			data.images.push(...images);
		}

		return data;
	}

	private async generateLayout(
		contentData: ContentData,
		template: InfographicTemplate
	): Promise<LayoutData> {
		switch (template.type) {
			case InfographicType.WORD_CLOUD:
				return this.generateWordCloudLayout(contentData);
			case InfographicType.MIND_MAP:
				return this.generateMindMapLayout(contentData);
			case InfographicType.VOCABULARY_TREE:
				return this.generateVocabularyTreeLayout(contentData);
			case InfographicType.COMPARISON_CHART:
				return this.generateComparisonChartLayout(contentData);
			default:
				return this.generateDefaultLayout(contentData, template);
		}
	}

	private async generateWordCloudLayout(contentData: ContentData): Promise<LayoutData> {
		// Calculate word frequencies and importance
		const wordFrequencies = this.calculateWordFrequencies(contentData.words);
		const wordImportance = this.calculateWordImportance(contentData.words);

		// Generate positions using force-directed layout
		const positions = await this.getDataVisualizationService().generateWordCloudPositions(
			wordFrequencies,
			wordImportance
		);

		return {
			type: 'word_cloud',
			elements: contentData.words.map((word, index) => ({
				id: word.id,
				type: 'word',
				content: word.term,
				position: positions[index],
				size: this.calculateWordSize(wordFrequencies[word.term], wordImportance[word.id]),
				style: this.getWordStyle(word, wordImportance[word.id]),
			})),
			dimensions: { width: 1200, height: 800 },
		};
	}

	private async generateMindMapLayout(contentData: ContentData): Promise<LayoutData> {
		// Group words by semantic similarity
		const wordGroups = await this.getAIGenerationService().groupWordsBySemantic(
			contentData.words
		);

		// Generate hierarchical layout
		const hierarchy = this.buildWordHierarchy(wordGroups);
		const positions = this.calculateMindMapPositions(hierarchy);

		return {
			type: 'mind_map',
			elements: this.createMindMapElements(hierarchy, positions),
			connections: this.createMindMapConnections(hierarchy),
			dimensions: { width: 1400, height: 1000 },
		};
	}

	private async generateVisual(
		contentData: ContentData,
		layout: LayoutData,
		template: InfographicTemplate
	): Promise<InfographicData> {
		// Apply template styling
		const styledLayout = this.applyTemplateStyle(layout, template.style);

		// Generate the actual visual
		const visualUrl = await this.getImageGenerationService().generateInfographic(styledLayout);

		// Generate thumbnail
		const thumbnailUrl = await this.getImageGenerationService().generateThumbnail(visualUrl);

		return {
			title: this.generateTitle(contentData, layout.type),
			description: this.generateDescription(contentData),
			type: template.type,
			category: this.determineCategory(contentData),
			content: contentData,
			layout: styledLayout,
			style: template.style,
			generatedUrl: visualUrl,
			thumbnailUrl,
		};
	}

	private calculateWordFrequencies(words: Word[]): Record<string, number> {
		const frequencies: Record<string, number> = {};

		words.forEach((word) => {
			frequencies[word.term] = (frequencies[word.term] || 0) + 1;
		});

		return frequencies;
	}

	private calculateWordImportance(words: Word[]): Record<string, number> {
		const importance: Record<string, number> = {};

		words.forEach((word) => {
			// Calculate importance based on various factors
			let score = 0.5; // Base score

			// Frequency in definitions and examples
			score += this.getWordUsageFrequency(word) * 0.3;

			// Difficulty level
			score += this.getDifficultyScore(word) * 0.2;

			// User interaction data
			score += this.getUserInteractionScore(word) * 0.3;

			importance[word.id] = Math.min(1.0, score);
		});

		return importance;
	}

	private estimateGenerationTime(type: InfographicType, parameters: any): number {
		const baseTimes = {
			[InfographicType.WORD_CLOUD]: 30000, // 30 seconds
			[InfographicType.MIND_MAP]: 60000, // 1 minute
			[InfographicType.VOCABULARY_TREE]: 45000, // 45 seconds
			[InfographicType.COMPARISON_CHART]: 40000, // 40 seconds
			[InfographicType.INTERACTIVE_DIAGRAM]: 90000, // 1.5 minutes
		};

		const baseTime = baseTimes[type] || 45000;
		const wordCount = parameters.wordIds?.length || parameters.maxWords || 50;

		// Scale based on content complexity
		const complexityMultiplier = Math.min(2.0, 1 + wordCount / 100);

		return Math.round(baseTime * complexityMultiplier);
	}
}
```

### Frontend Implementation

#### Components

**Infographic Generator Component** (`src/components/ui/infographic-generator.tsx`)

```typescript
interface InfographicGeneratorProps {
  collectionId?: string;
  wordIds?: string[];
  onGenerated: (infographic: Infographic) => void;
}

export function InfographicGenerator({
  collectionId,
  wordIds,
  onGenerated
}: InfographicGeneratorProps) {
  const [selectedType, setSelectedType] = useState<InfographicType>(InfographicType.WORD_CLOUD);
  const [parameters, setParameters] = useState<GenerationParameters>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  const infographicTypes = [
    {
      type: InfographicType.WORD_CLOUD,
      name: 'Word Cloud',
      description: 'Visual representation of word frequency and importance',
      icon: <Cloud className="w-6 h-6" />,
      estimatedTime: '30 seconds',
    },
    {
      type: InfographicType.MIND_MAP,
      name: 'Mind Map',
      description: 'Hierarchical visualization of word relationships',
      icon: <Network className="w-6 h-6" />,
      estimatedTime: '1 minute',
    },
    {
      type: InfographicType.VOCABULARY_TREE,
      name: 'Vocabulary Tree',
      description: 'Tree structure showing word families and connections',
      icon: <TreePine className="w-6 h-6" />,
      estimatedTime: '45 seconds',
    },
    {
      type: InfographicType.COMPARISON_CHART,
      name: 'Comparison Chart',
      description: 'Compare words, definitions, and usage patterns',
      icon: <BarChart className="w-6 h-6" />,
      estimatedTime: '40 seconds',
    },
  ];

  const handleGenerate = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const request = await generateInfographicApi({
        type: selectedType,
        parameters: {
          ...parameters,
          collectionId,
          wordIds,
        },
      });

      // Poll for progress
      const pollProgress = setInterval(async () => {
        const status = await getGenerationStatusApi(request.id);
        setGenerationProgress(status.progress);

        if (status.status === GenerationStatus.COMPLETED) {
          clearInterval(pollProgress);
          const infographic = await getInfographicApi(status.resultId!);
          onGenerated(infographic);
          setIsGenerating(false);
        } else if (status.status === GenerationStatus.FAILED) {
          clearInterval(pollProgress);
          setIsGenerating(false);
          toast.error('Generation failed: ' + status.errorMessage);
        }
      }, 2000);

    } catch (error) {
      setIsGenerating(false);
      toast.error('Failed to start generation');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Choose Infographic Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {infographicTypes.map(({ type, name, description, icon, estimatedTime }) => (
            <div
              key={type}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedType === type
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedType(type)}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${
                  selectedType === type ? 'bg-blue-500 text-white' : 'bg-gray-100'
                }`}>
                  {icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{name}</h4>
                  <p className="text-sm text-gray-600 mt-1">{description}</p>
                  <p className="text-xs text-gray-500 mt-2">
                    Estimated time: {estimatedTime}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Type-specific parameters */}
      <div>
        <h4 className="font-medium mb-3">Customization Options</h4>
        <InfographicParametersForm
          type={selectedType}
          parameters={parameters}
          onChange={setParameters}
        />
      </div>

      {/* Generation controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          {collectionId && 'Using current collection'}
          {wordIds && `Using ${wordIds.length} selected words`}
        </div>

        <Button
          onClick={handleGenerate}
          disabled={isGenerating}
          className="min-w-32"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4 mr-2" />
              Generate
            </>
          )}
        </Button>
      </div>

      {/* Progress indicator */}
      {isGenerating && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Generation Progress</span>
            <span>{Math.round(generationProgress * 100)}%</span>
          </div>
          <Progress value={generationProgress * 100} className="w-full" />
        </div>
      )}
    </div>
  );
}
```

**Infographic Viewer Component** (`src/components/ui/infographic-viewer.tsx`)

```typescript
interface InfographicViewerProps {
  infographic: Infographic;
  interactive?: boolean;
  onWordClick?: (wordId: string) => void;
  onShare?: () => void;
  onDownload?: () => void;
}

export function InfographicViewer({
  infographic,
  interactive = false,
  onWordClick,
  onShare,
  onDownload
}: InfographicViewerProps) {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{infographic.title}</h3>
            {infographic.description && (
              <p className="text-gray-600 text-sm mt-1">{infographic.description}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant="secondary">{infographic.type}</Badge>
            <div className="flex items-center space-x-1">
              <Button size="sm" variant="outline" onClick={onShare}>
                <Share2 className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={onDownload}>
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Infographic content */}
      <div className="relative">
        {/* Zoom controls */}
        <div className="absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2">
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            <span className="text-sm font-medium min-w-12 text-center">
              {Math.round(zoomLevel * 100)}%
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Interactive infographic */}
        <div
          className="overflow-auto max-h-96"
          style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}
        >
          {infographic.generatedUrl ? (
            <img
              src={infographic.generatedUrl}
              alt={infographic.title}
              className="w-full h-auto"
            />
          ) : (
            <InfographicRenderer
              infographic={infographic}
              interactive={interactive}
              selectedElement={selectedElement}
              onElementClick={(elementId, elementType) => {
                setSelectedElement(elementId);
                if (elementType === 'word' && onWordClick) {
                  onWordClick(elementId);
                }
              }}
            />
          )}
        </div>
      </div>

      {/* Footer with stats */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>{infographic.viewCount} views</span>
            </span>
            <span className="flex items-center space-x-1">
              <Download className="w-4 h-4" />
              <span>{infographic.downloadCount} downloads</span>
            </span>
            {infographic.rating > 0 && (
              <span className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>{infographic.rating.toFixed(1)} ({infographic.ratingCount})</span>
              </span>
            )}
          </div>

          <span>
            Created {formatDistanceToNow(new Date(infographic.createdAt))} ago
          </span>
        </div>
      </div>
    </div>
  );
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic infographic service and repository
- Template system foundation

### Phase 2 (Weeks 3-4): AI Generation Engine

- Content analysis and processing
- Layout generation algorithms
- Visual rendering pipeline

### Phase 3 (Weeks 5-6): Frontend Components

- Infographic generator interface
- Interactive viewer components
- Template management system

### Phase 4 (Weeks 7-8): Advanced Features

- Real-time collaboration
- Advanced customization options
- Performance optimization

## Success Metrics

- Infographic generation success rate
- User engagement with generated content
- Download and sharing rates
- Visual learning effectiveness
- Template usage and creation

## Future Enhancements

- Video infographic generation
- Interactive web-based infographics
- Collaborative editing features
- Advanced AI-powered layouts
- Integration with presentation tools
