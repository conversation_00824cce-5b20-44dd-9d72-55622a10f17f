# Kế Hoạch Tối Ưu Chi Phí Token OpenAI API

## Tổng Quan Hiện Tại

### Cấu Hình OpenAI Hiện Tại

- **Model**: `gpt-4o-mini` (từ env `LLM_OPENAI_MODEL`)
- **Max Tokens**: 1000-2000 tokens mỗi request
- **Temperature**: 0.3-0.7 tùy theo use case
- **Retry Logic**: 3 lần thử lại cho mỗi request

### Các Chức Năng Sử dụng OpenAI API

1. **Tạo từ vựng ngẫu nhiên** (`generateRandomTerms`)
2. **Tạo chi tiết từ vựng** (`generateWordDetails`)
3. **Tạo đoạn văn** (`generateParagraph`)
4. **Tạo bài tập** (`generateExercises`)
5. **Đ<PERSON>h giá bản dịch** (`evaluateTranslation`)
6. **Tạo câu hỏi** (`generateQuestions`)
7. **Tạo đoạn văn với câu hỏi** (`generateParagraphWithQuestions`)
8. **<PERSON><PERSON>h giá câu trả lời** (`evaluateAnswers`)
9. **Tạo bài tập ngữ pháp** (`generateGrammarPractice`)

## Phân Tích Chi Phí Token

### Chi Phí Ước Tính Theo Chức Năng

| Chức năng            | Input Tokens | Output Tokens | Tần suất sử dụng | Chi phí/request |
| -------------------- | ------------ | ------------- | ---------------- | --------------- |
| Tạo từ vựng          | 500-800      | 200-500       | Cao              | $0.0002-0.0005  |
| Chi tiết từ vựng     | 300-600      | 800-1500      | Cao              | $0.0003-0.0008  |
| Tạo đoạn văn         | 400-700      | 500-1000      | Trung bình       | $0.0003-0.0007  |
| Đánh giá bản dịch    | 600-1000     | 300-600       | Trung bình       | $0.0004-0.0008  |
| Tạo câu hỏi          | 500-800      | 300-800       | Trung bình       | $0.0003-0.0007  |
| Đánh giá câu trả lời | 800-1500     | 500-1000      | Cao              | $0.0006-0.0012  |

_Giá gpt-4o-mini: $0.00015/1K input tokens, $0.0006/1K output tokens_

## Chiến Lược Tối Ưu Chi Phí

### 1. Tối Ưu Prompt Engineering

#### A. Rút Gọn System Prompts

```typescript
// Hiện tại - dài dòng
const currentPrompt = `Generate ${maxTerms} ${this.getLanguageName(
	target_language
)} vocabulary terms for ${this.getLanguageName(source_language)} native speakers.

Keywords: ${keywordTerms.join(', ')}
Exclude: ${allExcludes.join(', ')}
Format: lowercase (except proper nouns)
Types: mix nouns, verbs, adjectives
Level: appropriate for language learners`;

// Tối ưu - ngắn gọn
const optimizedPrompt = `Gen ${maxTerms} ${target_language} vocab for ${source_language} speakers.
KW: ${keywordTerms.join(',')}
Excl: ${allExcludes.join(',')}
Format: lowercase, mix types, learner level`;
```

#### B. Sử Dụng Structured Output

- Thay vì text parsing, sử dụng `response_format` với JSON schema
- Giảm tokens cần thiết cho format instructions
- Tăng độ chính xác, giảm retry

### 2. Caching Thông Minh

#### A. Multi-Level Caching

```typescript
interface TokenOptimizedCache {
	// Level 1: Exact match cache
	exactCache: Map<string, CachedResult>;

	// Level 2: Semantic similarity cache
	semanticCache: Map<string, CachedResult>;

	// Level 3: Partial result cache
	partialCache: Map<string, PartialResult>;
}
```

#### B. Cache Strategy

- **Từ vựng**: Cache theo keywords + language pair (TTL: 7 ngày)
- **Đoạn văn**: Cache theo keywords + difficulty + length (TTL: 3 ngày)
- **Đánh giá**: Cache theo content hash (TTL: 30 ngày)

### 3. Batch Processing

#### A. Gộp Requests

```typescript
// Thay vì 10 requests riêng lẻ
for (const term of terms) {
	await generateWordDetails([term]);
}

// Gộp thành 1 request
await generateWordDetails(terms); // Xử lý tối đa 20 terms/request
```

#### B. Smart Batching

- Gộp requests có cùng context
- Ưu tiên batch cho high-frequency operations
- Implement queue system cho batch processing

### 4. Model Selection Strategy

#### A. Tiered Model Usage

```typescript
const modelTiers = {
	simple: 'gpt-4o-mini', // Tạo từ vựng, format đơn giản
	standard: 'gpt-4o-mini', // Đa số use cases
	complex: 'gpt-4o', // Đánh giá phức tạp, creative writing
};
```

#### B. Dynamic Model Selection

- Phân tích complexity của request
- Tự động chọn model phù hợp
- Fallback mechanism

### 5. Token Limit Management

#### A. Intelligent Truncation

```typescript
function optimizePromptLength(prompt: string, maxTokens: number): string {
	const estimatedTokens = prompt.length / 4; // Rough estimation

	if (estimatedTokens > maxTokens * 0.7) {
		// Truncate examples, keep core instructions
		return truncateExamples(prompt, maxTokens);
	}

	return prompt;
}
```

#### B. Progressive Loading

- Tạo content theo chunks nhỏ
- Combine results client-side
- Reduce individual request size

## Implementation Plan

### Phase 1: Immediate Optimizations (1-2 tuần)

1. **Optimize Existing Prompts**
    - Rút gọn system prompts 30-50%
    - Implement structured output cho tất cả endpoints
    - Add prompt compression utility

2. **Enhanced Caching**
    - Extend cache TTL cho stable content
    - Add semantic similarity matching
    - Implement cache warming strategies

3. **Batch Processing**
    - Combine word detail generation
    - Batch evaluation requests
    - Queue system cho non-urgent requests

### Phase 2: Advanced Optimizations (2-4 tuần)

1. **Smart Model Selection**
    - Implement complexity analysis
    - Dynamic model routing
    - Performance monitoring

2. **Token Usage Analytics**
    - Track token consumption per feature
    - Identify optimization opportunities
    - Cost monitoring dashboard

3. **Predictive Caching**
    - User behavior analysis
    - Pre-generate popular content
    - Intelligent prefetching

### Phase 3: Advanced Features (1-2 tháng)

1. **Custom Model Fine-tuning**
    - Train smaller models cho specific tasks
    - Reduce dependency on large models
    - Task-specific optimization

2. **Hybrid Approach**
    - Combine multiple AI providers
    - Cost-based routing
    - Quality vs cost optimization

## Monitoring & Metrics

### Key Performance Indicators

1. **Cost Metrics**
    - Token usage per user per day
    - Cost per feature usage
    - Monthly API spend trends

2. **Performance Metrics**
    - Cache hit rates
    - Average response time
    - Request success rates

3. **Quality Metrics**
    - User satisfaction scores
    - Content quality ratings
    - Error rates

### Monitoring Tools

```typescript
interface TokenUsageMonitor {
	trackRequest(endpoint: string, inputTokens: number, outputTokens: number): void;
	getCostAnalysis(timeframe: string): CostAnalysis;
	getOptimizationSuggestions(): OptimizationSuggestion[];
	alertOnThreshold(threshold: number): void;
}
```

## Expected Results

### Cost Reduction Targets

- **Phase 1**: 30-40% reduction trong token usage
- **Phase 2**: 50-60% reduction tổng chi phí API
- **Phase 3**: 70%+ optimization với custom solutions

### Performance Improvements

- **Response Time**: Giảm 40-60% nhờ caching
- **Cache Hit Rate**: Đạt 70-80% cho common requests
- **User Experience**: Faster loading, better reliability

## Risk Mitigation

### Potential Risks

1. **Quality Degradation**: Shorter prompts có thể giảm chất lượng
2. **Cache Staleness**: Cached content có thể outdated
3. **Complexity**: Advanced optimizations tăng system complexity

### Mitigation Strategies

1. **A/B Testing**: Test optimized prompts vs original
2. **Quality Monitoring**: Automated quality checks
3. **Gradual Rollout**: Implement changes incrementally
4. **Fallback Mechanisms**: Revert to original on quality issues

## Implementation Details

### Code Examples

#### 1. Optimized Prompt Templates

```typescript
// src/backend/services/prompt-optimizer.service.ts
export class PromptOptimizerService {
	private static readonly TEMPLATES = {
		vocabulary: {
			original: `Generate {count} {target_lang} vocabulary terms for {source_lang} native speakers.\n\nKeywords: {keywords}\nExclude: {excludes}\nFormat: lowercase (except proper nouns)\nTypes: mix nouns, verbs, adjectives\nLevel: appropriate for language learners`,
			optimized: `Gen {count} {target_lang} vocab for {source_lang}.\nKW: {keywords}\nExcl: {excludes}\nMix types, learner level`,
		},
		evaluation: {
			original: `Evaluate the following translation from {source_lang} to {target_lang}.\n\nOriginal: {original}\nTranslation: {translation}\n\nProvide detailed feedback on accuracy, grammar, and naturalness.`,
			optimized: `Eval {source_lang}→{target_lang}:\nOrig: {original}\nTrans: {translation}\nRate accuracy, grammar, naturalness (1-10)`,
		},
	};

	static optimizePrompt(template: string, params: Record<string, any>): string {
		return template.replace(/\{(\w+)\}/g, (match, key) => params[key] || match);
	}

	static estimateTokens(text: string): number {
		// Improved estimation: 1 token ≈ 3.5 characters for Vietnamese/English
		return Math.ceil(text.length / 3.5);
	}
}
```

#### 2. Smart Caching Implementation

```typescript
// src/backend/services/smart-cache.service.ts
export class SmartCacheService extends CacheService {
	private semanticCache = new Map<string, CacheEntry>();

	async getWithSemantic<T>(key: string, similarityThreshold = 0.8): Promise<T | null> {
		// Exact match first
		const exact = this.get<T>(key);
		if (exact) return exact;

		// Semantic similarity search
		const similar = await this.findSimilarKey(key, similarityThreshold);
		if (similar) {
			return this.get<T>(similar);
		}

		return null;
	}

	private async findSimilarKey(key: string, threshold: number): Promise<string | null> {
		// Implement semantic similarity using embeddings or fuzzy matching
		// For now, simple keyword matching
		const keyWords = key.toLowerCase().split(/[^a-z0-9]/);

		for (const [cachedKey] of this.semanticCache) {
			const cachedWords = cachedKey.toLowerCase().split(/[^a-z0-9]/);
			const similarity = this.calculateSimilarity(keyWords, cachedWords);

			if (similarity >= threshold) {
				return cachedKey;
			}
		}

		return null;
	}

	private calculateSimilarity(words1: string[], words2: string[]): number {
		const set1 = new Set(words1);
		const set2 = new Set(words2);
		const intersection = new Set([...set1].filter((x) => set2.has(x)));
		const union = new Set([...set1, ...set2]);

		return intersection.size / union.size;
	}
}
```

#### 3. Batch Processing Service

```typescript
// src/backend/services/batch-processor.service.ts
export class BatchProcessorService {
	private queue: BatchRequest[] = [];
	private processing = false;

	async addToBatch<T>(
		operation: string,
		params: any,
		priority: 'high' | 'normal' | 'low' = 'normal'
	): Promise<T> {
		return new Promise((resolve, reject) => {
			this.queue.push({
				operation,
				params,
				priority,
				resolve,
				reject,
				timestamp: Date.now(),
			});

			this.processBatch();
		});
	}

	private async processBatch(): Promise<void> {
		if (this.processing || this.queue.length === 0) return;

		this.processing = true;

		try {
			// Group by operation type
			const groups = this.groupByOperation();

			for (const [operation, requests] of groups) {
				await this.processBatchGroup(operation, requests);
			}
		} finally {
			this.processing = false;
		}
	}

	private async processBatchGroup(operation: string, requests: BatchRequest[]): Promise<void> {
		const batchSize = this.getBatchSize(operation);

		for (let i = 0; i < requests.length; i += batchSize) {
			const batch = requests.slice(i, i + batchSize);
			await this.executeBatch(operation, batch);
		}
	}

	private getBatchSize(operation: string): number {
		const sizes = {
			generateWordDetails: 20,
			generateRandomTerms: 1, // Already optimized
			evaluateAnswers: 10,
			generateQuestions: 5,
		};

		return sizes[operation] || 1;
	}
}
```

### Configuration Updates

#### Environment Variables

```bash
# .env additions
LLM_OPTIMIZATION_ENABLED=true
LLM_CACHE_TTL_VOCABULARY=604800  # 7 days
LLM_CACHE_TTL_PARAGRAPHS=259200  # 3 days
LLM_CACHE_TTL_EVALUATIONS=2592000 # 30 days
LLM_BATCH_SIZE_WORDS=20
LLM_BATCH_SIZE_EVALUATIONS=10
LLM_SEMANTIC_CACHE_THRESHOLD=0.8
LLM_TOKEN_BUDGET_DAILY=100000    # Daily token limit
LLM_COST_ALERT_THRESHOLD=50      # USD per day
```

#### Updated LLM Config

```typescript
// src/config/config.ts - additions
export async function getLLMOptimizationConfig() {
	return {
		enabled: env.LLM_OPTIMIZATION_ENABLED === 'true',
		cacheTTL: {
			vocabulary: Number(env.LLM_CACHE_TTL_VOCABULARY) || 604800,
			paragraphs: Number(env.LLM_CACHE_TTL_PARAGRAPHS) || 259200,
			evaluations: Number(env.LLM_CACHE_TTL_EVALUATIONS) || 2592000,
		},
		batchSizes: {
			words: Number(env.LLM_BATCH_SIZE_WORDS) || 20,
			evaluations: Number(env.LLM_BATCH_SIZE_EVALUATIONS) || 10,
		},
		semanticCacheThreshold: Number(env.LLM_SEMANTIC_CACHE_THRESHOLD) || 0.8,
		tokenBudget: {
			daily: Number(env.LLM_TOKEN_BUDGET_DAILY) || 100000,
		},
		costAlerts: {
			dailyThreshold: Number(env.LLM_COST_ALERT_THRESHOLD) || 50,
		},
	};
}
```

## Best Practices

### 1. Token Estimation

- Sử dụng tiktoken library cho estimation chính xác
- Monitor actual vs estimated usage
- Adjust estimation algorithms based on real data

### 2. Error Handling

- Implement exponential backoff cho rate limits
- Graceful degradation khi budget exceeded
- Fallback to cached/simplified responses

### 3. Quality Assurance

- A/B test optimized vs original prompts
- Monitor user satisfaction metrics
- Automated quality scoring system

### 4. Cost Monitoring

- Real-time cost tracking dashboard
- Daily/weekly budget alerts
- Per-feature cost attribution

## Migration Strategy

### Week 1-2: Foundation

1. Implement PromptOptimizerService
2. Add token estimation utilities
3. Setup monitoring infrastructure

### Week 3-4: Caching Enhancement

1. Deploy SmartCacheService
2. Implement semantic similarity
3. Add cache warming jobs

### Week 5-6: Batch Processing

1. Implement BatchProcessorService
2. Update API endpoints for batching
3. Test batch performance

### Week 7-8: Monitoring & Optimization

1. Deploy cost monitoring dashboard
2. Fine-tune cache strategies
3. Optimize based on real usage data

## Success Metrics

### Technical Metrics

- **Token Reduction**: 40-60% decrease in total tokens
- **Cache Hit Rate**: 70-80% for common requests
- **Response Time**: 30-50% improvement
- **API Cost**: 50-70% reduction

### Business Metrics

- **User Satisfaction**: Maintain >4.5/5 rating
- **Feature Usage**: No decrease in engagement
- **System Reliability**: 99.9% uptime maintained

## Conclusion

Kế hoạch này sẽ giúp giảm đáng kể chi phí OpenAI API trong khi duy trì hoặc cải thiện chất lượng dịch vụ. Việc implementation theo phases đảm bảo rủi ro thấp và có thể đo lường được kết quả từng bước.

Với các optimizations được đề xuất, dự án có thể tiết kiệm 50-70% chi phí API trong vòng 2-3 tháng, đồng thời cải thiện performance và user experience.
