# Learning Streaks System Development Plan

## Overview

Implement a comprehensive learning streak tracking system that motivates users through daily learning goals, streak preservation, and streak-based rewards in the vocabulary learning application.

## Technical Architecture

### Database Schema Extensions

#### Enhanced User Stats Model

```prisma
model UserStats {
  id                    String   @id @default(uuid())
  userId                String   @unique
  currentStreak         Int      @default(0)
  longestStreak         Int      @default(0)
  lastActivityDate      DateTime?
  streakFreezeCount     Int      @default(0) // Available streak freezes
  totalStreakFreezes    Int      @default(0) // Total freezes used
  streakGoal            Int      @default(1) // Daily goal (words/paragraphs)
  streakStartDate       DateTime?
  weeklyStreakCount     Int      @default(0)
  monthlyStreakCount    Int      @default(0)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  dailyActivities       DailyActivity[]
  streakMilestones      StreakMilestone[]

  @@index([userId])
  @@map("user_stats")
}

model DailyActivity {
  id                String   @id @default(uuid())
  userId            String
  date              DateTime @db.Date
  wordsReviewed     Int      @default(0)
  paragraphsRead    Int      @default(0)
  timeSpent         Int      @default(0) // in minutes
  goalAchieved      Boolean  @default(false)
  streakContributed Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  userStats         UserStats @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId, date])
  @@map("daily_activities")
}

model StreakMilestone {
  id          String   @id @default(uuid())
  userId      String
  streakDays  Int
  achievedAt  DateTime @default(now())
  rewardType  StreakRewardType
  rewardValue Json?
  claimed     Boolean  @default(false)

  userStats   UserStats @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("streak_milestones")
}

enum StreakRewardType {
  BADGE
  POINTS
  STREAK_FREEZE
  PREMIUM_FEATURE
  CUSTOM
}
```

### Backend Implementation

#### Services

**Streak Service** (`src/backend/services/streak.service.ts`)

```typescript
export interface StreakService {
	updateDailyActivity(userId: string, activity: DailyActivityUpdate): Promise<DailyActivity>;
	getCurrentStreak(userId: string): Promise<StreakInfo>;
	getStreakHistory(userId: string, days?: number): Promise<DailyActivity[]>;
	useStreakFreeze(userId: string): Promise<UserStats>;
	checkStreakMilestones(userId: string): Promise<StreakMilestone[]>;
	getStreakLeaderboard(period: 'weekly' | 'monthly' | 'allTime'): Promise<StreakLeaderboard[]>;
	setStreakGoal(userId: string, goal: number): Promise<UserStats>;
}

export class StreakServiceImpl implements StreakService {
	constructor(
		private getUserStatsRepository: () => UserStatsRepository,
		private getDailyActivityRepository: () => DailyActivityRepository,
		private getStreakMilestoneRepository: () => StreakMilestoneRepository
	) {}

	async updateDailyActivity(
		userId: string,
		activity: DailyActivityUpdate
	): Promise<DailyActivity> {
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		const existingActivity = await this.getDailyActivityRepository().findByUserAndDate(
			userId,
			today
		);

		const updatedActivity = {
			userId,
			date: today,
			wordsReviewed: (existingActivity?.wordsReviewed || 0) + (activity.wordsReviewed || 0),
			paragraphsRead:
				(existingActivity?.paragraphsRead || 0) + (activity.paragraphsRead || 0),
			timeSpent: (existingActivity?.timeSpent || 0) + (activity.timeSpent || 0),
		};

		const userStats = await this.getUserStatsRepository().findByUserId(userId);
		updatedActivity.goalAchieved = this.checkGoalAchieved(
			updatedActivity,
			userStats.streakGoal
		);

		const dailyActivity = await this.getDailyActivityRepository().upsert(
			userId,
			today,
			updatedActivity
		);

		// Update streak if goal achieved and not already contributed
		if (updatedActivity.goalAchieved && !existingActivity?.streakContributed) {
			await this.updateStreak(userId, today);
			await this.getDailyActivityRepository().update(dailyActivity.id, {
				streakContributed: true,
			});
		}

		return dailyActivity;
	}

	private async updateStreak(userId: string, activityDate: Date): Promise<void> {
		const userStats = await this.getUserStatsRepository().findByUserId(userId);
		const yesterday = new Date(activityDate);
		yesterday.setDate(yesterday.getDate() - 1);

		let newStreak = 1;

		if (userStats.lastActivityDate) {
			const lastActivity = new Date(userStats.lastActivityDate);
			lastActivity.setHours(0, 0, 0, 0);

			if (this.isSameDay(lastActivity, yesterday)) {
				// Consecutive day
				newStreak = userStats.currentStreak + 1;
			} else if (this.isSameDay(lastActivity, activityDate)) {
				// Same day, maintain streak
				newStreak = userStats.currentStreak;
			} else {
				// Streak broken, reset to 1
				newStreak = 1;
			}
		}

		const updatedStats = {
			currentStreak: newStreak,
			longestStreak: Math.max(userStats.longestStreak, newStreak),
			lastActivityDate: activityDate,
			streakStartDate: newStreak === 1 ? activityDate : userStats.streakStartDate,
		};

		await this.getUserStatsRepository().update(userId, updatedStats);

		// Check for milestone achievements
		await this.checkStreakMilestones(userId);
	}

	async useStreakFreeze(userId: string): Promise<UserStats> {
		const userStats = await this.getUserStatsRepository().findByUserId(userId);

		if (userStats.streakFreezeCount <= 0) {
			throw new ValidationError('No streak freezes available');
		}

		const yesterday = new Date();
		yesterday.setDate(yesterday.getDate() - 1);
		yesterday.setHours(0, 0, 0, 0);

		// Create a fake activity for yesterday to maintain streak
		await this.getDailyActivityRepository().upsert(userId, yesterday, {
			userId,
			date: yesterday,
			wordsReviewed: 0,
			paragraphsRead: 0,
			timeSpent: 0,
			goalAchieved: true,
			streakContributed: true,
		});

		const updatedStats = {
			streakFreezeCount: userStats.streakFreezeCount - 1,
			totalStreakFreezes: userStats.totalStreakFreezes + 1,
			lastActivityDate: yesterday,
		};

		return await this.getUserStatsRepository().update(userId, updatedStats);
	}

	async checkStreakMilestones(userId: string): Promise<StreakMilestone[]> {
		const userStats = await this.getUserStatsRepository().findByUserId(userId);
		const existingMilestones = await this.getStreakMilestoneRepository().findByUserId(userId);

		const achievedStreaks = new Set(existingMilestones.map((m) => m.streakDays));
		const milestoneThresholds = [7, 14, 30, 50, 100, 200, 365, 500, 1000];

		const newMilestones: StreakMilestone[] = [];

		for (const threshold of milestoneThresholds) {
			if (userStats.currentStreak >= threshold && !achievedStreaks.has(threshold)) {
				const milestone = await this.getStreakMilestoneRepository().create({
					userId,
					streakDays: threshold,
					rewardType: this.getRewardTypeForStreak(threshold),
					rewardValue: this.getRewardValueForStreak(threshold),
				});
				newMilestones.push(milestone);
			}
		}

		return newMilestones;
	}

	private getRewardTypeForStreak(days: number): StreakRewardType {
		if (days >= 365) return StreakRewardType.PREMIUM_FEATURE;
		if (days >= 100) return StreakRewardType.BADGE;
		if (days >= 30) return StreakRewardType.STREAK_FREEZE;
		if (days >= 7) return StreakRewardType.POINTS;
		return StreakRewardType.BADGE;
	}

	private getRewardValueForStreak(days: number): any {
		switch (this.getRewardTypeForStreak(days)) {
			case StreakRewardType.POINTS:
				return { points: days * 10 };
			case StreakRewardType.STREAK_FREEZE:
				return { freezes: Math.floor(days / 30) };
			case StreakRewardType.BADGE:
				return { badgeId: `streak_${days}_days` };
			case StreakRewardType.PREMIUM_FEATURE:
				return { feature: 'advanced_analytics', duration: 30 };
			default:
				return null;
		}
	}

	private checkGoalAchieved(activity: DailyActivityUpdate, goal: number): boolean {
		return (activity.wordsReviewed || 0) + (activity.paragraphsRead || 0) >= goal;
	}

	private isSameDay(date1: Date, date2: Date): boolean {
		return (
			date1.getFullYear() === date2.getFullYear() &&
			date1.getMonth() === date2.getMonth() &&
			date1.getDate() === date2.getDate()
		);
	}
}
```

#### Repositories

**Daily Activity Repository** (`src/backend/repositories/daily-activity.repository.ts`)

```typescript
export interface DailyActivityRepository extends BaseRepository<DailyActivity> {
	findByUserAndDate(userId: string, date: Date): Promise<DailyActivity | null>;
	findByUserAndDateRange(
		userId: string,
		startDate: Date,
		endDate: Date
	): Promise<DailyActivity[]>;
	upsert(userId: string, date: Date, data: Partial<DailyActivity>): Promise<DailyActivity>;
	getStreakHistory(userId: string, days: number): Promise<DailyActivity[]>;
}
```

### Frontend Implementation

#### Components

**Streak Display Component** (`src/components/ui/streak-display.tsx`)

```typescript
interface StreakDisplayProps {
  currentStreak: number;
  longestStreak: number;
  streakGoal: number;
  todayProgress: number;
  streakFreezes: number;
  size?: 'sm' | 'md' | 'lg';
}

export function StreakDisplay({
  currentStreak,
  longestStreak,
  streakGoal,
  todayProgress,
  streakFreezes,
  size = 'md'
}: StreakDisplayProps) {
  const progressPercentage = Math.min((todayProgress / streakGoal) * 100, 100);
  const isGoalAchieved = todayProgress >= streakGoal;

  return (
    <div className={`bg-gradient-to-r from-orange-400 to-red-500 rounded-lg p-4 text-white ${
      size === 'sm' ? 'p-2' : size === 'lg' ? 'p-6' : 'p-4'
    }`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Flame className={`${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-8 h-8' : 'w-6 h-6'} ${
            isGoalAchieved ? 'text-yellow-300' : 'text-orange-200'
          }`} />
          <span className={`font-bold ${size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-3xl' : 'text-2xl'}`}>
            {currentStreak}
          </span>
          <span className={`${size === 'sm' ? 'text-xs' : 'text-sm'} opacity-90`}>
            day{currentStreak !== 1 ? 's' : ''}
          </span>
        </div>

        {streakFreezes > 0 && (
          <div className="flex items-center space-x-1">
            <Snowflake className="w-4 h-4 text-blue-200" />
            <span className="text-xs">{streakFreezes}</span>
          </div>
        )}
      </div>

      <div className="mb-2">
        <div className="flex justify-between text-xs mb-1">
          <span>Today's Progress</span>
          <span>{todayProgress}/{streakGoal}</span>
        </div>
        <div className="w-full bg-white/20 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              isGoalAchieved ? 'bg-yellow-300' : 'bg-white'
            }`}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      <div className="text-xs opacity-75">
        Best: {longestStreak} day{longestStreak !== 1 ? 's' : ''}
      </div>
    </div>
  );
}
```

**Streak Calendar Component** (`src/components/ui/streak-calendar.tsx`)

```typescript
interface StreakCalendarProps {
  activities: DailyActivity[];
  currentMonth?: Date;
  onMonthChange?: (month: Date) => void;
}

export function StreakCalendar({ activities, currentMonth = new Date(), onMonthChange }: StreakCalendarProps) {
  const activityMap = new Map(
    activities.map(activity => [
      activity.date.toISOString().split('T')[0],
      activity
    ])
  );

  const getDayStatus = (date: Date): 'completed' | 'partial' | 'missed' | 'future' => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (date > today) return 'future';

    const dateStr = date.toISOString().split('T')[0];
    const activity = activityMap.get(dateStr);

    if (!activity) return 'missed';
    if (activity.goalAchieved) return 'completed';
    if (activity.wordsReviewed > 0 || activity.paragraphsRead > 0) return 'partial';
    return 'missed';
  };

  const getDayColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'bg-green-500 text-white';
      case 'partial': return 'bg-yellow-400 text-white';
      case 'missed': return 'bg-red-200 text-gray-600';
      case 'future': return 'bg-gray-100 text-gray-400';
      default: return 'bg-gray-100 text-gray-400';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">
          {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => onMonthChange?.(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <button
            onClick={() => onMonthChange?.(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1 mb-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {/* Calendar days implementation */}
      </div>

      <div className="flex items-center justify-center space-x-4 mt-4 text-xs">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span>Goal achieved</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-yellow-400 rounded"></div>
          <span>Partial progress</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-red-200 rounded"></div>
          <span>Missed</span>
        </div>
      </div>
    </div>
  );
}
```

#### Hooks

**Streak Hook** (`src/hooks/use-streak.ts`)

```typescript
export function useStreak() {
	const [streakInfo, setStreakInfo] = useState<StreakInfo | null>(null);
	const [dailyActivities, setDailyActivities] = useState<DailyActivity[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const updateDailyActivity = useCallback(async (activity: DailyActivityUpdate) => {
		try {
			const updatedActivity = await updateDailyActivityApi(activity);

			// Refresh streak info
			const newStreakInfo = await getCurrentStreakApi();
			setStreakInfo(newStreakInfo);

			// Update daily activities
			setDailyActivities((prev) => {
				const updated = [...prev];
				const index = updated.findIndex(
					(a) => a.date.toDateString() === updatedActivity.date.toDateString()
				);
				if (index >= 0) {
					updated[index] = updatedActivity;
				} else {
					updated.push(updatedActivity);
				}
				return updated.sort((a, b) => b.date.getTime() - a.date.getTime());
			});

			return updatedActivity;
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to update activity'));
			throw err;
		}
	}, []);

	const useStreakFreeze = useCallback(async () => {
		try {
			const updatedStats = await useStreakFreezeApi();
			setStreakInfo((prev) =>
				prev ? { ...prev, streakFreezes: updatedStats.streakFreezeCount } : null
			);
			toast.success('Streak freeze used! Your streak is safe.');
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to use streak freeze');
			setError(error);
			toast.error(error.message);
		}
	}, []);

	return {
		streakInfo,
		dailyActivities,
		loading,
		error,
		updateDailyActivity,
		useStreakFreeze,
		fetchStreakInfo: () => getCurrentStreakApi().then(setStreakInfo),
		fetchDailyActivities: (days?: number) => getStreakHistoryApi(days).then(setDailyActivities),
	};
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic streak tracking service
- Daily activity repository

### Phase 2 (Weeks 3-4): Streak Logic

- Streak calculation and maintenance
- Milestone detection system
- Streak freeze functionality

### Phase 3 (Weeks 5-6): Frontend Components

- Streak display components
- Calendar visualization
- Goal setting interface

### Phase 4 (Weeks 7-8): Integration & Gamification

- Integration with learning activities
- Streak-based rewards
- Social features and leaderboards

## Success Metrics

- Daily active user retention
- Average streak length
- Goal achievement rate
- Streak freeze usage patterns
- User engagement with streak features

## Future Enhancements

- Streak sharing on social media
- Custom streak goals per user
- Streak challenges between friends
- Streak recovery options
- Advanced streak analytics
