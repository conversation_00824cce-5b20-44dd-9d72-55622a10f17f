# LLM Module Documentation - Vocab App

## 📋 Tổng quan

LLM (Large Language Model) module là một thành phần cốt lõi của ứng dụng Vocab, cung cấp các tính năng AI-powered để tạo nội dung học tập, đ<PERSON>h giá và hỗ trợ người dùng trong việc học từ vựng tiếng Anh và tiếng Việt.

## 🏗️ Kiến trúc tổng thể

### Core Components

```
LLM Module
├── Services Layer
│   ├── LLMService (Core AI operations)
│   ├── ModelSelectorService (Smart model selection)
│   ├── PromptOptimizerService (Prompt optimization)
│   ├── SemanticCacheService (Intelligent caching)
│   ├── CacheService (Basic caching)
│   └── TokenMonitorService (Usage tracking)
├── API Layer
│   └── llm.api.ts (Server actions)
├── Context Layer
│   └── LLMContext (React context)
└── Configuration
    └── config.ts (LLM settings)
```

## 🔧 Core Services

### 1. LLMService (`src/backend/services/llm.service.ts`)

**Chức năng chính:**

-   T<PERSON>ch hợp với OpenAI GPT models (gpt-4o-mini, gpt-4o, gpt-4)
-   Tạo từ vựng ngẫu nhiên và chi tiết từ
-   Tạo đoạn văn và câu hỏi
-   Đánh giá bài dịch và câu trả lời
-   Tạo bài tập ngữ pháp

**Các phương thức chính:**

```typescript
class LLMService {
	// Vocabulary generation
	generateRandomTerms(params: GenerateRandomTermsParams): Promise<RandomWord[]>;
	generateWordDetails(
		terms: string[],
		source_language: Language,
		target_language: Language
	): Promise<Word[]>;

	// Content generation
	generateParagraph(params: GenerateParagraphParams): Promise<string[]>;
	generateQuestions(params: GenerateQuestionsParams): Promise<string[]>;
	generateParagraphWithQuestions(
		params: GenerateParagraphWithQuestionsParams
	): Promise<ParagraphWithQuestionsResult>;
	generateGrammarPractice(params: GrammarPracticeParams): Promise<GrammarPracticeResultItem[]>;

	// Evaluation
	evaluateTranslation(params: EvaluateTranslationParams): Promise<TranslationEvaluationResult>;
	evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]>;
}
```

### 2. ModelSelectorService (`src/backend/services/model-selector.service.ts`)

**Chức năng:**

-   Lựa chọn model phù hợp dựa trên operation type
-   Tối ưu hóa cost và performance
-   Adaptive learning từ usage patterns

**Model Configuration:**

```typescript
interface ModelConfig {
	name: string;
	maxTokens: number;
	costPer1kInput: number;
	costPer1kOutput: number;
	capabilities: ModelCapability[];
	performance: ModelPerformance;
	availability: 'high' | 'medium' | 'low';
}
```

**Supported Models:**

-   `gpt-4o-mini`: Cost-effective, fast, good for basic operations
-   `gpt-4o`: High quality, balanced performance
-   `gpt-4`: Premium quality, slower, expensive

### 3. PromptOptimizerService (`src/backend/services/prompt-optimizer.service.ts`)

**Chức năng:**

-   Tối ưu hóa prompts để giảm token usage
-   Template-based prompt management
-   Token estimation và compression

**Optimization Features:**

-   Prompt compression (target: 40% reduction)
-   Template reuse
-   Parameter substitution
-   Token estimation

### 4. SemanticCacheService (`src/backend/services/semantic-cache.service.ts`)

**Chức năng:**

-   Intelligent caching với semantic similarity
-   Keyword-based indexing
-   Cache hit optimization

**Key Features:**

```typescript
class SemanticCacheService extends CacheService {
	getWithSemantic<T>(
		key: string,
		params?: Record<string, any>,
		similarityThreshold?: number
	): Promise<SemanticSearchResult<T> | null>;
	setWithSemantic<T>(
		key: string,
		value: T,
		params: Record<string, any>,
		options?: OptimizedCacheOptions
	): boolean;
	findSimilarEntry<T>(
		key: string,
		params?: Record<string, any>,
		threshold?: number
	): Promise<SemanticSearchResult<T> | null>;
}
```

### 5. TokenMonitorService (`src/backend/services/token-monitor.service.ts`)

**Chức năng:**

-   Theo dõi token usage và cost
-   Budget management và alerts
-   Performance analytics

**Monitoring Features:**

-   Daily/monthly token budgets
-   Cost tracking per operation
-   Usage analytics và reporting
-   Budget alerts (warning/critical)

## 🔌 API Layer

### LLM API (`src/backend/api/llm.api.ts`)

**Server Actions:**

```typescript
// Vocabulary operations
generateRandomWordsApi(params: GenerateRandomWordsParams): Promise<RandomWord[]>
generateWordDetailsApi(terms: string[], source_language: Language, target_language: Language): Promise<Word[]>

// Content generation
generateParagraphApi(params: GenerateParagraphParams): Promise<string[]>
generateQuestionsApi(params: GenerateQuestionsParams): Promise<string[]>
generateParagraphWithQuestionsApi(params: GenerateParagraphWithQuestionsParams): Promise<ParagraphWithQuestionsResult>

// Evaluation
evaluateTranslationApi(params: EvaluateTranslationParams): Promise<TranslationEvaluationResult>
evaluateAnswersApi(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]>

// Grammar practice
generateGrammarPracticeApi(params: GrammarPracticeParams): Promise<GrammarPracticeResultItem[]>
```

**Validation:**

-   Zod schemas cho tất cả input parameters
-   Type-safe validation
-   Error handling và logging

## ⚛️ React Context

### LLMContext (`src/contexts/llm-context.tsx`)

**Chức năng:**

-   Centralized state management cho LLM operations
-   Loading states và error handling
-   Client-side API calls

**Context API:**

```typescript
interface LLMContextType {
	isLoading: boolean;
	error: Error | null;

	// Operations
	generateRandomTerms: (data: GenerateRandomTermsParams) => Promise<RandomWord[]>;
	generateWordDetails: (
		terms: string[],
		source_language: Language,
		target_language: Language
	) => Promise<WordDetail[]>;
	generateParagraphs: (data: GenerateParagraphParams) => Promise<string[]>;
	evaluateTranslation: (params: EvaluateTranslationParams) => Promise<any>;
	generateQuestions: (params: GenerateQuestionsParams) => Promise<string[]>;
	generateParagraphWithQuestions: (
		params: GenerateParagraphWithQuestionsParams
	) => Promise<ParagraphWithQuestionsResult>;
	evaluateAnswers: (params: EvaluateAnswersParams) => Promise<AnswerEvaluationResult[]>;
	generateGrammarPractice: (
		params: GrammarPracticeParams
	) => Promise<GrammarPracticeResultItem[]>;

	// Utilities
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
}
```

## ⚙️ Configuration

### Environment Variables (`.env.example`)

```bash
# Basic LLM Configuration
LLM_OPENAI_API_KEY=
LLM_OPENAI_MODEL=gpt-4o-mini
LLM_MAX_EXAMPLES=8
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000

# Optimization Settings
LLM_OPTIMIZATION_ENABLED=true
LLM_PROMPT_OPTIMIZATION_ENABLED=true
LLM_COMPRESSION_TARGET=0.4

# Caching Configuration
LLM_CACHING_ENABLED=true
LLM_CACHE_TTL_VOCABULARY=604800      # 7 days
LLM_CACHE_TTL_WORD_DETAILS=604800    # 7 days
LLM_CACHE_TTL_PARAGRAPHS=259200      # 3 days
LLM_CACHE_TTL_QUESTIONS=259200       # 3 days
LLM_CACHE_TTL_EVALUATIONS=2592000    # 30 days
LLM_CACHE_TTL_GRAMMAR=86400          # 1 day

# Semantic Cache
LLM_SEMANTIC_CACHE_ENABLED=false
LLM_SEMANTIC_CACHE_THRESHOLD=0.8
LLM_SEMANTIC_MAX_KEYWORDS=20

# Budget Management
LLM_TOKEN_BUDGET_DAILY=100000
LLM_TOKEN_BUDGET_MONTHLY=2500000
LLM_COST_ALERT_DAILY=10.0
LLM_COST_ALERT_MONTHLY=250.0

# Batch Processing
LLM_BATCH_PROCESSING_ENABLED=true
LLM_BATCH_SIZE_WORD_DETAILS=20
LLM_BATCH_SIZE_EVALUATE_ANSWERS=10
LLM_BATCH_MAX_WAIT_TIME=2000
LLM_BATCH_MAX_TOKENS=8000

# Smart Model Selection
LLM_MODEL_SELECTION_ENABLED=true
LLM_COST_OPTIMIZATION_ENABLED=false
LLM_QUALITY_THRESHOLD=0.8
LLM_LATENCY_THRESHOLD=5000
LLM_ADAPTIVE_LEARNING_ENABLED=false

# Monitoring
LLM_MONITORING_ENABLED=false
LLM_LOG_LEVEL=info
LLM_METRICS_COLLECTION=false
```

### LLM Config (`src/config/config.ts`)

```typescript
export async function getLLMConfig() {
	return {
		openAIKey: env.LLM_OPENAI_API_KEY || '',
		openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
		maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
		temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
		maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
	};
}

export async function getLLMOptimizationConfig() {
	return {
		enabled: env.LLM_OPTIMIZATION_ENABLED === 'true',
		promptOptimization: {
			enabled: env.LLM_PROMPT_OPTIMIZATION_ENABLED !== 'false',
			compressionTarget: Number.parseFloat(env.LLM_COMPRESSION_TARGET || '0.4'),
		},
		caching: {
			enabled: env.LLM_CACHING_ENABLED !== 'false',
			ttl: {
				vocabulary: Number.parseInt(env.LLM_CACHE_TTL_VOCABULARY || '604800'),
				wordDetails: Number.parseInt(env.LLM_CACHE_TTL_WORD_DETAILS || '604800'),
				paragraphs: Number.parseInt(env.LLM_CACHE_TTL_PARAGRAPHS || '259200'),
				questions: Number.parseInt(env.LLM_CACHE_TTL_QUESTIONS || '259200'),
				evaluations: Number.parseInt(env.LLM_CACHE_TTL_EVALUATIONS || '2592000'),
				grammarPractice: Number.parseInt(env.LLM_CACHE_TTL_GRAMMAR || '86400'),
			},
			semanticSimilarity: {
				enabled: env.LLM_SEMANTIC_CACHE_ENABLED === 'true',
				threshold: Number.parseFloat(env.LLM_SEMANTIC_CACHE_THRESHOLD || '0.8'),
				maxKeywords: Number.parseInt(env.LLM_SEMANTIC_MAX_KEYWORDS || '20'),
			},
		},
		modelSelection: {
			enabled: env.LLM_MODEL_SELECTION_ENABLED !== 'false',
			costOptimization: env.LLM_COST_OPTIMIZATION_ENABLED === 'true',
			qualityThreshold: Number.parseFloat(env.LLM_QUALITY_THRESHOLD || '0.8'),
			latencyThreshold: Number.parseInt(env.LLM_LATENCY_THRESHOLD || '5000'),
			adaptiveLearning: env.LLM_ADAPTIVE_LEARNING_ENABLED === 'true',
		},
	};
}
```

## 📊 Data Types & Schemas

### Core Types

```typescript
// Language support
enum Language {
	EN = 'EN', // English
	VI = 'VI', // Vietnamese
}

// Difficulty levels
enum Difficulty {
	BEGINNER = 'BEGINNER',
	INTERMEDIATE = 'INTERMEDIATE',
	ADVANCED = 'ADVANCED',
}

// Parts of speech
enum PartsOfSpeech {
	NOUN = 'NOUN',
	VERB = 'VERB',
	ADJECTIVE = 'ADJECTIVE',
	ADVERB = 'ADVERB',
	PRONOUN = 'PRONOUN',
	PREPOSITION = 'PREPOSITION',
	CONJUNCTION = 'CONJUNCTION',
	INTERJECTION = 'INTERJECTION',
}
```

### Input Parameters

```typescript
// Generate random terms
interface GenerateRandomTermsParams {
	keywords: string[];
	max_terms: number;
	exclude_collection_ids: string[];
	source_language: Language;
	target_language: Language;
}

// Generate paragraphs
interface GenerateParagraphParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number;
	sentenceCount?: number;
}

// Generate questions
interface GenerateQuestionsParams {
	paragraph: string;
	language: Language;
	questionCount: number;
}

// Evaluate translation
interface EvaluateTranslationParams {
	original_text: string;
	translated_text: string;
	source_language: Language;
	target_language: Language;
	feedback_language: Language;
}

// Evaluate answers
interface EvaluateAnswersParams {
	paragraph: string;
	questions: string[];
	answers: string[];
	qna_language: Language;
	feedback_native_language: Language;
}

// Grammar practice
interface GrammarPracticeParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number;
	errorTypes: string[];
}
```

### Response Types

```typescript
// Random word generation
interface RandomWord {
	term: string;
	definition: string;
	example: string;
	parts_of_speech: PartsOfSpeech;
	source_language: Language;
	target_language: Language;
}

// Word details
interface WordDetail {
	term: string;
	definition: string;
	example: string;
	parts_of_speech: PartsOfSpeech;
	pronunciation?: string;
	source_language: Language;
	target_language: Language;
}

// Translation evaluation
interface TranslationEvaluationResult {
	accuracy_score: number;
	fluency_score: number;
	overall_score: number;
	feedback: string;
	suggested_improvements: string[];
}

// Answer evaluation
interface AnswerEvaluationResult {
	question: string;
	answer: string;
	feedback: {
		qna_feedback_text: string;
		native_feedback_text: string;
	};
	score: number | null;
	is_correct: boolean | null;
	suggested_answer: string | null;
}

// Paragraph with questions
interface ParagraphWithQuestionsResult {
	paragraph: string;
	questions: string[];
}

// Grammar practice
interface GrammarPracticeResultItem {
	paragraphWithErrors: string;
	correctedParagraph: string;
	allErrors: Array<{
		errorText: string;
		correctedText: string;
		errorType: string;
		explanation: {
			source_language: string;
			target_language: string;
		};
	}>;
}
```

### Zod Validation Schemas

```typescript
// Random word schema
const RandomWordSchema = z.object({
	term: z.string(),
	definition: z.string(),
	example: z.string(),
	parts_of_speech: z.nativeEnum(PartsOfSpeech),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

// Translation evaluation schema
const TranslationEvaluationSchema = z.object({
	accuracy_score: z.number().min(1).max(10),
	fluency_score: z.number().min(1).max(10),
	overall_score: z.number().min(1).max(10),
	feedback: z.string(),
	suggested_improvements: z.array(z.string()),
});

// Answer evaluation schema
const AnswerEvaluationSchema = z.object({
	question: z.string(),
	answer: z.string(),
	feedback: z.object({
		qna_feedback_text: z.string(),
		native_feedback_text: z.string(),
	}),
	score: z.number().min(1).max(5).nullable(),
	is_correct: z.boolean().nullable(),
	suggested_answer: z.string().nullable(),
});

// Grammar practice schema
const GrammarPracticeResponseSchema = z.object({
	paragraphs: z.array(
		z.object({
			paragraphWithErrors: z.string(),
			correctedParagraph: z.string(),
			allErrors: z.array(
				z.object({
					errorText: z.string(),
					correctedText: z.string(),
					errorType: z.string(),
					explanation: z.object({
						source_language: z.string(),
						target_language: z.string(),
					}),
				})
			),
		})
	),
});
```

## 🚀 Usage Examples

### 1. Generating Random Vocabulary

```typescript
import { useLLM } from '@/contexts/llm-context';

function VocabularyGenerator() {
	const { generateRandomTerms, isLoading } = useLLM();

	const handleGenerate = async () => {
		try {
			const words = await generateRandomTerms({
				keywords: ['technology', 'business'],
				max_terms: 10,
				exclude_collection_ids: [],
				source_language: Language.EN,
				target_language: Language.VI,
			});
			console.log('Generated words:', words);
		} catch (error) {
			console.error('Error generating words:', error);
		}
	};

	return (
		<button onClick={handleGenerate} disabled={isLoading}>
			{isLoading ? 'Generating...' : 'Generate Words'}
		</button>
	);
}
```

### 2. Creating Paragraph with Questions

```typescript
import { useLLM } from '@/contexts/llm-context';

function ParagraphQuestionGenerator() {
	const { generateParagraphWithQuestions } = useLLM();

	const handleGenerate = async () => {
		try {
			const result = await generateParagraphWithQuestions({
				keywords: ['environment', 'climate change'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				sentenceCount: 5,
				questionCount: 3,
			});

			console.log('Paragraph:', result.paragraph);
			console.log('Questions:', result.questions);
		} catch (error) {
			console.error('Error generating content:', error);
		}
	};

	return <button onClick={handleGenerate}>Generate Paragraph & Questions</button>;
}
```

### 3. Evaluating User Answers

```typescript
import { useLLM } from '@/contexts/llm-context';

function AnswerEvaluator() {
	const { evaluateAnswers } = useLLM();

	const handleEvaluate = async () => {
		try {
			const evaluations = await evaluateAnswers({
				paragraph: 'Climate change is a global challenge...',
				questions: ['What is climate change?', 'Why is it a global challenge?'],
				answers: [
					'Climate change refers to long-term changes in temperature and weather patterns.',
					'It affects all countries and requires international cooperation.',
				],
				qna_language: Language.EN,
				feedback_native_language: Language.VI,
			});

			evaluations.forEach((eval, index) => {
				console.log(`Question ${index + 1}:`, eval.question);
				console.log('Score:', eval.score);
				console.log('Feedback:', eval.feedback.native_feedback_text);
			});
		} catch (error) {
			console.error('Error evaluating answers:', error);
		}
	};

	return <button onClick={handleEvaluate}>Evaluate Answers</button>;
}
```

## 🔄 Caching Strategy

### Multi-tier Caching System

1. **Basic Cache (CacheService)**

    - In-memory caching với NodeCache
    - TTL-based expiration
    - Tag-based invalidation
    - Hit/miss statistics

2. **Semantic Cache (SemanticCacheService)**
    - Intelligent similarity matching
    - Keyword-based indexing
    - Semantic search capabilities
    - Access pattern optimization

### Cache Configuration

```typescript
// Cache TTL settings (seconds)
const CACHE_TTL = {
	vocabulary: 604800, // 7 days
	wordDetails: 604800, // 7 days
	paragraphs: 259200, // 3 days
	questions: 259200, // 3 days
	evaluations: 2592000, // 30 days
	grammarPractice: 86400, // 1 day
};

// Semantic cache settings
const SEMANTIC_CONFIG = {
	enabled: false, // Enable semantic matching
	threshold: 0.8, // Similarity threshold
	maxKeywords: 20, // Max keywords to extract
	keywordWeight: 0.4, // Keyword similarity weight
	structuralWeight: 0.3, // Structural similarity weight
	semanticWeight: 0.3, // Semantic similarity weight
};
```

### Cache Key Generation

```typescript
// LLM cache key format
function generateLLMKey(operation: string, params: Record<string, any>): string {
	const sortedParams = Object.keys(params)
		.sort()
		.reduce((result, key) => {
			let value = params[key];
			if (Array.isArray(value)) {
				value = value.sort().join(',');
			} else if (typeof value === 'object' && value !== null) {
				value = JSON.stringify(value);
			}
			result[key] = value;
			return result;
		}, {});

	return `llm:${operation}:${JSON.stringify(sortedParams)}`;
}
```

## 📈 Performance Optimization

### 1. Prompt Optimization

**Template-based Optimization:**

```typescript
const TEMPLATES = {
	vocabulary: {
		original: `Generate {count} {target_lang} vocabulary terms for {source_lang} native speakers...`,
		optimized: `Gen {count} {target_lang} vocab for {source_lang} speakers...`,
		tokenSavings: 35, // 35% reduction
	},
	evaluation: {
		original: `Evaluate the following translation from {source_lang} to {target_lang}...`,
		optimized: `Eval {source_lang}→{target_lang} translation...`,
		tokenSavings: 42, // 42% reduction
	},
};
```

**Compression Techniques:**

-   Remove redundant phrases ("please", "and", "or")
-   Shorten common terms ("paragraph" → "para", "question" → "q")
-   Use abbreviations and symbols
-   Eliminate extra whitespace

### 2. Model Selection Strategy

**Operation-based Model Selection:**

```typescript
const MODEL_SELECTION_RULES = {
	generateRandomTerms: 'gpt-4o-mini', // Fast, cost-effective
	generateWordDetails: 'gpt-4o-mini', // Good quality, affordable
	generateParagraphs: 'gpt-4o', // High quality needed
	evaluateTranslation: 'gpt-4o', // Accuracy critical
	evaluateAnswers: 'gpt-4o', // Detailed feedback needed
	generateQuestions: 'gpt-4o-mini', // Simple generation
	generateGrammarPractice: 'gpt-4o', // Complex error generation
};
```

**Dynamic Model Selection Factors:**

-   Operation complexity
-   Quality requirements
-   Cost constraints
-   Latency requirements
-   Current model availability

### 3. Batch Processing

**Batch Configuration:**

```typescript
const BATCH_SIZES = {
	wordDetails: 20, // Process 20 words at once
	evaluateAnswers: 10, // Evaluate 10 answers together
	evaluateTranslation: 15, // Batch translation evaluations
	questions: 5, // Generate questions in batches
	paragraphs: 3, // Create multiple paragraphs
	grammar: 2, // Grammar practice batches
};

const BATCH_LIMITS = {
	maxWaitTime: 2000, // 2 seconds max wait
	maxTokens: 8000, // 8K token limit per batch
	maxRetries: 3, // Retry failed batches
};
```

## 📊 Monitoring & Analytics

### Token Usage Tracking

```typescript
interface TokenUsageRecord {
	id: string;
	operation: string;
	model: string;
	inputTokens: number;
	outputTokens: number;
	totalTokens: number;
	estimatedCost: number;
	latency: number;
	timestamp: Date;
	userId?: string;
	success: boolean;
}
```

### Budget Management

```typescript
interface BudgetConfig {
	daily: {
		tokenLimit: 100000; // 100K tokens per day
		costLimit: 10.0; // $10 per day
	};
	monthly: {
		tokenLimit: 2500000; // 2.5M tokens per month
		costLimit: 250.0; // $250 per month
	};
	alerts: {
		warningThreshold: 0.8; // 80% of limit
		criticalThreshold: 1.0; // 100% of limit
	};
}
```

### Performance Metrics

```typescript
interface PerformanceMetrics {
	// Response times
	averageLatency: number;
	p95Latency: number;
	p99Latency: number;

	// Success rates
	successRate: number;
	errorRate: number;
	retryRate: number;

	// Cache performance
	cacheHitRate: number;
	semanticCacheHitRate: number;

	// Cost efficiency
	costPerOperation: number;
	tokensPerOperation: number;

	// Quality metrics
	userSatisfactionScore: number;
	contentQualityScore: number;
}
```

## 🛠️ Development & Testing

### Local Development Setup

```bash
# 1. Install dependencies
yarn install

# 2. Set up environment variables
cp .env.example .env.local

# 3. Configure OpenAI API key
LLM_OPENAI_API_KEY=your_openai_api_key_here

# 4. Enable development features
LLM_OPTIMIZATION_ENABLED=true
LLM_CACHING_ENABLED=true
LLM_MONITORING_ENABLED=true

# 5. Start development server
yarn dev
```

### Testing LLM Operations

```typescript
// Test vocabulary generation
import { generateRandomWordsApi } from '@/backend/api/llm.api';

describe('LLM Vocabulary Generation', () => {
	it('should generate random words', async () => {
		const result = await generateRandomWordsApi({
			keywordTerms: ['technology'],
			maxTerms: 5,
			excludeCollectionIds: [],
			source_language: Language.EN,
			target_language: Language.VI,
		});

		expect(result).toHaveLength(5);
		expect(result[0]).toHaveProperty('term');
		expect(result[0]).toHaveProperty('definition');
	});
});

// Test caching behavior
import { semanticCache } from '@/backend/services/semantic-cache.service';

describe('Semantic Cache', () => {
	it('should cache and retrieve LLM responses', async () => {
		const key = 'test-key';
		const value = { result: 'test data' };
		const params = { operation: 'test', keywords: ['test'] };

		// Set cache
		semanticCache.setWithSemantic(key, value, params);

		// Get from cache
		const cached = await semanticCache.getWithSemantic(key, params);
		expect(cached?.entry.value).toEqual(value);
	});
});
```

### Development Cache Files

Trong development mode, LLM responses được cache vào files để tăng tốc development:

```
.cache/llm/
├── random-words/
├── word-details/
├── paragraphs/
├── questions/
├── evaluations/
└── grammar-practice/
```

## 🔒 Security & Best Practices

### API Key Management

```typescript
// Secure API key handling
const llmConfig = await getLLMConfig();
if (!llmConfig.openAIKey) {
	throw new Error('OpenAI API key not configured');
}

// Initialize OpenAI client with proper error handling
this.openai = new OpenAI({
	apiKey: llmConfig.openAIKey,
	timeout: 30000, // 30 second timeout
	maxRetries: 3, // Retry failed requests
});
```

### Input Validation

```typescript
// Comprehensive input validation with Zod
const generateRandomWordsSchema = z.object({
	keywordTerms: z.array(z.string().min(1)).min(1).max(10),
	maxTerms: z.number().int().min(1).max(50),
	excludeCollectionIds: z.array(z.string()).optional().default([]),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

// Validate before processing
const validatedParams = generateRandomWordsSchema.parse(params);
```

### Rate Limiting & Error Handling

```typescript
// Implement retry logic with exponential backoff
const maxRetries = 3;
let retryCount = 0;

while (retryCount < maxRetries) {
	try {
		const result = await openai.chat.completions.create(params);
		return result;
	} catch (error) {
		retryCount++;
		if (retryCount >= maxRetries) {
			throw new Error(`LLM operation failed after ${maxRetries} retries: ${error.message}`);
		}

		// Exponential backoff
		const delay = Math.pow(2, retryCount) * 1000;
		await new Promise((resolve) => setTimeout(resolve, delay));
	}
}
```

### Content Safety

```typescript
// Content filtering and safety checks
function validateGeneratedContent(content: string): boolean {
	// Check for inappropriate content
	const inappropriatePatterns = [
		/\b(violence|hate|discrimination)\b/i,
		// Add more patterns as needed
	];

	return !inappropriatePatterns.some((pattern) => pattern.test(content));
}

// Apply safety checks to all generated content
if (!validateGeneratedContent(generatedText)) {
	throw new Error('Generated content failed safety checks');
}
```
