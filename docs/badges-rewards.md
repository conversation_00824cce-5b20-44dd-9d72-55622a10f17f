# Badges & Rewards System Development Plan

## Overview

Implement a comprehensive gamification system with badges, achievements, and rewards to motivate users and track their learning progress in the vocabulary learning application.

## Technical Architecture

### Database Schema Extensions

#### New Models

```prisma
model Badge {
  id          String   @id @default(uuid())
  name        String
  description String
  icon        String   // Icon identifier or URL
  category    BadgeCategory
  criteria    Json     // Flexible criteria definition
  points      Int      @default(0)
  rarity      BadgeRarity @default(COMMON)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userBadges  UserBadge[]

  @@map("badges")
}

model UserBadge {
  id        String   @id @default(uuid())
  userId    String
  badgeId   String
  earnedAt  DateTime @default(now())
  progress  Json?    // Progress towards badge completion

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  badge     Badge    @relation(fields: [badgeId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeId])
  @@index([userId])
  @@map("user_badges")
}

model UserStats {
  id                    String   @id @default(uuid())
  userId                String   @unique
  totalWordsLearned     Int      @default(0)
  totalParagraphsRead   Int      @default(0)
  currentStreak         Int      @default(0)
  longestStreak         Int      @default(0)
  totalPoints           Int      @default(0)
  level                 Int      @default(1)
  experiencePoints      Int      @default(0)
  lastActivityDate      DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_stats")
}

enum BadgeCategory {
  LEARNING
  STREAK
  MILESTONE
  SOCIAL
  SPECIAL
}

enum BadgeRarity {
  COMMON
  UNCOMMON
  RARE
  EPIC
  LEGENDARY
}
```

#### User Model Extensions

```prisma
model User {
  // ... existing fields
  userBadges  UserBadge[]
  userStats   UserStats?
}
```

### Backend Implementation

#### Services

**Badge Service** (`src/backend/services/badge.service.ts`)

```typescript
export interface BadgeService {
	getAllBadges(): Promise<Badge[]>;
	getUserBadges(userId: string): Promise<UserBadge[]>;
	checkAndAwardBadges(userId: string, activity: ActivityType): Promise<UserBadge[]>;
	getBadgeProgress(userId: string, badgeId: string): Promise<BadgeProgress>;
	createBadge(badgeData: CreateBadgeDto): Promise<Badge>;
	updateBadge(badgeId: string, updateData: UpdateBadgeDto): Promise<Badge>;
}

export class BadgeServiceImpl implements BadgeService {
	constructor(
		private getBadgeRepository: () => BadgeRepository,
		private getUserStatsService: () => UserStatsService
	) {}

	async checkAndAwardBadges(userId: string, activity: ActivityType): Promise<UserBadge[]> {
		const userStats = await this.getUserStatsService().getUserStats(userId);
		const availableBadges = await this.getBadgeRepository().getEligibleBadges(userId);

		const newBadges: UserBadge[] = [];

		for (const badge of availableBadges) {
			if (await this.evaluateBadgeCriteria(badge, userStats, activity)) {
				const userBadge = await this.getBadgeRepository().awardBadge(userId, badge.id);
				newBadges.push(userBadge);
			}
		}

		return newBadges;
	}

	private async evaluateBadgeCriteria(
		badge: Badge,
		userStats: UserStats,
		activity: ActivityType
	): Promise<boolean> {
		const criteria = badge.criteria as BadgeCriteria;

		switch (badge.category) {
			case BadgeCategory.LEARNING:
				return this.evaluateLearningCriteria(criteria, userStats);
			case BadgeCategory.STREAK:
				return this.evaluateStreakCriteria(criteria, userStats);
			case BadgeCategory.MILESTONE:
				return this.evaluateMilestoneCriteria(criteria, userStats);
			default:
				return false;
		}
	}
}
```

**User Stats Service** (`src/backend/services/user-stats.service.ts`)

```typescript
export interface UserStatsService {
	getUserStats(userId: string): Promise<UserStats>;
	updateStats(userId: string, activity: StatsUpdate): Promise<UserStats>;
	calculateLevel(experiencePoints: number): number;
	getLeaderboard(limit?: number): Promise<UserStats[]>;
}

export class UserStatsServiceImpl implements UserStatsService {
	async updateStats(userId: string, activity: StatsUpdate): Promise<UserStats> {
		const stats = await this.getUserStatsRepository().findByUserId(userId);

		const updatedStats = {
			...stats,
			totalWordsLearned: stats.totalWordsLearned + (activity.wordsLearned || 0),
			totalParagraphsRead: stats.totalParagraphsRead + (activity.paragraphsRead || 0),
			totalPoints: stats.totalPoints + (activity.points || 0),
			experiencePoints: stats.experiencePoints + (activity.experience || 0),
			lastActivityDate: new Date(),
		};

		updatedStats.level = this.calculateLevel(updatedStats.experiencePoints);
		updatedStats.currentStreak = this.calculateCurrentStreak(stats, activity);
		updatedStats.longestStreak = Math.max(
			updatedStats.longestStreak,
			updatedStats.currentStreak
		);

		return await this.getUserStatsRepository().update(userId, updatedStats);
	}

	calculateLevel(experiencePoints: number): number {
		// Level formula: level = floor(sqrt(experience / 100)) + 1
		return Math.floor(Math.sqrt(experiencePoints / 100)) + 1;
	}
}
```

#### Repositories

**Badge Repository** (`src/backend/repositories/badge.repository.ts`)

```typescript
export interface BadgeRepository extends BaseRepository<Badge> {
	getEligibleBadges(userId: string): Promise<Badge[]>;
	awardBadge(userId: string, badgeId: string): Promise<UserBadge>;
	getUserBadges(userId: string): Promise<UserBadge[]>;
	getBadgeProgress(userId: string, badgeId: string): Promise<BadgeProgress | null>;
}
```

#### API Endpoints

**Badge API** (`src/backend/api/badge.ts`)

```typescript
export async function getUserBadgesApi(userId?: string) {
	const session = await auth();
	const currentUserId = userId || session?.user?.id;
	if (!currentUserId) throw new UnauthorizedError('Unauthorized');

	const badgeService = getBadgeService();
	return await badgeService.getUserBadges(currentUserId);
}

export async function getBadgeProgressApi(badgeId: string) {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	const badgeService = getBadgeService();
	return await badgeService.getBadgeProgress(userId, badgeId);
}
```

### Frontend Implementation

#### Components

**Badge Display Component** (`src/components/ui/badge-display.tsx`)

```typescript
interface BadgeDisplayProps {
  badge: Badge;
  earned?: boolean;
  progress?: number;
  size?: 'sm' | 'md' | 'lg';
  showProgress?: boolean;
}

export function BadgeDisplay({ badge, earned, progress, size = 'md', showProgress }: BadgeDisplayProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className="relative group">
      <div className={`${sizeClasses[size]} rounded-full border-2 ${
        earned ? 'border-yellow-400 bg-yellow-100' : 'border-gray-300 bg-gray-100'
      } flex items-center justify-center ${!earned ? 'grayscale' : ''}`}>
        <Icon name={badge.icon} className={`${size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      {showProgress && progress !== undefined && (
        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">{Math.round(progress * 100)}%</span>
        </div>
      )}

      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
        {badge.name}
      </div>
    </div>
  );
}
```

**Badge Collection Component** (`src/components/ui/badge-collection.tsx`)

```typescript
interface BadgeCollectionProps {
  userBadges: UserBadge[];
  allBadges: Badge[];
  showProgress?: boolean;
}

export function BadgeCollection({ userBadges, allBadges, showProgress }: BadgeCollectionProps) {
  const earnedBadgeIds = new Set(userBadges.map(ub => ub.badgeId));

  return (
    <div className="grid grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-4">
      {allBadges.map(badge => (
        <BadgeDisplay
          key={badge.id}
          badge={badge}
          earned={earnedBadgeIds.has(badge.id)}
          showProgress={showProgress}
        />
      ))}
    </div>
  );
}
```

#### Hooks

**Badge Hook** (`src/hooks/use-badges.ts`)

```typescript
export function useBadges() {
	const [badges, setBadges] = useState<Badge[]>([]);
	const [userBadges, setUserBadges] = useState<UserBadge[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const fetchUserBadges = useCallback(async () => {
		setLoading(true);
		setError(null);
		try {
			const [allBadges, earnedBadges] = await Promise.all([
				getAllBadgesApi(),
				getUserBadgesApi(),
			]);
			setBadges(allBadges);
			setUserBadges(earnedBadges);
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to fetch badges'));
		} finally {
			setLoading(false);
		}
	}, []);

	const checkForNewBadges = useCallback(async (activity: ActivityType) => {
		try {
			const newBadges = await checkBadgesApi(activity);
			if (newBadges.length > 0) {
				setUserBadges((prev) => [...prev, ...newBadges]);
				// Show notification for new badges
				newBadges.forEach((badge) => {
					toast.success(`🏆 New badge earned: ${badge.badge.name}!`);
				});
			}
		} catch (err) {
			console.error('Failed to check for new badges:', err);
		}
	}, []);

	return {
		badges,
		userBadges,
		loading,
		error,
		fetchUserBadges,
		checkForNewBadges,
	};
}
```

## Badge Categories and Examples

### Learning Badges

- **First Steps**: Complete first word review
- **Word Master**: Learn 100 words
- **Vocabulary Expert**: Learn 1000 words
- **Paragraph Pioneer**: Complete first paragraph
- **Reading Champion**: Complete 50 paragraphs

### Streak Badges

- **Consistent Learner**: 7-day streak
- **Dedicated Student**: 30-day streak
- **Learning Legend**: 100-day streak
- **Unstoppable**: 365-day streak

### Milestone Badges

- **Level Up**: Reach level 5, 10, 25, 50
- **Point Collector**: Earn 1K, 10K, 100K points
- **Time Traveler**: Use app for 30, 90, 365 days

### Social Badges

- **Helpful**: Contribute user-generated content
- **Community Member**: Join study groups
- **Challenger**: Complete peer challenges

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic badge and user stats services
- Repository layer implementation

### Phase 2 (Weeks 3-4): Badge System Logic

- Badge criteria evaluation system
- Automatic badge awarding
- Progress tracking implementation

### Phase 3 (Weeks 5-6): Frontend Components

- Badge display components
- User profile badge section
- Badge collection page

### Phase 4 (Weeks 7-8): Integration & Polish

- Integration with existing learning activities
- Notification system for new badges
- Admin panel for badge management

## Success Metrics

- User engagement increase (daily active users)
- Learning session duration improvement
- User retention rate enhancement
- Badge collection completion rates
- User feedback on motivation levels

## Future Enhancements

- Seasonal/limited-time badges
- Badge trading system
- Custom badge creation for premium users
- Badge-based leaderboards
- Social sharing of badge achievements
