# VOCAB PROJECT - ARCHITECTURE OVERVIEW

## 📋 PROJECT OVERVIEW

**Vocab** là một ứng dụng học từ vựng AI-powered được xây dựng với Next.js 15, tập trung vào việc mở rộng từ vựng thông qua spaced repetition, nội dung được tạo bởi AI và các bộ sưu tập học tập có tổ chức.

### Core Features

-   **Collection Management**: Tổ chức từ vựng theo chủ đề
-   **AI-Powered Content**: Tích hợp LLM để tạo từ vựng, đoạn văn, câu hỏi
-   **Spaced Repetition**: Hệ thống ôn tập thông minh
-   **Multi-language Support**: Hỗ trợ tiếng Anh và tiếng Việt
-   **Practice Modes**: Đa dạng hình thức luyện tập

## 🏗️ TECHNOLOGY STACK

### Frontend

-   **Framework**: Next.js 15 với App Router
-   **Language**: TypeScript (strict mode)
-   **Styling**: Tailwind CSS v4 với custom animations
-   **UI Components**: Radix UI primitives với design system tùy chỉnh
-   **State Management**: React Context API + Custom hooks
-   **Internationalization**: i18next với next-i18next
-   **Animations**: Framer Motion
-   **Icons**: Lucide React
-   **Package Manager**: Yarn v4.9.2 (KHÔNG sử dụng npm)

### Backend

-   **Runtime**: Node.js với Next.js API routes
-   **Database**: PostgreSQL với Prisma ORM (đang migrate sang MongoDB)
-   **Authentication**: JWT với multiple providers (Telegram, Google, Username/Password)
-   **AI Integration**: OpenAI GPT-4o-mini
-   **Caching**: Node-cache với file-based persistence
-   **Security**: Security headers, CSRF protection, rate limiting

### Infrastructure

-   **Deployment**: Vercel với automatic CI/CD
-   **Development**: Docker Compose cho local database
-   **Environment**: Environment-based configuration
-   **PWA**: Service worker và manifest cho offline support

## 🏛️ ARCHITECTURE PATTERNS

### Clean Architecture Implementation

```
src/
├── app/                     # Next.js App Router pages & layouts
├── backend/                 # Server-side business logic
│   ├── api/                # API endpoint handlers (8 files)
│   ├── services/           # Business logic layer (10 services)
│   ├── repositories/       # Data access layer (7 repositories)
│   ├── middleware/         # Server middleware
│   └── wire.ts            # Dependency injection container
├── components/             # Reusable UI components
├── contexts/              # React Context providers
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and shared logic
├── types/                 # TypeScript type definitions
└── config/               # Configuration management
```

### Key Design Principles

1. **Separation of Concerns**: Tách biệt rõ ràng giữa API, Service và Repository layers
2. **Dependency Injection**: Centralized service wiring trong `wire.ts`
3. **Type Safety**: Sử dụng TypeScript toàn diện với Prisma-generated types
4. **Security First**: Multiple security layers với validation, rate limiting, CSRF protection
5. **Performance**: Caching strategies, optimized LLM usage, skeleton loading states

## 🔧 BACKEND ARCHITECTURE

### Layer Organization

#### 1. API Layer (`src/backend/api`)

-   **Responsibility**: Request handling, validation, authentication
-   **Pattern**: Thin controllers, delegate to services
-   **Security**: Input validation, rate limiting, auth middleware
-   **Files**: `auth.api.ts`, `collection.api.ts`, `word.api.ts`, `llm.api.ts`, etc.
-   **Conventions**:
    -   Tất cả API files phải có `'use server';` ở đầu
    -   Sử dụng Zod schemas cho validation
    -   Delegate business logic cho services thông qua dependency injection
    -   KHÔNG gọi repositories trực tiếp

#### 2. Service Layer (`src/backend/services`)

-   **Responsibility**: Business logic, orchestration
-   **Pattern**: Service interfaces với implementation classes
-   **Key Services**:
    -   `AuthService`: Authentication và user session management
    -   `CollectionService`: Collection CRUD và word/term management
    -   `WordService`: Word search, retrieval, vocabulary operations
    -   `LLMService`: Large Language Model integration cho AI features
    -   `CacheService`: Caching operations và cache management
    -   `UserService`: User profile và settings management
-   **Pattern Example**:

```typescript
export interface ServiceName {
	methodName(params): Promise<ReturnType>;
}

export class ServiceNameImpl implements ServiceName {
	constructor(private repository: Repository) {}

	async methodName(params): Promise<ReturnType> {
		// Business logic implementation
	}
}
```

#### 3. Repository Layer (`src/backend/repositories`)

-   **Responsibility**: Data access, Prisma operations
-   **Pattern**: Repository pattern với base repository
-   **Features**: Support cả PostgreSQL và MongoDB
-   **Base Repository**: Tất cả repositories extend `BaseRepositoryImpl<T>`
-   **Methods**: `findById`, `findOne`, `find`, `create`, `update`, `delete`
-   **Dual Database**: Có implementation riêng cho PostgreSQL và MongoDB

#### 4. Dependency Injection (`src/backend/wire.ts`)

-   **Pattern**: Factory functions với lazy initialization
-   **Benefits**: Loose coupling, testability, service lifecycle management
-   **Usage**: `getCollectionService()`, `getUserRepository()`, etc.
-   **Singleton Pattern**: Tất cả services và repositories là singletons
-   **Database Switching**: Tự động chọn repository implementation dựa trên feature flags

### Database Strategy

-   **Primary**: PostgreSQL với Prisma ORM
-   **Migration**: Đang chuyển sang MongoDB
-   **Dual Support**: Abstraction layer hỗ trợ cả hai databases
-   **Feature Flags**: `FEATURE_MONGODB_ENABLED`, `FEATURE_DUAL_DATABASE`
-   **Database Manager**: Centralized database connection management
-   **Health Checks**: Built-in health check cho cả hai databases

## 🎨 FRONTEND ARCHITECTURE

### Component Organization

#### 1. App Router Structure (`src/app`)

-   **Layout**: Root layout với provider hierarchy
-   **Pages**: Client/server component separation
-   **Routing**: File-based routing với dynamic routes
-   **Structure**:
    ```
    src/app/
    ├── layout.tsx              # Root layout với PWA support
    ├── page.tsx                # Home page với animations
    ├── globals.css             # Global styles
    ├── components/             # App-specific components
    └── collections/            # Collections feature pages
        ├── [id]/              # Dynamic collection routes
        │   ├── page.tsx       # Collection detail page
        │   ├── paragraph/     # Paragraph practice
        │   └── components/    # Collection-specific components
        └── page.tsx           # Collections listing page
    ```

#### 2. Component Layer (`src/components`)

-   **UI Components**: Reusable primitives trong `src/components/ui`
    -   50+ UI components: Button, Card, Dialog, Form, Input, etc.
    -   Accessibility-first design với ARIA support
    -   Consistent styling với Tailwind CSS
    -   Export pattern: Tất cả components export qua `index.ts`
-   **Feature Components**: Domain-specific components
    -   Home components: HeroSection, CollectionsSection, etc.
    -   Auth components: AuthGuard, LoginForm, etc.
    -   Floating UI: FloatingUIManager, SimpleFloatingProvider
-   **Component Patterns**:

    ```typescript
    // Component structure pattern
    interface ComponentProps {
    	// Props definition với TypeScript
    }

    export function Component({ prop1, prop2 }: ComponentProps) {
    	// Component logic
    	return <div>...</div>;
    }
    ```

#### 3. State Management

-   **Global State**: React Context providers
    -   AuthContext: User authentication state
    -   TranslationContext: Language và translation state
    -   LoadingContext: Loading states với scoped loading
    -   ToastContext: Toast notifications
    -   ThemeContext: Dark/light theme
-   **Local State**: Custom hooks
    -   `useCollections`: Collection management
    -   `useOffline`: Offline detection
    -   `useGracefulDegradation`: Feature degradation
-   **Server State**: SWR pattern với API integration
    -   Caching strategies
    -   Optimistic updates
    -   Error handling

#### 4. Context Providers Hierarchy

```typescript
// Provider hierarchy trong layout.tsx - ORDER MATTERS!
<ErrorManagementProvider>
	{' '}
	// Error boundary và reporting
	<ThemeProvider>
		{' '}
		// Theme management
		<TranslationProvider>
			{' '}
			// i18n support
			<LoadingProvider>
				{' '}
				// Loading states
				<ToastProvider>
					{' '}
					// Toast notifications
					<SimpleFloatingProvider>
						{' '}
						// Floating UI management
						<AuthProvider>
							{' '}
							// Authentication
							{children}
						</AuthProvider>
					</SimpleFloatingProvider>
				</ToastProvider>
			</LoadingProvider>
		</TranslationProvider>
	</ThemeProvider>
</ErrorManagementProvider>
```

### Internationalization System

#### Translation Architecture

-   **Translation Files**: Organized theo domain trong `src/contexts/translations/`
-   **Translation Keys**: Structured keys như `nav.home`, `collections.create`
-   **Language Support**: English (EN) và Vietnamese (VI)
-   **Type Safety**: TypeScript types cho translation keys
-   **Usage Patterns**:

    ```typescript
    // Component usage
    const { t } = useTranslation();
    const text = t('nav.home');

    // JSX usage
    <Translate text="nav.home" />
    <TranslateText>Submit</TranslateText>
    ```

#### Translation Structure

```
src/contexts/translations/
├── index.ts              # Main export file
├── translation-dict.ts   # Type definitions
├── nav.ts               # Navigation translations
├── home.ts              # Home page translations
├── collections.ts       # Collections translations
├── ui.ts                # UI component translations
└── errors.ts            # Error message translations
```

### Styling System

#### Tailwind CSS Configuration

-   **Version**: Tailwind CSS v4
-   **Design System**: Custom design tokens với CSS variables
-   **Theme**: Support dark/light mode
-   **Animations**: Custom animations với `tw-animate-css`
-   **Components**: Radix UI primitives với custom styling

#### CSS Architecture

```
src/app/globals.css
├── @import 'tailwindcss'
├── @import 'tw-animate-css'
├── Custom CSS variables
├── Base styles
├── Component styles
└── Utility classes
```

## 🔐 SECURITY IMPLEMENTATION

### Multi-Layer Security

#### 1. Middleware Level (`src/middleware.ts`)

-   **Authentication**: JWT token verification cho tất cả API routes
-   **Public Endpoints**: `/api/auth/*` không cần authentication
-   **Rate Limiting**: IP-based request throttling
-   **Security Headers**: X-Content-Type-Options, X-Frame-Options, X-XSS-Protection
-   **CORS**: Configurable origin validation

#### 2. API Level (`src/lib/api-error-middleware.ts`)

-   **Input Validation**: Zod schema validation cho tất cả API endpoints
-   **Error Handling**: Structured error responses với request tracing
-   **Authentication**: Protected route middleware
-   **Middleware Functions**:
    -   `withErrorHandling`: Error boundary cho API routes
    -   `withValidation`: Schema validation
    -   `withRateLimit`: Rate limiting
    -   `withAuth`: Authentication checks
    -   `withCors`: CORS handling

#### 3. Authentication Flow

-   **JWT Tokens**: HttpOnly cookies với secure flags
-   **Multiple Providers**:
    -   Telegram Bot authentication
    -   Google OAuth (feature flag)
    -   Username/Password authentication
-   **Token Management**:
    -   Automatic refresh
    -   Secure storage trong HttpOnly cookies
    -   Environment-based expiration

#### 4. Error Handling System

-   **Comprehensive Error Management**: `src/lib/error-handling.ts`
-   **Error Types**: AppError, ValidationError, NetworkError, etc.
-   **Error Logging**: Centralized error logger với context
-   **Error Boundaries**: React error boundaries cho UI
-   **Graceful Degradation**: Feature degradation khi có lỗi

#### 5. Security Headers (vercel.json)

```json
{
	"headers": [
		{
			"key": "X-Content-Type-Options",
			"value": "nosniff"
		},
		{
			"key": "X-Frame-Options",
			"value": "DENY"
		},
		{
			"key": "X-XSS-Protection",
			"value": "1; mode=block"
		}
	]
}
```

#### 6. Input Validation Patterns

```typescript
// Zod schema validation example
const createCollectionSchema = z.object({
	name: z.string().min(1).max(100),
	target_language: z.nativeEnum(Language),
	source_language: z.nativeEnum(Language),
});

// API route với validation
export const POST = createValidatedApiRoute(createCollectionSchema, async (request, data) => {
	// data is validated và typed
	return NextResponse.json({ success: true });
});
```

## ⚡ PERFORMANCE OPTIMIZATION

### Caching Strategy

#### 1. LLM Response Caching (`src/backend/services/cache.service.ts`)

-   **File-based Caching**: TTL caching với different TTLs:
    -   Vocabulary: 7 days (604800s)
    -   Word Details: 7 days (604800s)
    -   Paragraphs: 3 days (259200s)
    -   Questions: 3 days (259200s)
    -   Evaluations: 30 days (2592000s)
    -   Grammar Practice: 1 day (86400s)
-   **Semantic Caching**: Similar request detection (feature flag)
-   **Cache Keys**: Structured cache keys với semantic indexing
-   **Cache Management**: Automatic cleanup, TTL management

#### 2. Database Caching

-   **In-memory**: Node-cache cho frequently accessed data
-   **Query Optimization**: Prisma query optimization với includes
-   **Connection Pooling**: Database connection management
-   **Dual Database**: Caching strategy cho cả PostgreSQL và MongoDB

#### 3. Frontend Performance

-   **Loading States**: Comprehensive skeleton components:
    -   `ListSkeleton`, `HomeSkeleton`, `CollectionsSkeleton`
    -   `PageSkeleton`, `StatsSkeleton`, `PracticeSessionSkeleton`
-   **Code Splitting**: Dynamic imports cho heavy components
-   **Image Optimization**: Next.js Image component với optimization
-   **Bundle Optimization**: Turbopack cho fast development builds

### LLM Optimization System

#### 1. Model Selection (`src/backend/services/llm.service.ts`)

-   **Adaptive Model Selection**: Chọn model dựa trên complexity
-   **Cost Optimization**: Balance giữa cost và quality
-   **Quality Threshold**: Minimum quality requirements
-   **Latency Threshold**: Maximum response time limits

#### 2. Token Management

-   **Budget Limits**: Daily và monthly token budgets
-   **Cost Alerts**: Threshold-based cost alerting
-   **Usage Tracking**: Comprehensive token usage monitoring
-   **Estimation**: Token estimation trước khi gọi API

#### 3. Prompt Optimization

-   **Compression Techniques**: Reduce prompt size
-   **Template Optimization**: Optimized prompt templates
-   **Batch Processing**: Batch multiple requests
-   **Context Management**: Efficient context handling

#### 4. Monitoring & Analytics

-   **Usage Tracking**: Track token usage, costs, performance
-   **Performance Metrics**: Latency, success rates, quality scores
-   **Error Monitoring**: LLM API errors và fallbacks
-   **Optimization Insights**: Data-driven optimization recommendations

## 🧪 TESTING STRATEGY

### Test Types & Configuration

#### 1. Unit Tests

-   **Jest**: Component và service testing (`jest.config.js`)
-   **Vitest**: Fast unit testing với HMR (`vitest.config.ts`)
-   **Coverage**: 70% threshold cho lines, functions, branches, statements
-   **Test Files**: `*.test.ts`, `*.test.tsx`, `*.spec.ts`, `*.spec.tsx`

#### 2. Integration Tests

-   **API Testing**: API endpoint testing với real database
-   **Service Integration**: Service layer integration tests
-   **Database Integration**: Repository layer testing

#### 3. E2E Tests

-   **Playwright**: Cross-browser E2E testing (`playwright.config.ts`)
-   **Test Browsers**: Chrome, Firefox, Safari, Mobile Chrome
-   **Test Patterns**: User workflows, critical paths
-   **CI Integration**: Automated E2E testing trong GitHub Actions

#### 4. MongoDB Tests

-   **Separate Configuration**: `jest.config.mongodb.js`
-   **In-memory MongoDB**: MongoDB Memory Server cho testing
-   **Isolation**: Separate test suite cho MongoDB integration
-   **Migration Testing**: Test database migration scripts

### Test Scripts

```json
{
	"test": "jest",
	"test:watch": "jest --watch",
	"test:coverage": "jest --coverage",
	"test:unit": "vitest",
	"test:unit:watch": "vitest --watch",
	"test:unit:coverage": "vitest --coverage",
	"test:mongo": "jest --config jest.config.mongodb.js",
	"test:integration": "jest --testPathPattern=integration",
	"test:e2e": "playwright test",
	"test:e2e:ui": "playwright test --ui"
}
```

### Test Environment Setup

-   **Environment Variables**: Test-specific environment configuration
-   **Database Setup**: Separate test databases
-   **Mock Services**: Mock external services (OpenAI, etc.)
-   **Test Data**: Seed data cho testing

## 🚀 DEVELOPMENT WORKFLOWS

### Development Scripts

```json
{
	"dev": "next dev --turbopack",
	"scan": "next dev --turbopack & npx react-scan@latest localhost:3000",
	"build": "yarn p:m && next build",
	"start": "next start",
	"lint": "npx tsc --noEmit --skipLibCheck --project .",
	"p:m": "prisma migrate dev",
	"p:m:r": "prisma migrate reset",
	"p:s": "prisma studio",
	"dup": "docker compose up -d",
	"migration:full": "tsx scripts/migration/run-migration.ts",
	"benchmark:mongodb": "tsx scripts/performance/mongodb-benchmark.ts"
}
```

### Development Environment Setup

#### 1. Local Database

-   **Docker Compose**: PostgreSQL + MongoDB containers
-   **Commands**: `yarn dup` để start databases
-   **Ports**: PostgreSQL (5432), MongoDB (27017)
-   **Data Persistence**: Docker volumes cho data persistence

#### 2. Development Tools

-   **Hot Reload**: Turbopack cho fast refresh
-   **Type Checking**: Continuous TypeScript validation
-   **Performance**: React Scan integration (`yarn scan`)
-   **Database Tools**: Prisma Studio (`yarn p:s`)

#### 3. Environment Variables

-   **Required**: `DATABASE_URL`, `JWT_SECRET`, `LLM_OPENAI_API_KEY`
-   **Optional**: `TELEGRAM_BOT_TOKEN`, `GOOGLE_CLIENT_ID`
-   **Feature Flags**: `FEATURE_MONGODB_ENABLED`, `FEATURE_GOOGLE_LOGIN`
-   **Configuration**: Copy từ `.env.example` sang `.env.local`

### CI/CD Pipeline

#### 1. GitHub Actions (`.github/workflows/nextjs.yml`)

-   **Triggers**: Push to tags `v*`
-   **Jobs**: Lint → Build → Deploy
-   **Node Version**: 20
-   **Package Manager**: Yarn với caching
-   **Build Optimization**: Next.js build caching

#### 2. Vercel Deployment

-   **Auto Deploy**: Từ main branch
-   **Build Command**: `yarn build` (includes Prisma migration)
-   **Environment**: Production environment variables
-   **Region**: Singapore (sin1) cho optimal performance
-   **Framework**: Next.js với automatic optimization

#### 3. Database Migration

-   **Development**: `yarn p:m` cho dev migrations
-   **Production**: Automatic migration trong build process
-   **Reset**: `yarn p:m:r` cho reset database
-   **MongoDB Migration**: Separate migration scripts

## 📝 CODING CONVENTIONS

### File Naming Conventions

#### 1. Components

-   **React Components**: PascalCase (`UserProfile.tsx`, `CollectionCard.tsx`)
-   **UI Components**: PascalCase (`Button.tsx`, `Dialog.tsx`)
-   **Page Components**: PascalCase (`HomePage.tsx`, `CollectionPage.tsx`)

#### 2. Backend Files

-   **Services**: kebab-case (`collection.service.ts`, `auth.service.ts`)
-   **Repositories**: kebab-case (`user.repository.ts`, `word.repository.ts`)
-   **API Routes**: kebab-case (`auth.api.ts`, `collection.api.ts`)
-   **Middleware**: kebab-case (`auth.middleware.ts`)

#### 3. Other Files

-   **Hooks**: camelCase với `use` prefix (`useCollections.ts`, `useAuth.ts`)
-   **Utilities**: kebab-case (`token.util.ts`, `validation.util.ts`)
-   **Types**: kebab-case (`user.types.ts`, `api.types.ts`)
-   **Constants**: kebab-case (`loading-scopes.ts`, `error-codes.ts`)

### Import Organization Rules

```typescript
// 1. External libraries (React, Next.js, third-party)
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { toast } from 'sonner';

// 2. Internal modules (using @/ alias)
import { getCollectionService } from '@/backend/wire';
import { ValidationError } from '@/lib/error-handling';
import { useTranslation } from '@/contexts';

// 3. Relative imports (same directory or subdirectories)
import './styles.css';
import { ComponentHelper } from './helpers';
```

### TypeScript Patterns & Standards

#### 1. Type Definitions

```typescript
// Interface cho component props
interface ComponentProps {
	title: string;
	optional?: boolean;
	children: React.ReactNode;
}

// Type cho API responses
type ApiResponse<T> = {
	success: boolean;
	data?: T;
	error?: string;
};

// Enum cho constants
enum LoadingState {
	IDLE = 'idle',
	LOADING = 'loading',
	SUCCESS = 'success',
	ERROR = 'error',
}
```

#### 2. Service & Repository Patterns

```typescript
// Service interface pattern
export interface ServiceName {
	methodName(params: ParamType): Promise<ReturnType>;
}

// Service implementation pattern
export class ServiceNameImpl implements ServiceName {
	constructor(private readonly getRepository: () => Repository) {}

	async methodName(params: ParamType): Promise<ReturnType> {
		// Implementation
	}
}
```

#### 3. Error Handling Patterns

```typescript
// Custom error types
export class CustomError extends AppError {
	constructor(message: string, context?: ErrorContext) {
		super(
			message,
			'CUSTOM_ERROR_CODE',
			400,
			ErrorSeverity.MEDIUM,
			ErrorCategory.VALIDATION,
			context
		);
	}
}

// Error handling trong services
try {
	const result = await operation();
	return result;
} catch (error) {
	throw new CustomError('Operation failed', { originalError: error });
}
```

## 🔄 DEPLOYMENT & OPERATIONS

### Vercel Configuration

#### 1. Build Configuration (`vercel.json`)

-   **Build Command**: `yarn build` (includes Prisma migration)
-   **Framework**: Next.js với automatic optimization
-   **Output Directory**: `.next`
-   **Install Command**: `yarn install`
-   **Regions**: Singapore (sin1) cho optimal performance

#### 2. Security Headers

-   **X-Content-Type-Options**: `nosniff`
-   **X-Frame-Options**: `DENY`
-   **X-XSS-Protection**: `1; mode=block`

### Environment Management

#### 1. Configuration System (`src/config/config.ts`)

-   **Server Config**: Port, environment settings
-   **Auth Config**: JWT settings, OAuth configurations
-   **LLM Config**: OpenAI settings, optimization parameters
-   **Feature Flags**: Environment-based feature toggles

#### 2. Environment Variables

```bash
# Core Configuration
DATABASE_URL="postgresql://..."
JWT_SECRET="your-secret-key"
LLM_OPENAI_API_KEY="sk-..."

# Feature Flags
FEATURE_MONGODB_ENABLED=false
FEATURE_GOOGLE_LOGIN=false

# LLM Optimization
LLM_OPTIMIZATION_ENABLED=true
LLM_CACHING_ENABLED=true
LLM_TOKEN_BUDGET_DAILY=100000
```

#### 3. Database Configuration

-   **PostgreSQL**: Primary database với Prisma
-   **MongoDB**: Migration target với feature flags
-   **Dual Database**: Support cả hai databases
-   **Connection Management**: Centralized database manager

### Database Operations

#### 1. Migration Strategy

-   **Development**: `yarn p:m` cho dev migrations
-   **Production**: Automatic migration trong build
-   **MongoDB Migration**: Separate migration scripts
-   **Data Transformation**: Custom migration tools

#### 2. Monitoring & Performance

-   **Connection Monitoring**: Connection pool monitoring
-   **Query Performance**: Prisma query optimization
-   **Error Tracking**: Comprehensive error logging

## 🎯 BEST PRACTICES & GUIDELINES

### Development Guidelines

#### 1. Package Management

-   **Always use Yarn**: KHÔNG bao giờ sử dụng npm
-   **Version**: Yarn v4.9.2
-   **Lock File**: Commit `yarn.lock` file
-   **Scripts**: Sử dụng yarn scripts trong `package.json`

#### 2. Type Safety

-   **TypeScript Strict Mode**: Enabled với comprehensive type checking
-   **Interface Definitions**: Clear props interfaces cho components
-   **Generic Types**: Reusable type patterns
-   **Prisma Types**: Sử dụng generated types cho database models

#### 3. Error Handling

-   **Structured Error Management**: Sử dụng `AppError` classes
-   **Error Boundaries**: React error boundaries cho UI
-   **Logging**: Centralized error logging với context
-   **Graceful Degradation**: Handle errors gracefully

#### 4. Security

-   **Input Validation**: Zod schema validation cho tất cả inputs
-   **Authentication**: JWT tokens với secure storage
-   **Authorization**: Role-based access control
-   **HTTPS**: Always use HTTPS trong production

#### 5. Performance

-   **Caching**: Multi-tier caching strategy
-   **Optimization**: LLM optimization, database query optimization
-   **Monitoring**: Performance monitoring và alerting
-   **Loading States**: Skeleton components cho better UX

#### 6. Testing

-   **Unit Tests**: Write tests cho critical functionality
-   **Integration Tests**: Test API endpoints và services
-   **E2E Tests**: Test user workflows
-   **Coverage**: Maintain 70% test coverage

#### 7. Documentation

-   **Code Comments**: Clear comments cho complex logic
-   **README**: Comprehensive project documentation
-   **API Documentation**: Document API endpoints
-   **Architecture**: Keep architecture docs updated

### Code Quality Standards

#### 1. Code Organization

-   **Clean Architecture**: Separation of concerns
-   **Dependency Injection**: Use wire.ts cho service management
-   **Single Responsibility**: Each class/function has one responsibility
-   **DRY Principle**: Don't repeat yourself

#### 2. Code Style

-   **Linting**: TypeScript strict mode
-   **Formatting**: Consistent code formatting
-   **Naming**: Descriptive variable và function names
-   **Comments**: Meaningful comments, not obvious ones

#### 3. Review Process

-   **Code Reviews**: All code must be reviewed
-   **Pull Requests**: Use PR templates
-   **Testing**: All PRs must include tests
-   **Documentation**: Update docs với code changes

#### 4. Monitoring & Maintenance

-   **Performance Monitoring**: Track performance metrics
-   **Error Tracking**: Monitor và fix errors promptly
-   **Refactoring**: Regular code improvement
-   **Dependencies**: Keep dependencies updated

### Common Patterns to Follow

#### 1. API Route Pattern

```typescript
// Always use this pattern for API routes
export const POST = createValidatedApiRoute(schema, async (request, data) => {
	const service = getService();
	const result = await service.method(data);
	return NextResponse.json(result);
});
```

#### 2. Component Pattern

```typescript
// Component with proper TypeScript
interface ComponentProps {
	title: string;
	optional?: boolean;
}

export function Component({ title, optional = false }: ComponentProps) {
	const { t } = useTranslation();

	return (
		<div>
			<Translate text="ui.title" />
		</div>
	);
}
```

#### 3. Service Pattern

```typescript
// Service with dependency injection
export class ServiceImpl implements Service {
	constructor(private readonly getRepository: () => Repository) {}

	async method(params: Params): Promise<Result> {
		const repository = this.getRepository();
		return await repository.operation(params);
	}
}
```

---

## 📚 GETTING STARTED FOR NEW DEVELOPERS

### 1. Setup Development Environment

```bash
# Clone repository
git clone <repository-url>
cd vocab

# Install dependencies (MUST use Yarn)
yarn install

# Setup environment variables
cp .env.example .env.local
# Edit .env.local với your configurations

# Start local databases
yarn dup

# Run database migrations
yarn p:m

# Start development server
yarn dev
```

### 2. Understanding the Codebase

1. **Start with**: `src/app/layout.tsx` để hiểu provider hierarchy
2. **Explore**: `src/backend/wire.ts` để hiểu dependency injection
3. **Review**: `src/components/ui/index.ts` để see available components
4. **Check**: `src/contexts/translations/` để understand i18n system

### 3. Making Your First Change

1. **Create a branch**: `git checkout -b feature/your-feature`
2. **Follow conventions**: Use proper file naming và patterns
3. **Add tests**: Write tests cho your changes
4. **Update docs**: Update documentation if needed
5. **Create PR**: Follow PR template và guidelines

### 4. Key Files to Understand

-   `src/backend/wire.ts`: Dependency injection container
-   `src/middleware.ts`: Request middleware và authentication
-   `src/app/layout.tsx`: Provider hierarchy và global setup
-   `src/lib/error-handling.ts`: Error management system
-   `src/config/config.ts`: Configuration management
-   `package.json`: Scripts và dependencies

### 5. Common Tasks

-   **Add new API endpoint**: Create trong `src/backend/api/`
-   **Add new service**: Create trong `src/backend/services/`
-   **Add new component**: Create trong `src/components/`
-   **Add translations**: Update `src/contexts/translations/`
-   **Run tests**: `yarn test`, `yarn test:e2e`
-   **Database changes**: `yarn p:m` after schema changes

Tài liệu này cung cấp cái nhìn tổng quan đầy đủ về kiến trúc dự án Vocab. Để biết thêm chi tiết, hãy tham khảo các file documentation khác trong thư mục `docs/`.
