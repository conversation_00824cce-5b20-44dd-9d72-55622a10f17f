# Content Recommendation - Development Plan

## Overview

Implement an intelligent content recommendation system that uses machine learning, collaborative filtering, and personalization algorithms to suggest optimal learning materials, exercises, and study paths tailored to individual user needs and preferences.

## Technical Architecture

### Core Components

#### 1. Recommendation Engine

- **Location**: `src/backend/services/recommendation.service.ts`
- **Purpose**: Core recommendation algorithms and content matching
- **Algorithms**:
    - Collaborative filtering
    - Content-based filtering
    - Hybrid recommendation models
    - Deep learning recommendations

#### 2. Content Analysis Service

- **Location**: `src/backend/services/content-analysis.service.ts`
- **Purpose**: Analyze and categorize content for recommendation
- **Features**:
    - Content feature extraction
    - Similarity calculation
    - Difficulty assessment
    - Topic modeling

#### 3. User Modeling Service

- **Location**: `src/backend/services/user-modeling.service.ts`
- **Purpose**: Build comprehensive user profiles for personalization
- **Capabilities**:
    - Interest profiling
    - Skill level assessment
    - Learning pattern analysis
    - Preference tracking

## Database Schema Extensions

### New Tables

```prisma
model ContentRecommendation {
  id              String   @id @default(uuid())
  user_id         String
  content_id      String
  content_type    String   // 'word', 'paragraph', 'exercise', 'collection'
  recommendation_type String // 'next_lesson', 'review', 'challenge', 'similar'
  score           Float    // Recommendation confidence score (0.0-1.0)
  reasoning       Json     // Why this was recommended
  algorithm_used  String   // Which algorithm generated this
  context         Json?    // Context when recommendation was made
  is_clicked      Boolean  @default(false)
  is_completed    Boolean  @default(false)
  user_rating     Int?     // User feedback (1-5)
  created_at      DateTime @default(now())
  expires_at      DateTime?

  user User @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([content_type])
  @@index([recommendation_type])
  @@index([score])
  @@index([created_at])
}

model ContentFeature {
  id              String   @id @default(uuid())
  content_id      String
  content_type    String
  feature_type    String   // 'topic', 'difficulty', 'skill', 'format', 'language_feature'
  feature_name    String
  feature_value   Float    // Normalized feature value (0.0-1.0)
  confidence      Float    @default(1.0)
  extraction_method String // 'manual', 'nlp', 'ml_model', 'user_feedback'
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@unique([content_id, content_type, feature_type, feature_name])
  @@index([content_id, content_type])
  @@index([feature_type])
  @@index([feature_name])
}

model UserInterest {
  id              String   @id @default(uuid())
  user_id         String
  interest_type   String   // 'topic', 'skill', 'content_format', 'difficulty_level'
  interest_name   String
  interest_score  Float    // Interest level (0.0-1.0)
  evidence_count  Int      @default(1)
  last_interaction DateTime @default(now())
  decay_rate      Float    @default(0.1) // How fast interest decays
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  user User @relation(fields: [user_id], references: [id])

  @@unique([user_id, interest_type, interest_name])
  @@index([user_id])
  @@index([interest_type])
  @@index([interest_score])
}

model ContentSimilarity {
  id              String   @id @default(uuid())
  content_a_id    String
  content_a_type  String
  content_b_id    String
  content_b_type  String
  similarity_type String   // 'semantic', 'structural', 'difficulty', 'topic'
  similarity_score Float   // Similarity score (0.0-1.0)
  calculation_method String
  calculated_at   DateTime @default(now())

  @@unique([content_a_id, content_a_type, content_b_id, content_b_type, similarity_type])
  @@index([content_a_id, content_a_type])
  @@index([content_b_id, content_b_type])
  @@index([similarity_type])
  @@index([similarity_score])
}

model RecommendationFeedback {
  id                  String   @id @default(uuid())
  user_id             String
  recommendation_id   String
  feedback_type       String   // 'rating', 'click', 'completion', 'dismissal'
  feedback_value      Float?   // Numeric feedback value
  feedback_text       String?  // Text feedback
  implicit_feedback   Json?    // Implicit feedback signals
  created_at          DateTime @default(now())

  user User @relation(fields: [user_id], references: [id])
  recommendation ContentRecommendation @relation(fields: [recommendation_id], references: [id])

  @@index([user_id])
  @@index([recommendation_id])
  @@index([feedback_type])
  @@index([created_at])
}

model RecommendationModel {
  id              String   @id @default(uuid())
  model_name      String   @unique
  model_type      String   // 'collaborative', 'content_based', 'hybrid', 'deep_learning'
  model_version   String
  parameters      Json     // Model hyperparameters
  training_data   Json     // Training data metadata
  performance_metrics Json // Model performance metrics
  is_active       Boolean  @default(false)
  trained_at      DateTime @default(now())

  @@index([model_type])
  @@index([is_active])
  @@index([trained_at])
}

model UserContentInteraction {
  id              String   @id @default(uuid())
  user_id         String
  content_id      String
  content_type    String
  interaction_type String  // 'view', 'start', 'complete', 'bookmark', 'share', 'rate'
  interaction_value Float? // Numeric value (time spent, rating, etc.)
  interaction_context Json? // Context information
  timestamp       DateTime @default(now())

  user User @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([content_id, content_type])
  @@index([interaction_type])
  @@index([timestamp])
}

model LearningPath {
  id              String   @id @default(uuid())
  user_id         String
  path_name       String
  description     String?
  content_sequence Json    // Ordered list of content items
  current_position Int     @default(0)
  total_items     Int      @default(0)
  estimated_duration Int   // Estimated completion time in minutes
  difficulty_progression String // 'linear', 'adaptive', 'mixed'
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  user User @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([is_active])
  @@index([created_at])
}
```

## Implementation Plan

### Phase 1: Content Analysis and Feature Extraction (Week 1-2)

#### 1.1 Content Feature Extraction

```typescript
// src/backend/services/content-analysis.service.ts
export interface ContentAnalysisService {
	extractContentFeatures(
		contentId: string,
		contentType: string,
		contentData: any
	): Promise<ContentFeature[]>;

	calculateContentSimilarity(
		contentA: ContentItem,
		contentB: ContentItem
	): Promise<ContentSimilarity>;

	analyzeContentDifficulty(contentId: string, contentType: string): Promise<DifficultyAnalysis>;

	extractTopics(contentData: any): Promise<TopicExtraction>;
}

class ContentFeatureExtractor {
	async extractFeatures(
		contentId: string,
		contentType: string,
		contentData: any
	): Promise<ContentFeature[]> {
		const features: ContentFeature[] = [];

		switch (contentType) {
			case 'word':
				features.push(...(await this.extractWordFeatures(contentId, contentData)));
				break;
			case 'paragraph':
				features.push(...(await this.extractParagraphFeatures(contentId, contentData)));
				break;
			case 'exercise':
				features.push(...(await this.extractExerciseFeatures(contentId, contentData)));
				break;
		}

		// Extract common features
		features.push(...(await this.extractCommonFeatures(contentId, contentType, contentData)));

		return features;
	}

	private async extractWordFeatures(
		contentId: string,
		wordData: WordData
	): Promise<ContentFeature[]> {
		const features: ContentFeature[] = [];

		// Part of speech features
		if (wordData.definitions) {
			for (const definition of wordData.definitions) {
				for (const pos of definition.pos) {
					features.push({
						content_id: contentId,
						content_type: 'word',
						feature_type: 'part_of_speech',
						feature_name: pos,
						feature_value: 1.0,
						extraction_method: 'manual',
					});
				}
			}
		}

		// Frequency features
		const frequency = await this.getWordFrequency(wordData.term);
		features.push({
			content_id: contentId,
			content_type: 'word',
			feature_type: 'frequency',
			feature_name: 'corpus_frequency',
			feature_value: this.normalizeFrequency(frequency),
			extraction_method: 'corpus_analysis',
		});

		// Semantic features
		const semanticFeatures = await this.extractSemanticFeatures(wordData.term);
		features.push(
			...semanticFeatures.map((sf) => ({
				content_id: contentId,
				content_type: 'word',
				feature_type: 'semantic',
				feature_name: sf.name,
				feature_value: sf.value,
				extraction_method: 'nlp',
			}))
		);

		return features;
	}

	private async extractSemanticFeatures(word: string): Promise<SemanticFeature[]> {
		// Use word embeddings to extract semantic features
		const embedding = await this.getWordEmbedding(word);
		const semanticClusters = await this.getSemanticClusters();

		const features: SemanticFeature[] = [];

		for (const cluster of semanticClusters) {
			const similarity = this.calculateCosineSimilarity(embedding, cluster.centroid);
			if (similarity > 0.3) {
				// Threshold for relevance
				features.push({
					name: cluster.name,
					value: similarity,
				});
			}
		}

		return features;
	}
}
```

#### 1.2 Content Similarity Calculation

```typescript
class ContentSimilarityCalculator {
	async calculateSimilarity(
		contentA: ContentItem,
		contentB: ContentItem
	): Promise<ContentSimilarity[]> {
		const similarities: ContentSimilarity[] = [];

		// Semantic similarity
		const semanticSim = await this.calculateSemanticSimilarity(contentA, contentB);
		similarities.push({
			content_a_id: contentA.id,
			content_a_type: contentA.type,
			content_b_id: contentB.id,
			content_b_type: contentB.type,
			similarity_type: 'semantic',
			similarity_score: semanticSim,
			calculation_method: 'word_embeddings',
		});

		// Structural similarity
		const structuralSim = await this.calculateStructuralSimilarity(contentA, contentB);
		similarities.push({
			content_a_id: contentA.id,
			content_a_type: contentA.type,
			content_b_id: contentB.id,
			content_b_type: contentB.type,
			similarity_type: 'structural',
			similarity_score: structuralSim,
			calculation_method: 'feature_comparison',
		});

		// Difficulty similarity
		const difficultySim = await this.calculateDifficultySimilarity(contentA, contentB);
		similarities.push({
			content_a_id: contentA.id,
			content_a_type: contentA.type,
			content_b_id: contentB.id,
			content_b_type: contentB.type,
			similarity_type: 'difficulty',
			similarity_score: difficultySim,
			calculation_method: 'difficulty_analysis',
		});

		return similarities;
	}

	private async calculateSemanticSimilarity(
		contentA: ContentItem,
		contentB: ContentItem
	): Promise<number> {
		const embeddingA = await this.getContentEmbedding(contentA);
		const embeddingB = await this.getContentEmbedding(contentB);

		return this.calculateCosineSimilarity(embeddingA, embeddingB);
	}

	private async getContentEmbedding(content: ContentItem): Promise<number[]> {
		switch (content.type) {
			case 'word':
				return await this.getWordEmbedding(content.data.term);
			case 'paragraph':
				return await this.getParagraphEmbedding(content.data.content);
			case 'exercise':
				return await this.getExerciseEmbedding(content.data);
			default:
				throw new Error(`Unsupported content type: ${content.type}`);
		}
	}
}
```

### Phase 2: User Modeling and Interest Tracking (Week 3-4)

#### 2.1 User Interest Profiling

```typescript
// src/backend/services/user-modeling.service.ts
export interface UserModelingService {
	updateUserInterests(userId: string, interactionData: InteractionData): Promise<void>;

	getUserInterestProfile(userId: string): Promise<UserInterestProfile>;

	calculateUserContentAffinity(
		userId: string,
		contentId: string,
		contentType: string
	): Promise<number>;

	predictUserPreferences(userId: string, contentItems: ContentItem[]): Promise<PreferenceScore[]>;
}

class UserInterestTracker {
	async updateInterests(userId: string, interactionData: InteractionData): Promise<void> {
		// Extract interest signals from interaction
		const interestSignals = this.extractInterestSignals(interactionData);

		for (const signal of interestSignals) {
			await this.updateOrCreateInterest(userId, signal);
		}

		// Apply interest decay to all user interests
		await this.applyInterestDecay(userId);
	}

	private extractInterestSignals(interactionData: InteractionData): InterestSignal[] {
		const signals: InterestSignal[] = [];

		// Content type interest
		signals.push({
			type: 'content_format',
			name: interactionData.contentType,
			strength: this.calculateInteractionStrength(interactionData),
			evidence: 'direct_interaction',
		});

		// Topic interests (from content features)
		if (interactionData.contentFeatures) {
			for (const feature of interactionData.contentFeatures) {
				if (feature.feature_type === 'topic') {
					signals.push({
						type: 'topic',
						name: feature.feature_name,
						strength:
							feature.feature_value *
							this.calculateInteractionStrength(interactionData),
						evidence: 'content_analysis',
					});
				}
			}
		}

		// Difficulty preference
		if (interactionData.difficultyLevel) {
			signals.push({
				type: 'difficulty_level',
				name: interactionData.difficultyLevel,
				strength: this.calculateDifficultyPreference(interactionData),
				evidence: 'performance_analysis',
			});
		}

		return signals;
	}

	private async updateOrCreateInterest(userId: string, signal: InterestSignal): Promise<void> {
		const existing = await this.prisma.userInterest.findUnique({
			where: {
				user_id_interest_type_interest_name: {
					user_id: userId,
					interest_type: signal.type,
					interest_name: signal.name,
				},
			},
		});

		if (existing) {
			// Update existing interest with exponential moving average
			const alpha = 0.1; // Learning rate
			const newScore = existing.interest_score * (1 - alpha) + signal.strength * alpha;

			await this.prisma.userInterest.update({
				where: { id: existing.id },
				data: {
					interest_score: newScore,
					evidence_count: existing.evidence_count + 1,
					last_interaction: new Date(),
				},
			});
		} else {
			// Create new interest
			await this.prisma.userInterest.create({
				data: {
					user_id: userId,
					interest_type: signal.type,
					interest_name: signal.name,
					interest_score: signal.strength,
					evidence_count: 1,
				},
			});
		}
	}
}
```

### Phase 3: Recommendation Algorithms (Week 5-6)

#### 3.1 Collaborative Filtering

```typescript
class CollaborativeFilteringRecommender {
	async generateRecommendations(
		userId: string,
		limit: number = 10
	): Promise<ContentRecommendation[]> {
		// Find similar users
		const similarUsers = await this.findSimilarUsers(userId, 50);

		// Get content liked by similar users
		const candidateContent = await this.getCandidateContent(similarUsers, userId);

		// Score and rank recommendations
		const scoredRecommendations = await this.scoreRecommendations(
			userId,
			candidateContent,
			similarUsers
		);

		return scoredRecommendations
			.sort((a, b) => b.score - a.score)
			.slice(0, limit)
			.map((rec) => this.formatRecommendation(rec, 'collaborative'));
	}

	private async findSimilarUsers(userId: string, limit: number): Promise<SimilarUser[]> {
		const userInteractions = await this.getUserInteractions(userId);
		const allUsers = await this.getAllActiveUsers();

		const similarities: SimilarUser[] = [];

		for (const otherUser of allUsers) {
			if (otherUser.id === userId) continue;

			const otherInteractions = await this.getUserInteractions(otherUser.id);
			const similarity = this.calculateUserSimilarity(userInteractions, otherInteractions);

			if (similarity > 0.1) {
				// Minimum similarity threshold
				similarities.push({
					userId: otherUser.id,
					similarity,
				});
			}
		}

		return similarities.sort((a, b) => b.similarity - a.similarity).slice(0, limit);
	}

	private calculateUserSimilarity(userA: UserInteraction[], userB: UserInteraction[]): number {
		// Create content-rating matrices
		const ratingsA = new Map<string, number>();
		const ratingsB = new Map<string, number>();

		userA.forEach((interaction) => {
			const key = `${interaction.content_id}_${interaction.content_type}`;
			ratingsA.set(key, this.interactionToRating(interaction));
		});

		userB.forEach((interaction) => {
			const key = `${interaction.content_id}_${interaction.content_type}`;
			ratingsB.set(key, this.interactionToRating(interaction));
		});

		// Calculate Pearson correlation coefficient
		return this.calculatePearsonCorrelation(ratingsA, ratingsB);
	}
}
```

#### 3.2 Content-Based Filtering

```typescript
class ContentBasedRecommender {
	async generateRecommendations(
		userId: string,
		limit: number = 10
	): Promise<ContentRecommendation[]> {
		// Get user's content interaction history
		const userHistory = await this.getUserContentHistory(userId);

		// Build user content profile
		const userProfile = await this.buildUserContentProfile(userHistory);

		// Find candidate content
		const candidateContent = await this.getCandidateContent(userId);

		// Score content based on profile similarity
		const scoredContent = await this.scoreContentSimilarity(userProfile, candidateContent);

		return scoredContent
			.sort((a, b) => b.score - a.score)
			.slice(0, limit)
			.map((content) => this.formatRecommendation(content, 'content_based'));
	}

	private async buildUserContentProfile(
		userHistory: UserContentInteraction[]
	): Promise<UserContentProfile> {
		const profile: UserContentProfile = {
			topicWeights: new Map(),
			difficultyPreference: 0.5,
			formatPreferences: new Map(),
			featureWeights: new Map(),
		};

		let totalWeight = 0;

		for (const interaction of userHistory) {
			const weight = this.calculateInteractionWeight(interaction);
			const contentFeatures = await this.getContentFeatures(
				interaction.content_id,
				interaction.content_type
			);

			// Update topic weights
			for (const feature of contentFeatures) {
				if (feature.feature_type === 'topic') {
					const currentWeight = profile.topicWeights.get(feature.feature_name) || 0;
					profile.topicWeights.set(
						feature.feature_name,
						currentWeight + weight * feature.feature_value
					);
				}
			}

			totalWeight += weight;
		}

		// Normalize weights
		for (const [topic, weight] of profile.topicWeights) {
			profile.topicWeights.set(topic, weight / totalWeight);
		}

		return profile;
	}

	private async scoreContentSimilarity(
		userProfile: UserContentProfile,
		candidateContent: ContentItem[]
	): Promise<ScoredContent[]> {
		const scoredContent: ScoredContent[] = [];

		for (const content of candidateContent) {
			const features = await this.getContentFeatures(content.id, content.type);
			let score = 0;

			// Calculate topic similarity
			for (const feature of features) {
				if (feature.feature_type === 'topic') {
					const userWeight = userProfile.topicWeights.get(feature.feature_name) || 0;
					score += userWeight * feature.feature_value;
				}
			}

			scoredContent.push({
				content,
				score,
				reasoning: this.generateContentReasoning(userProfile, features),
			});
		}

		return scoredContent;
	}
}
```

### Phase 4: Hybrid Recommendation System (Week 7-8)

#### 4.1 Hybrid Model Integration

```typescript
class HybridRecommendationEngine {
	private collaborativeRecommender: CollaborativeFilteringRecommender;
	private contentBasedRecommender: ContentBasedRecommender;
	private deepLearningRecommender: DeepLearningRecommender;

	async generateRecommendations(
		userId: string,
		context: RecommendationContext,
		limit: number = 10
	): Promise<ContentRecommendation[]> {
		// Get recommendations from different algorithms
		const [collaborative, contentBased, deepLearning] = await Promise.all([
			this.collaborativeRecommender.generateRecommendations(userId, limit * 2),
			this.contentBasedRecommender.generateRecommendations(userId, limit * 2),
			this.deepLearningRecommender.generateRecommendations(userId, limit * 2),
		]);

		// Combine recommendations using weighted ensemble
		const combinedRecommendations = this.combineRecommendations([
			{ recommendations: collaborative, weight: 0.4, algorithm: 'collaborative' },
			{ recommendations: contentBased, weight: 0.4, algorithm: 'content_based' },
			{ recommendations: deepLearning, weight: 0.2, algorithm: 'deep_learning' },
		]);

		// Apply context-based filtering
		const contextFilteredRecs = this.applyContextFiltering(combinedRecommendations, context);

		// Diversify recommendations
		const diversifiedRecs = this.diversifyRecommendations(contextFilteredRecs);

		return diversifiedRecs.slice(0, limit);
	}

	private combineRecommendations(algorithmResults: AlgorithmResult[]): ContentRecommendation[] {
		const combinedScores = new Map<string, CombinedScore>();

		for (const result of algorithmResults) {
			for (const rec of result.recommendations) {
				const key = `${rec.content_id}_${rec.content_type}`;
				const existing = combinedScores.get(key);

				if (existing) {
					existing.totalScore += rec.score * result.weight;
					existing.algorithmCount++;
					existing.algorithms.push(result.algorithm);
				} else {
					combinedScores.set(key, {
						recommendation: rec,
						totalScore: rec.score * result.weight,
						algorithmCount: 1,
						algorithms: [result.algorithm],
					});
				}
			}
		}

		// Convert to final recommendations
		return Array.from(combinedScores.values())
			.map((combined) => ({
				...combined.recommendation,
				score: combined.totalScore,
				algorithm_used: combined.algorithms.join(','),
				reasoning: this.combineReasoning(combined),
			}))
			.sort((a, b) => b.score - a.score);
	}

	private diversifyRecommendations(
		recommendations: ContentRecommendation[]
	): ContentRecommendation[] {
		const diversified: ContentRecommendation[] = [];
		const usedTopics = new Set<string>();
		const usedTypes = new Map<string, number>();

		for (const rec of recommendations) {
			// Check diversity constraints
			const contentFeatures = await this.getContentFeatures(rec.content_id, rec.content_type);
			const topics = contentFeatures
				.filter((f) => f.feature_type === 'topic')
				.map((f) => f.feature_name);

			// Limit repetition of topics and content types
			const typeCount = usedTypes.get(rec.content_type) || 0;
			const hasNewTopic = topics.some((topic) => !usedTopics.has(topic));

			if (typeCount < 3 && (hasNewTopic || diversified.length < 5)) {
				diversified.push(rec);
				topics.forEach((topic) => usedTopics.add(topic));
				usedTypes.set(rec.content_type, typeCount + 1);
			}

			if (diversified.length >= 10) break;
		}

		return diversified;
	}
}
```

## Frontend Integration

### Recommendation Dashboard

```typescript
// src/components/ui/recommendation-dashboard.tsx
export function RecommendationDashboard({ userId }: { userId: string }) {
  const { recommendations, loading } = useRecommendations(userId);
  const { feedback } = useRecommendationFeedback();

  return (
    <div className="recommendation-dashboard">
      <div className="dashboard-header">
        <h2>Recommended for You</h2>
        <RecommendationFilters />
      </div>

      <div className="recommendation-sections">
        <RecommendationSection
          title="Continue Learning"
          recommendations={recommendations.filter(r => r.recommendation_type === 'next_lesson')}
          onFeedback={feedback}
        />

        <RecommendationSection
          title="Review & Practice"
          recommendations={recommendations.filter(r => r.recommendation_type === 'review')}
          onFeedback={feedback}
        />

        <RecommendationSection
          title="Challenge Yourself"
          recommendations={recommendations.filter(r => r.recommendation_type === 'challenge')}
          onFeedback={feedback}
        />
      </div>
    </div>
  );
}
```

## Success Criteria

### Recommendation Quality

- 75%+ user click-through rate on recommendations
- 60%+ completion rate for recommended content
- 4.0+ average user rating for recommendations
- 30% improvement in content discovery

### Personalization Effectiveness

- 40% increase in user engagement with recommended content
- 25% improvement in learning path adherence
- 50% reduction in content browsing time
- 35% increase in user session duration

## Timeline

- **Week 1-2**: Content analysis and feature extraction systems
- **Week 3-4**: User modeling and interest tracking
- **Week 5-6**: Individual recommendation algorithms
- **Week 7-8**: Hybrid system integration and optimization
- **Week 9**: Testing and performance tuning
- **Week 10**: Deployment and feedback collection
