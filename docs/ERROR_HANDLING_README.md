# 🛡️ Comprehensive Error Handling System

## 📋 Tổng quan

Hệ thống xử lý lỗi toàn diện cho ứng dụng React với các tính năng:

- ✅ **Error Logging**: Ghi log có cấu trúc với nhiều mức độ nghiêm trọng
- ✅ **User-Friendly Display**: Hi<PERSON>n thị lỗi thân thiện với người dùng
- ✅ **Fallback Mechanisms**: Cơ chế dự phòng và khôi phục
- ✅ **Global Error Handling**: Xử lý lỗi toàn cục và tự động

## 🚀 Cách sử dụng

### 1. Error Context và Hooks

```typescript
import { useError, useErrorHandler } from '@/contexts/error-context';

function MyComponent() {
	const { errorState, addError, clearErrors } = useError();
	const { handleError } = useErrorHandler('MyComponent');

	const handleAction = async () => {
		try {
			await someApiCall();
		} catch (error) {
			handleError(error, 'api_call_failed');
		}
	};
}
```

### 2. API Calls với Retry tự động

```typescript
import { apiClient } from '@/lib/api-interceptor';

// Tự động retry và xử lý lỗi
const data = await apiClient.get('/api/data');
const result = await apiClient.post('/api/users', userData);
```

### 3. Error Boundaries

```typescript
import { ErrorBoundary, withErrorBoundary } from '@/components/error';

// Sử dụng trực tiếp
<ErrorBoundary level="section" name="MySection">
	<MyComponent />
</ErrorBoundary>;

// Hoặc sử dụng HOC
export default withErrorBoundary(MyComponent, {
	level: 'component',
	name: 'MyComponent',
});
```

### 4. Fallback Components

```typescript
import { DataFallback, ImageFallback, NetworkFallback } from '@/components/fallback';

// Data fallback với loading và empty states
<DataFallback
  isLoading={loading}
  isEmpty={data.length === 0}
  error={error}
  onRetry={refetch}
>
  {data.map(item => <Item key={item.id} {...item} />)}
</DataFallback>

// Image fallback
<ImageFallback
  src={imageUrl}
  alt="Description"
  fallbackSrc="/placeholder.jpg"
  className="w-32 h-32"
/>

// Network fallback
<NetworkFallback>
  <OnlineOnlyContent />
</NetworkFallback>
```

### 5. Retry Logic

```typescript
import { useRetry, withRetry } from '@/lib/retry';

// Sử dụng hook
const { execute, isRetrying } = useRetry(async () => await apiCall(), {
	maxAttempts: 3,
	enabled: true,
});

// Sử dụng trực tiếp
const result = await withRetry(() => apiCall(), { maxAttempts: 3, baseDelay: 1000 });
```

### 6. Offline Support

```typescript
import { useOffline, useOfflineQueue } from '@/hooks/use-offline';

const { isOnline, isOffline, checkConnectivity } = useOffline();
const { addToQueue, processQueue } = useOfflineQueue();

// Thêm action vào queue khi offline
if (isOffline) {
	addToQueue({
		type: 'CREATE_USER',
		payload: userData,
		maxRetries: 3,
	});
}
```

### 7. Graceful Degradation

```typescript
import { useGracefulDegradation, COMMON_FEATURES } from '@/hooks/use-graceful-degradation';

const { currentLevel, isFeatureAvailable } = useGracefulDegradation({
	features: [COMMON_FEATURES.api, COMMON_FEATURES.storage],
});

if (isFeatureAvailable('notifications')) {
	// Hiển thị notifications
} else {
	// Fallback to console logging
}
```

## 🎯 Error Types

### Built-in Error Classes

```typescript
import {
	AppError,
	ValidationError,
	NetworkError,
	ServerError,
	UnauthorizedError,
	ForbiddenError,
	NotFoundError,
	TimeoutError,
} from '@/lib/error-handling';

// Custom error with context
throw new AppError('Custom error message', 'CUSTOM_ERROR_CODE', 400, {
	userId: '123',
	action: 'create_post',
});

// Validation error
throw new ValidationError('Invalid email format', {
	field: 'email',
	value: 'invalid-email',
});

// Network error
throw new NetworkError('Failed to connect to API', {
	url: 'https://api.example.com',
	timeout: 5000,
});
```

## 🔧 Integration Patterns

### 1. Component Error Handling

```typescript
import { withErrorHandling, ErrorBoundary } from '@/lib/error-management';

// Method 1: HOC wrapper
const SafeComponent = withErrorHandling(MyComponent, 'MyComponent', {
	fallback: ErrorFallback,
	logErrors: true,
	retryable: true,
});

// Method 2: Error Boundary wrapper
function MyPage() {
	return (
		<ErrorBoundary level="page" name="MyPage">
			<MyComponent />
		</ErrorBoundary>
	);
}

// Method 3: Manual error handling
function MyComponent() {
	const { handleError } = useErrorHandler('MyComponent');

	const handleAction = async () => {
		try {
			await riskyOperation();
		} catch (error) {
			handleError(error, 'risky_operation');
		}
	};
}
```

### 2. Hook Error Handling

```typescript
import { withHookErrorHandling } from '@/lib/error-integration';

// Enhance existing hook
const useEnhancedCollections = withHookErrorHandling(useCollections, 'useCollections');

// Manual error handling in hooks
function useMyHook() {
	const { handleError } = useErrorHandler('useMyHook');

	const fetchData = useCallback(async () => {
		try {
			return await apiCall();
		} catch (error) {
			handleError(error, 'fetch_data');
			throw error;
		}
	}, [handleError]);

	return { fetchData };
}
```

### 3. Service Error Handling

```typescript
import { withServiceErrorHandling, enhanceServiceWithErrorHandling } from '@/lib/error-integration';

// Method 1: Enhance individual methods
class MyService {
	async getData() {
		return withServiceErrorHandling(
			async () => {
				// Service logic
			},
			'MyService',
			'getData'
		)();
	}
}

// Method 2: Enhance entire service
const enhancedService = enhanceServiceWithErrorHandling(new MyService(), 'MyService');
```

### 4. API Route Error Handling

```typescript
import { createApiRoute, createProtectedApiRoute, withValidation } from '@/lib/error-management';
import { z } from 'zod';

// Basic API route with error handling
export const GET = createApiRoute(async (request) => {
	const data = await fetchData();
	return NextResponse.json(data);
});

// Protected API route
export const POST = createProtectedApiRoute(async (request) => {
	// Authenticated logic
	return NextResponse.json({ success: true });
});

// API route with validation
const schema = z.object({
	name: z.string(),
	email: z.string().email(),
});

export const PUT = withValidation(schema, async (request, data) => {
	// data is validated
	return NextResponse.json({ data });
});
```

### 5. Form Error Handling

```typescript
import { EnhancedForm, useEnhancedForm } from '@/components/ui/enhanced-form';
import { z } from 'zod';

const schema = z.object({
	name: z.string().min(1),
	email: z.string().email(),
});

function MyForm() {
	const handleSubmit = async (data: z.infer<typeof schema>) => {
		// Form submission logic
		await submitData(data);
	};

	return (
		<EnhancedForm schema={schema} onSubmit={handleSubmit} enableRetry={true} maxRetries={3}>
			{/* Form fields */}
		</EnhancedForm>
	);
}
```

### 6. Database Error Handling

```typescript
import {
	withDatabaseErrorHandling,
	enhanceRepositoryWithErrorHandling,
} from '@/lib/error-integration';

// Method 1: Enhance individual operations
class MyRepository {
	async findById(id: string) {
		return withDatabaseErrorHandling(async () => {
			return await this.model.findUnique({ where: { id } });
		}, 'findById')();
	}
}

// Method 2: Enhance entire repository
const enhancedRepository = enhanceRepositoryWithErrorHandling(new MyRepository(), 'MyRepository');
```

## 🌐 Global Error Handling

### Error Management Provider Setup

```typescript
// In your app layout
import { ErrorManagementProvider } from '@/providers/error-management-provider';

export default function RootLayout({ children }) {
	return (
		<html>
			<body>
				<ErrorManagementProvider
					enableErrorReporting={true}
					enableNetworkDetection={true}
					enableApiInterception={true}
				>
					{children}
				</ErrorManagementProvider>
			</body>
		</html>
	);
}
```

### Global Error Boundaries

```typescript
// Wrap entire app sections
function App() {
	return (
		<ErrorBoundary level="app" name="App">
			<Header />
			<ErrorBoundary level="page" name="MainContent">
				<MainContent />
			</ErrorBoundary>
			<Footer />
		</ErrorBoundary>
	);
}
```

## 📊 Error Monitoring & Reporting

### Error Logging

```typescript
import { errorLogger } from '@/lib/error-handling';

// Log different severity levels
errorLogger.debug('Debug message', context);
errorLogger.info('Info message', context);
errorLogger.warn('Warning message', context);
errorLogger.error('Error message', error, context);
errorLogger.fatal('Fatal error', error, context);
```

### Error Reporting Integration

```typescript
// Configure external error reporting
import { initializeErrorReporting } from '@/lib/error-reporting';

initializeErrorReporting({
	dsn: process.env.SENTRY_DSN,
	environment: process.env.NODE_ENV,
	enableInDevelopment: false,
});
```

## 🔄 Retry & Recovery

### Automatic Retry

```typescript
import { retryApiCall, useRetry } from '@/lib/retry';

// Function-level retry
const result = await retryApiCall(() => apiCall(), { maxAttempts: 3, baseDelay: 1000 });

// Hook-based retry
const { execute, isRetrying } = useRetry(async () => await apiCall(), {
	maxAttempts: 3,
	enabled: true,
});
```

### Graceful Degradation

```typescript
import { useGracefulDegradation, COMMON_FEATURES } from '@/hooks/use-graceful-degradation';

const { currentLevel, isFeatureAvailable } = useGracefulDegradation({
	features: [COMMON_FEATURES.api, COMMON_FEATURES.storage],
});

if (isFeatureAvailable('notifications')) {
	// Show notifications
} else {
	// Fallback behavior
}
```

## 🌍 Offline Support

### Offline Detection & Queue

```typescript
import { useOffline, useOfflineQueue } from '@/hooks/use-offline';

const { isOnline, isOffline } = useOffline();
const { addToQueue, processQueue } = useOfflineQueue();

if (isOffline) {
	addToQueue({
		type: 'CREATE_POST',
		payload: postData,
		maxRetries: 3,
	});
}
```

## 🎨 UI Error Components

### Data Fallback

```typescript
import { DataFallback } from '@/components/fallback';

<DataFallback
	isLoading={loading}
	error={error}
	isEmpty={data.length === 0}
	onRetry={refetch}
	emptyMessage="No data available"
>
	{data.map((item) => (
		<Item key={item.id} {...item} />
	))}
</DataFallback>;
```

### Network Fallback

```typescript
import { NetworkFallback } from '@/components/fallback';

<NetworkFallback>
	<OnlineOnlyContent />
</NetworkFallback>;
```

### Image Fallback

```typescript
import { ImageFallback } from '@/components/fallback';

<ImageFallback
	src={imageUrl}
	alt="Description"
	fallbackSrc="/placeholder.jpg"
	onError={(error) => console.log('Image failed to load')}
/>;
```

## 🧪 Testing Error Handling

### Error Test Page

Visit `/error-test` to test all error handling features:

- Trigger different error types
- Test error boundaries
- Test retry mechanisms
- Test offline functionality
- Test fallback components

### Development Helpers

```typescript
// Available in development mode
window.__errorManagement.triggerTestError();
window.__errorManagement.triggerTestAsyncError();
```

## 📋 Best Practices

### 1. **Layer-Specific Error Handling**

- **UI Layer**: Use ErrorBoundary and DataFallback components
- **Hook Layer**: Use withHookErrorHandling or manual error handling
- **Service Layer**: Use withServiceErrorHandling
- **Repository Layer**: Use withDatabaseErrorHandling
- **API Layer**: Use createApiRoute and middleware

### 2. **Error Context**

Always provide meaningful context when handling errors:

```typescript
handleError(error, 'action_name', {
	userId,
	resourceId,
	timestamp: new Date().toISOString(),
});
```

### 3. **User-Friendly Messages**

Use appropriate error messages for users:

```typescript
// Good
throw new ValidationError('Please enter a valid email address');

// Bad
throw new Error('Invalid input at field[0].email.format');
```

### 4. **Retry Strategy**

Implement intelligent retry for transient errors:

```typescript
// Retry network errors but not validation errors
const shouldRetry = error instanceof NetworkError || error instanceof TimeoutError;
```

### 5. **Error Boundaries Placement**

Place error boundaries at strategic levels:

```typescript
// App level - catches all errors
<ErrorBoundary level="app" name="App">
	// Page level - isolates page errors
	<ErrorBoundary level="page" name="HomePage">
		// Section level - isolates component errors
		<ErrorBoundary level="section" name="UserProfile">
			<UserProfile />
		</ErrorBoundary>
	</ErrorBoundary>
</ErrorBoundary>
```

## 🚀 Production Considerations

### 1. **Error Reporting**

- Configure external error reporting (Sentry, LogRocket, etc.)
- Set up alerts for critical errors
- Monitor error rates and patterns

### 2. **Performance**

- Error handling should not impact performance
- Use lazy loading for error components
- Implement efficient retry strategies

### 3. **Security**

- Don't expose sensitive information in error messages
- Sanitize error details in production
- Log detailed errors server-side only

### 4. **Monitoring**

- Track error rates by component/feature
- Monitor retry success rates
- Track user experience impact

## 📁 File Structure Summary

// Validation error
throw new ValidationError('Email is required');

// Network error
throw new NetworkError('Connection failed');

// Custom error với context
throw new AppError(
'Custom error message',
'CUSTOM_ERROR_CODE',
500,
ErrorSeverity.HIGH,
ErrorCategory.SERVER,
context
);

````

### Error Severity Levels

- `LOW`: Lỗi nhỏ, không ảnh hưởng nhiều
- `MEDIUM`: Lỗi trung bình, cần chú ý
- `HIGH`: Lỗi nghiêm trọng, cần xử lý ngay
- `CRITICAL`: Lỗi cực kỳ nghiêm trọng, có thể crash app

## 🔧 Configuration

### Error Management Provider

```typescript
// app/layout.tsx
<ErrorManagementProvider
  config={{
    enableErrorReporting: true,
    enableNetworkDetection: true,
    enableApiInterception: true,
    errorReportingEndpoint: '/api/errors',
    environment: process.env.NODE_ENV,
    buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION,
  }}
>
  <App />
</ErrorManagementProvider>
````

### Error Logger Configuration

```typescript
import { errorLogger } from '@/lib/error-handling';

errorLogger.configure({
	enableConsoleLogging: true,
	enableRemoteLogging: true,
	remoteEndpoint: '/api/logs',
	maxLogs: 1000,
});
```

## 📊 Monitoring và Analytics

### Error Reporting

```typescript
import { reportError } from '@/lib/error-reporting';

// Report error manually
await reportError(error, {
	userId: user.id,
	action: 'button_click',
	additionalContext: { buttonId: 'submit' },
});
```

### Error Statistics

```typescript
import { errorLogger } from '@/lib/error-handling';

// Lấy logs theo level
const errorLogs = errorLogger.getLogs('ERROR', 50);
const allLogs = errorLogger.getLogs();

// Export logs
const logsJson = errorLogger.exportLogs();
```

## 🧪 Testing

Truy cập `/error-test` để test các tính năng:

- Trigger các loại lỗi khác nhau
- Test error boundaries
- Test retry mechanisms
- Test offline functionality
- Test fallback components

## 📁 File Structure

```
src/
├── lib/
│   ├── error-handling.ts          # Core error classes
│   ├── retry.ts                   # Retry logic
│   ├── api-interceptor.ts         # HTTP error handling
│   ├── form-error-handler.ts      # Form validation
│   ├── network-detector.ts        # Network status
│   ├── error-reporting.ts         # Error reporting
│   └── error-management.ts        # Main exports
├── contexts/
│   └── error-context.tsx          # Error state management
├── components/
│   ├── error/                     # Error UI components
│   └── fallback/                  # Fallback components
├── hooks/
│   ├── use-offline.ts             # Offline detection
│   └── use-graceful-degradation.ts # Feature degradation
└── providers/
    └── error-management-provider.tsx # Global provider
```

## 🔍 Best Practices

1. **Luôn sử dụng Error Boundaries** cho các component quan trọng
2. **Wrap API calls** trong try-catch và sử dụng error handlers
3. **Provide fallback UI** cho tất cả các trạng thái lỗi
4. **Log errors** với đủ context để debug
5. **Test error scenarios** thường xuyên
6. **Monitor error rates** và patterns
7. **Provide recovery actions** khi có thể

## 🚨 Troubleshooting

### Common Issues

1. **Error boundaries không catch async errors**: Sử dụng error handlers trong async functions
2. **Toast notifications không hiện**: Kiểm tra ErrorToastContainer đã được add vào layout
3. **Retry không hoạt động**: Kiểm tra error có retryable không
4. **Offline queue không process**: Kiểm tra network detection và queue processing

### Debug Mode

Trong development mode, có thể access global utilities:

```javascript
// Browser console
window.__errorManagement.triggerTestError();
window.__errorManagement.errorLogger.getLogs();
```
