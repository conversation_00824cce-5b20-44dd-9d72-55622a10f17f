# Image-Word Association Development Plan

## Overview

Implement a comprehensive visual learning system that associates images with vocabulary words to enhance memory retention through visual-spatial learning techniques and multi-modal learning approaches.

## Technical Architecture

### Database Schema Extensions

#### Visual Learning Models

```prisma
model WordImage {
  id          String      @id @default(uuid())
  wordId      String
  imageUrl    String
  imageType   ImageType   @default(PHOTO)
  source      ImageSource @default(GENERATED)
  description String?
  tags        String[]
  isActive    Boolean     @default(true)
  quality     Float       @default(0.0) // AI quality score
  userRating  Float       @default(0.0) // User rating average
  ratingCount Int         @default(0)
  createdBy   String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  word        Word        @relation(fields: [wordId], references: [id], onDelete: Cascade)
  creator     User?       @relation(fields: [createdBy], references: [id])
  ratings     ImageRating[]
  associations UserImageAssociation[]

  @@index([wordId])
  @@index([imageType, isActive])
  @@map("word_images")
}

model ImageRating {
  id        String    @id @default(uuid())
  imageId   String
  userId    String
  rating    Int       // 1-5 scale
  feedback  String?
  createdAt DateTime  @default(now())

  image     WordImage @relation(fields: [imageId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([imageId, userId])
  @@index([imageId])
  @@map("image_ratings")
}

model UserImageAssociation {
  id              String              @id @default(uuid())
  userId          String
  wordId          String
  imageId         String
  associationType AssociationType     @default(SYSTEM_SUGGESTED)
  strength        Float               @default(0.5) // 0-1 association strength
  lastReviewed    DateTime?
  reviewCount     Int                 @default(0)
  isPreferred     Boolean             @default(false)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  word            Word                @relation(fields: [wordId], references: [id], onDelete: Cascade)
  image           WordImage           @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@unique([userId, wordId, imageId])
  @@index([userId, wordId])
  @@index([associationType])
  @@map("user_image_associations")
}

model VisualMemoryPalace {
  id          String                    @id @default(uuid())
  userId      String
  name        String
  description String?
  theme       PalaceTheme               @default(HOUSE)
  isActive    Boolean                   @default(true)
  layout      Json                      // Palace layout configuration
  createdAt   DateTime                  @default(now())
  updatedAt   DateTime                  @updatedAt

  user        User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  rooms       MemoryPalaceRoom[]

  @@index([userId])
  @@map("visual_memory_palaces")
}

model MemoryPalaceRoom {
  id          String              @id @default(uuid())
  palaceId    String
  name        String
  position    Json                // Room position in palace
  theme       String?
  capacity    Int                 @default(10)
  wordCount   Int                 @default(0)
  createdAt   DateTime            @default(now())

  palace      VisualMemoryPalace  @relation(fields: [palaceId], references: [id], onDelete: Cascade)
  placements  WordPlacement[]

  @@index([palaceId])
  @@map("memory_palace_rooms")
}

model WordPlacement {
  id          String            @id @default(uuid())
  roomId      String
  wordId      String
  imageId     String?
  position    Json              // Position within room
  mnemonic    String?           // Custom mnemonic device
  lastVisited DateTime?
  visitCount  Int               @default(0)
  createdAt   DateTime          @default(now())

  room        MemoryPalaceRoom  @relation(fields: [roomId], references: [id], onDelete: Cascade)
  word        Word              @relation(fields: [wordId], references: [id], onDelete: Cascade)
  image       WordImage?        @relation(fields: [imageId], references: [id])

  @@unique([roomId, wordId])
  @@index([roomId])
  @@map("word_placements")
}

model ImageGenerationRequest {
  id          String            @id @default(uuid())
  wordId      String
  userId      String
  prompt      String
  style       ImageStyle        @default(REALISTIC)
  status      GenerationStatus  @default(PENDING)
  resultUrl   String?
  errorMessage String?
  cost        Float?            // Generation cost
  createdAt   DateTime          @default(now())
  completedAt DateTime?

  word        Word              @relation(fields: [wordId], references: [id], onDelete: Cascade)
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@map("image_generation_requests")
}

enum ImageType {
  PHOTO
  ILLUSTRATION
  DIAGRAM
  ICON
  GENERATED_AI
  USER_UPLOADED
}

enum ImageSource {
  STOCK_PHOTO
  GENERATED
  USER_CONTRIBUTED
  CURATED
  WIKIPEDIA
}

enum AssociationType {
  SYSTEM_SUGGESTED
  USER_SELECTED
  AI_RECOMMENDED
  COMMUNITY_VOTED
}

enum PalaceTheme {
  HOUSE
  CASTLE
  SCHOOL
  OFFICE
  NATURE
  CITY
  CUSTOM
}

enum ImageStyle {
  REALISTIC
  CARTOON
  MINIMALIST
  ARTISTIC
  EDUCATIONAL
}

enum GenerationStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}
```

#### User and Word Model Extensions

```prisma
model User {
  // ... existing fields
  imageRatings          ImageRating[]
  imageAssociations     UserImageAssociation[]
  memoryPalaces         VisualMemoryPalace[]
  imageRequests         ImageGenerationRequest[]
  createdImages         WordImage[]
  visualLearningEnabled Boolean                  @default(true)
}

model Word {
  // ... existing fields
  images                WordImage[]
  imageAssociations     UserImageAssociation[]
  placements            WordPlacement[]
  imageRequests         ImageGenerationRequest[]
}
```

### Backend Implementation

#### Services

**Visual Learning Service** (`src/backend/services/visual-learning.service.ts`)

```typescript
export interface VisualLearningService {
	getWordImages(wordId: string, userId?: string): Promise<WordImage[]>;
	generateImageForWord(
		userId: string,
		wordId: string,
		style?: ImageStyle
	): Promise<ImageGenerationRequest>;
	rateImage(
		userId: string,
		imageId: string,
		rating: number,
		feedback?: string
	): Promise<ImageRating>;
	createImageAssociation(
		userId: string,
		wordId: string,
		imageId: string
	): Promise<UserImageAssociation>;
	getImageAssociations(userId: string, wordId: string): Promise<UserImageAssociation[]>;
	updateAssociationStrength(
		userId: string,
		associationId: string,
		strength: number
	): Promise<UserImageAssociation>;
	searchImagesByTags(tags: string[], filters?: ImageFilters): Promise<WordImage[]>;
}

export class VisualLearningServiceImpl implements VisualLearningService {
	constructor(
		private getWordImageRepository: () => WordImageRepository,
		private getImageRatingRepository: () => ImageRatingRepository,
		private getUserImageAssociationRepository: () => UserImageAssociationRepository,
		private getImageGenerationService: () => ImageGenerationService,
		private getImageSearchService: () => ImageSearchService
	) {}

	async getWordImages(wordId: string, userId?: string): Promise<WordImage[]> {
		const images = await this.getWordImageRepository().findByWordId(wordId);

		if (userId) {
			// Get user's associations and preferences
			const associations = await this.getUserImageAssociationRepository().findByUserAndWord(
				userId,
				wordId
			);

			// Sort images by user preference and quality
			return this.sortImagesByPreference(images, associations);
		}

		// Sort by quality and rating for anonymous users
		return images.sort((a, b) => b.quality + b.userRating - (a.quality + a.userRating));
	}

	async generateImageForWord(
		userId: string,
		wordId: string,
		style: ImageStyle = ImageStyle.REALISTIC
	): Promise<ImageGenerationRequest> {
		const word = await this.getWordRepository().findById(wordId);
		if (!word) {
			throw new NotFoundError('Word not found');
		}

		// Check user's generation quota
		const userQuota = await this.checkGenerationQuota(userId);
		if (userQuota.remaining <= 0) {
			throw new ValidationError('Generation quota exceeded');
		}

		// Create generation request
		const request = await this.getImageGenerationRequestRepository().create({
			wordId,
			userId,
			prompt: this.buildImagePrompt(word, style),
			style,
			status: GenerationStatus.PENDING,
		});

		// Queue for processing
		await this.getImageGenerationService().queueGeneration(request);

		return request;
	}

	async rateImage(
		userId: string,
		imageId: string,
		rating: number,
		feedback?: string
	): Promise<ImageRating> {
		if (rating < 1 || rating > 5) {
			throw new ValidationError('Rating must be between 1 and 5');
		}

		const existingRating = await this.getImageRatingRepository().findByImageAndUser(
			imageId,
			userId
		);

		if (existingRating) {
			// Update existing rating
			const updatedRating = await this.getImageRatingRepository().update(existingRating.id, {
				rating,
				feedback,
			});

			await this.updateImageAverageRating(imageId);
			return updatedRating;
		}

		// Create new rating
		const newRating = await this.getImageRatingRepository().create({
			imageId,
			userId,
			rating,
			feedback,
		});

		await this.updateImageAverageRating(imageId);
		return newRating;
	}

	async createImageAssociation(
		userId: string,
		wordId: string,
		imageId: string
	): Promise<UserImageAssociation> {
		const existingAssociation =
			await this.getUserImageAssociationRepository().findByUserWordAndImage(
				userId,
				wordId,
				imageId
			);

		if (existingAssociation) {
			// Update existing association
			return await this.getUserImageAssociationRepository().update(existingAssociation.id, {
				associationType: AssociationType.USER_SELECTED,
				strength: Math.min(existingAssociation.strength + 0.1, 1.0),
				lastReviewed: new Date(),
			});
		}

		// Create new association
		return await this.getUserImageAssociationRepository().create({
			userId,
			wordId,
			imageId,
			associationType: AssociationType.USER_SELECTED,
			strength: 0.7, // Higher initial strength for user-selected
		});
	}

	private buildImagePrompt(word: Word, style: ImageStyle): string {
		const basePrompt = `A clear, educational image representing the word "${word.term}"`;

		const stylePrompts = {
			[ImageStyle.REALISTIC]: 'photorealistic, high quality, professional photography',
			[ImageStyle.CARTOON]: 'cartoon style, colorful, friendly, educational illustration',
			[ImageStyle.MINIMALIST]: 'minimalist design, clean lines, simple, modern',
			[ImageStyle.ARTISTIC]: 'artistic interpretation, creative, expressive',
			[ImageStyle.EDUCATIONAL]: 'educational diagram, clear labels, instructional',
		};

		return `${basePrompt}, ${stylePrompts[style]}, suitable for language learning`;
	}

	private async updateImageAverageRating(imageId: string): Promise<void> {
		const ratings = await this.getImageRatingRepository().findByImageId(imageId);

		if (ratings.length === 0) return;

		const averageRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;

		await this.getWordImageRepository().update(imageId, {
			userRating: averageRating,
			ratingCount: ratings.length,
		});
	}

	private sortImagesByPreference(
		images: WordImage[],
		associations: UserImageAssociation[]
	): WordImage[] {
		const associationMap = new Map(associations.map((a) => [a.imageId, a]));

		return images.sort((a, b) => {
			const aAssoc = associationMap.get(a.id);
			const bAssoc = associationMap.get(b.id);

			// Prioritize user's preferred images
			if (aAssoc?.isPreferred && !bAssoc?.isPreferred) return -1;
			if (!aAssoc?.isPreferred && bAssoc?.isPreferred) return 1;

			// Then by association strength
			const aStrength = aAssoc?.strength || 0;
			const bStrength = bAssoc?.strength || 0;
			if (aStrength !== bStrength) return bStrength - aStrength;

			// Finally by quality and rating
			return b.quality + b.userRating - (a.quality + a.userRating);
		});
	}
}
```

**Memory Palace Service** (`src/backend/services/memory-palace.service.ts`)

```typescript
export interface MemoryPalaceService {
	createPalace(userId: string, palaceData: CreatePalaceDto): Promise<VisualMemoryPalace>;
	getPalaces(userId: string): Promise<VisualMemoryPalace[]>;
	addWordToPalace(
		userId: string,
		palaceId: string,
		wordId: string,
		roomId: string,
		position: Position
	): Promise<WordPlacement>;
	removeWordFromPalace(userId: string, placementId: string): Promise<void>;
	visitPalace(userId: string, palaceId: string): Promise<VisualMemoryPalace>;
	getPalaceProgress(userId: string, palaceId: string): Promise<PalaceProgress>;
}

export class MemoryPalaceServiceImpl implements MemoryPalaceService {
	async createPalace(userId: string, palaceData: CreatePalaceDto): Promise<VisualMemoryPalace> {
		const palace = await this.getVisualMemoryPalaceRepository().create({
			...palaceData,
			userId,
			layout: this.generatePalaceLayout(palaceData.theme),
		});

		// Create default rooms
		await this.createDefaultRooms(palace.id, palaceData.theme);

		return palace;
	}

	async addWordToPalace(
		userId: string,
		palaceId: string,
		wordId: string,
		roomId: string,
		position: Position
	): Promise<WordPlacement> {
		// Verify palace ownership
		const palace = await this.getVisualMemoryPalaceRepository().findById(palaceId);
		if (!palace || palace.userId !== userId) {
			throw new UnauthorizedError('Palace not found or access denied');
		}

		// Check room capacity
		const room = await this.getMemoryPalaceRoomRepository().findById(roomId);
		if (!room || room.wordCount >= room.capacity) {
			throw new ValidationError('Room is at capacity');
		}

		// Get best image for the word
		const images = await this.getVisualLearningService().getWordImages(wordId, userId);
		const bestImage = images[0]; // Already sorted by preference

		const placement = await this.getWordPlacementRepository().create({
			roomId,
			wordId,
			imageId: bestImage?.id,
			position,
		});

		// Update room word count
		await this.getMemoryPalaceRoomRepository().update(roomId, {
			wordCount: room.wordCount + 1,
		});

		return placement;
	}

	private generatePalaceLayout(theme: PalaceTheme): any {
		const layouts = {
			[PalaceTheme.HOUSE]: {
				rooms: ['Living Room', 'Kitchen', 'Bedroom', 'Bathroom', 'Study'],
				connections: {
					/* room connections */
				},
				style: 'cozy_home',
			},
			[PalaceTheme.CASTLE]: {
				rooms: ['Great Hall', 'Throne Room', 'Library', 'Armory', 'Tower'],
				connections: {
					/* room connections */
				},
				style: 'medieval_castle',
			},
			// ... other themes
		};

		return layouts[theme] || layouts[PalaceTheme.HOUSE];
	}
}
```

### Frontend Implementation

#### Components

**Image Association Component** (`src/components/ui/image-association.tsx`)

```typescript
interface ImageAssociationProps {
  word: Word;
  images: WordImage[];
  userAssociations: UserImageAssociation[];
  onSelectImage: (imageId: string) => void;
  onRateImage: (imageId: string, rating: number) => void;
  onGenerateImage: () => void;
}

export function ImageAssociation({
  word,
  images,
  userAssociations,
  onSelectImage,
  onRateImage,
  onGenerateImage
}: ImageAssociationProps) {
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const associationMap = new Map(userAssociations.map(a => [a.imageId, a]));

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Visual Associations for "{word.term}"</h3>
        <Button onClick={onGenerateImage} size="sm" variant="outline">
          <Sparkles className="w-4 h-4 mr-2" />
          Generate Image
        </Button>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map(image => {
          const association = associationMap.get(image.id);
          const isSelected = association?.isPreferred;

          return (
            <div
              key={image.id}
              className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => {
                setSelectedImageId(image.id);
                onSelectImage(image.id);
              }}
            >
              <div className="aspect-square relative">
                <img
                  src={image.imageUrl}
                  alt={image.description || word.term}
                  className="w-full h-full object-cover"
                />

                {/* Overlay with controls */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity space-x-2">
                    <Button size="sm" variant="secondary">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="secondary">
                      <Heart className={`w-4 h-4 ${isSelected ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>
                  </div>
                </div>

                {/* Association strength indicator */}
                {association && (
                  <div className="absolute top-2 right-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      {Math.round(association.strength * 100)}
                    </div>
                  </div>
                )}

                {/* Image type badge */}
                <div className="absolute bottom-2 left-2">
                  <Badge variant="secondary" className="text-xs">
                    {image.imageType}
                  </Badge>
                </div>
              </div>

              {/* Rating component */}
              <div className="p-2">
                <div className="flex items-center justify-between">
                  <StarRating
                    rating={image.userRating}
                    onRate={(rating) => onRateImage(image.id, rating)}
                    size="sm"
                  />
                  <span className="text-xs text-gray-500">
                    {image.ratingCount} ratings
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Image generation status */}
      <ImageGenerationStatus wordId={word.id} />
    </div>
  );
}
```

**Memory Palace Viewer Component** (`src/components/ui/memory-palace-viewer.tsx`)

```typescript
interface MemoryPalaceViewerProps {
  palace: VisualMemoryPalace;
  rooms: MemoryPalaceRoom[];
  onRoomSelect: (roomId: string) => void;
  onWordPlace: (wordId: string, roomId: string, position: Position) => void;
}

export function MemoryPalaceViewer({
  palace,
  rooms,
  onRoomSelect,
  onWordPlace
}: MemoryPalaceViewerProps) {
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [draggedWord, setDraggedWord] = useState<string | null>(null);

  return (
    <div className="bg-gradient-to-b from-blue-50 to-indigo-100 rounded-lg p-6 min-h-96">
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-900">{palace.name}</h2>
        <p className="text-gray-600">{palace.description}</p>
      </div>

      {/* Palace layout */}
      <div className="relative">
        <svg viewBox="0 0 800 600" className="w-full h-96 border rounded-lg bg-white">
          {/* Render palace layout based on theme */}
          {rooms.map((room, index) => {
            const position = this.getRoomPosition(index, rooms.length);

            return (
              <g key={room.id}>
                {/* Room */}
                <rect
                  x={position.x}
                  y={position.y}
                  width={120}
                  height={80}
                  fill={selectedRoomId === room.id ? '#3B82F6' : '#E5E7EB'}
                  stroke="#6B7280"
                  strokeWidth="2"
                  rx="8"
                  className="cursor-pointer hover:fill-blue-200 transition-colors"
                  onClick={() => {
                    setSelectedRoomId(room.id);
                    onRoomSelect(room.id);
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    if (draggedWord) {
                      onWordPlace(draggedWord, room.id, { x: e.clientX, y: e.clientY });
                      setDraggedWord(null);
                    }
                  }}
                  onDragOver={(e) => e.preventDefault()}
                />

                {/* Room label */}
                <text
                  x={position.x + 60}
                  y={position.y + 45}
                  textAnchor="middle"
                  className="text-sm font-medium fill-gray-700"
                >
                  {room.name}
                </text>

                {/* Word count */}
                <text
                  x={position.x + 60}
                  y={position.y + 65}
                  textAnchor="middle"
                  className="text-xs fill-gray-500"
                >
                  {room.wordCount}/{room.capacity}
                </text>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Room details */}
      {selectedRoomId && (
        <RoomDetailPanel
          roomId={selectedRoomId}
          onWordDragStart={setDraggedWord}
        />
      )}
    </div>
  );
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic image association service
- Image storage and management

### Phase 2 (Weeks 3-4): AI Image Generation

- Integration with AI image generation APIs
- Image quality assessment
- User preference learning

### Phase 3 (Weeks 5-6): Memory Palace System

- Memory palace creation and management
- Word placement and visualization
- Palace navigation interface

### Phase 4 (Weeks 7-8): Advanced Features

- Community image contributions
- Advanced visual learning analytics
- Mobile-optimized interfaces

## Success Metrics

- Image association usage rates
- Memory retention improvement
- User engagement with visual features
- Image quality ratings
- Memory palace completion rates

## Future Enhancements

- AR/VR memory palace experiences
- Collaborative palace building
- Advanced image recognition
- Personalized visual learning paths
- Cross-cultural image adaptations
