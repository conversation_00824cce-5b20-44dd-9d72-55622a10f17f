# Floating Button System

Hệ thống floating button mới được thiết kế để hiển thị các button theo thứ tự ưu tiên và sử dụng flex vertical layout thay vì fix cứng vị trí.

## Tính năng chính

### 1. Priority-based Ordering
- Các button được sắp xếp theo `priority` (số càng cao càng ưu tiên)
- Button có priority cao hơn sẽ hiển thị trước (gần main button hơn)

### 2. Flex Vertical Layout
- Sử dụng flexbox để sắp xếp button theo chiều dọc
- Tự động điều chỉnh direction dựa trên position (bottom/top)
- Hỗ trợ custom gap giữa các button

### 3. Dynamic Management
- Thêm/xóa button động
- Show/hide button riêng lẻ hoặc tất cả
- Update properties của button

### 4. Integration với Floating UI System
- Tích hợp với hệ thống floating UI hiện tại
- Collision detection và auto-positioning
- Animation và transition effects

## Components

### FloatingButtonWrapper
Component cơ bản để wrap các floating button:

```tsx
import { FloatingButtonWrapper } from '@/components/floating-ui';

<FloatingButtonWrapper 
  position="bottom-right" 
  gap={2}
  integrateWithSystem={true}
  systemId="my-system"
>
  {/* Button content */}
</FloatingButtonWrapper>
```

### FloatingButtonManager
Component quản lý toàn bộ hệ thống floating button:

```tsx
import { FloatingButtonManager } from '@/components/floating-ui';

<FloatingButtonManager
  systemId="main-system"
  position="bottom-right"
  gap={3}
  maxVisible={5}
  collisionDetection={true}
/>
```

### FloatingButtonStack
Component để hiển thị stack của button với system integration:

```tsx
import { FloatingButtonStack } from '@/components/floating-ui';

<FloatingButtonStack
  systemId="stack-system"
  position="bottom-left"
  gap={2}
>
  <Button>Manual Button</Button>
</FloatingButtonStack>
```

## Hooks

### useFloatingButtonSystem
Hook chính để quản lý hệ thống floating button:

```tsx
import { useFloatingButtonSystem } from '@/components/floating-ui';

const system = useFloatingButtonSystem('my-system', {
  position: 'bottom-right',
  gap: 2,
  maxVisible: 10,
  collisionDetection: true,
});

// Register button
system.registerButton({
  id: 'settings',
  label: 'Settings',
  icon: <Settings />,
  onClick: () => console.log('Settings'),
  priority: 10,
});

// Control buttons
system.showButton('settings');
system.hideButton('settings');
system.showAllButtons();
system.hideAllButtons();
```

### useFloatingButton
Hook để quản lý button riêng lẻ:

```tsx
import { useFloatingButton } from '@/components/floating-ui';

const settingsButton = useFloatingButton(
  'settings-btn',
  {
    label: 'Settings',
    icon: <Settings />,
    onClick: () => console.log('Settings'),
    priority: 10,
  },
  'my-system'
);

// Control individual button
settingsButton.show();
settingsButton.hide();
settingsButton.toggle();
```

## Updated Float Components

### FloatButton (Updated)
Component FloatButton đã được cập nhật để hỗ trợ priority và sử dụng FloatingButtonWrapper:

```tsx
import { FloatButton } from '@/components/ui';

<FloatButton
  items={[
    {
      label: 'Settings',
      icon: <Settings />,
      onClick: () => console.log('Settings'),
      priority: 10, // Higher priority = closer to main button
    },
    {
      label: 'Info',
      icon: <Info />,
      onClick: () => console.log('Info'),
      priority: 5,
    },
  ]}
/>
```

### FloatMenuButton (Updated)
Component FloatMenuButton cũng được cập nhật tương tự:

```tsx
import { FloatMenuButton } from '@/components/ui';

<FloatMenuButton
  items={[
    {
      label: 'Action 1',
      icon: <Icon1 />,
      onClick: () => console.log('Action 1'),
      priority: 10,
    },
    {
      label: 'Action 2',
      icon: <Icon2 />,
      onClick: () => console.log('Action 2'),
      priority: 5,
    },
  ]}
/>
```

## Configuration Options

### Position Options
- `bottom-right`: Bottom right corner (default)
- `bottom-left`: Bottom left corner
- `top-right`: Top right corner
- `top-left`: Top left corner

### FloatingButtonSystemOptions
```tsx
interface FloatingButtonSystemOptions {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  gap?: number; // Gap between buttons (in rem units * 0.25)
  maxVisible?: number; // Maximum number of visible buttons
  autoHide?: boolean; // Auto-hide when no buttons visible
  collisionDetection?: boolean; // Enable collision detection
}
```

### FloatingButtonItem
```tsx
interface FloatingButtonItem {
  id: string; // Unique identifier
  label: string; // Accessibility label
  icon: React.ReactNode; // Button icon
  onClick: () => void; // Click handler
  priority?: number; // Display priority (higher = first)
  visible?: boolean; // Visibility state
  disabled?: boolean; // Disabled state
  className?: string; // Additional CSS classes
}
```

## Migration Guide

### From Old FloatButton
```tsx
// Old way
<div className="fixed bottom-6 right-6 z-50 flex flex-col items-end gap-2">
  <Button>Button 1</Button>
  <Button>Button 2</Button>
</div>

// New way
<FloatingButtonWrapper position="bottom-right" gap={2}>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
</FloatingButtonWrapper>

// Or with system integration
<FloatingButtonManager systemId="my-system" />
```

### Priority-based Ordering
```tsx
// Old way - manual ordering
const items = [item1, item2, item3];

// New way - priority-based
const items = [
  { ...item1, priority: 10 }, // Will show first
  { ...item2, priority: 5 },  // Will show second
  { ...item3, priority: 1 },  // Will show last
];
```

## Best Practices

1. **Use Priority Wisely**: Assign meaningful priorities (e.g., 10 for critical, 5 for normal, 1 for optional)
2. **Limit Visible Buttons**: Use `maxVisible` to prevent UI clutter
3. **Consistent Positioning**: Use the same position across your app for consistency
4. **Accessibility**: Always provide meaningful `label` for screen readers
5. **System Integration**: Use `FloatingButtonManager` for complex scenarios with multiple buttons
