# Enterprise Features - Development Plan

## Overview

Implement comprehensive enterprise features including assignment systems, grade book integration, administrative dashboards, bulk user management, advanced analytics, and institutional reporting to support educational institutions and corporate training programs.

## Technical Architecture

### Core Components

#### 1. Enterprise Management System

- **Location**: `src/backend/services/enterprise.service.ts`
- **Purpose**: Core enterprise functionality and organization management
- **Features**:
    - Organization hierarchy
    - Role-based access control
    - Bulk user management
    - License management

#### 2. Assignment System

- **Location**: `src/backend/services/assignment.service.ts`
- **Purpose**: Create, distribute, and manage assignments
- **Features**:
    - Assignment creation and templates
    - Distribution and scheduling
    - Progress tracking
    - Automated grading

#### 3. Grade Book Integration

- **Location**: `src/backend/services/gradebook.service.ts`
- **Purpose**: Comprehensive grading and assessment management
- **Features**:
    - Grade calculation and weighting
    - Rubric-based assessment
    - Progress reporting
    - Export capabilities

## Database Schema Extensions

### New Tables

```prisma
model Organization {
  id              String   @id @default(uuid())
  name            String
  type            String   // 'school', 'university', 'corporate', 'training_center'
  domain          String?  // Email domain for auto-enrollment
  settings        Json     // Organization-specific settings
  license_type    String   // 'basic', 'premium', 'enterprise'
  max_users       Int      @default(100)
  current_users   Int      @default(0)
  subscription_end DateTime?
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@index([domain])
  @@index([license_type])
  @@index([is_active])
}

model OrganizationMember {
  id              String   @id @default(uuid())
  organization_id String
  user_id         String
  role            String   // 'admin', 'teacher', 'student', 'manager'
  department      String?
  employee_id     String?
  joined_at       DateTime @default(now())
  is_active       Boolean  @default(true)
  permissions     Json     // Role-specific permissions

  organization Organization @relation(fields: [organization_id], references: [id])
  user User @relation(fields: [user_id], references: [id])

  @@unique([organization_id, user_id])
  @@index([organization_id])
  @@index([user_id])
  @@index([role])
}

model Class {
  id              String   @id @default(uuid())
  organization_id String
  name            String
  description     String?
  subject         String   // 'english', 'vocabulary', 'grammar'
  level           String   // 'beginner', 'intermediate', 'advanced'
  teacher_id      String
  class_code      String   @unique
  max_students    Int      @default(30)
  current_students Int     @default(0)
  start_date      DateTime
  end_date        DateTime?
  is_active       Boolean  @default(true)
  settings        Json     // Class-specific settings
  created_at      DateTime @default(now())

  organization Organization @relation(fields: [organization_id], references: [id])
  teacher User @relation(fields: [teacher_id], references: [id])

  @@index([organization_id])
  @@index([teacher_id])
  @@index([class_code])
  @@index([is_active])
}

model ClassEnrollment {
  id          String   @id @default(uuid())
  class_id    String
  student_id  String
  enrolled_at DateTime @default(now())
  status      String   @default("active") // 'active', 'dropped', 'completed'
  final_grade Float?

  class Class @relation(fields: [class_id], references: [id])
  student User @relation(fields: [student_id], references: [id])

  @@unique([class_id, student_id])
  @@index([class_id])
  @@index([student_id])
  @@index([status])
}

model Assignment {
  id              String   @id @default(uuid())
  class_id        String
  teacher_id      String
  title           String
  description     String
  assignment_type String   // 'vocabulary', 'reading', 'practice', 'quiz'
  content_data    Json     // Assignment content and requirements
  due_date        DateTime
  points_possible Int      @default(100)
  attempts_allowed Int     @default(1)
  time_limit      Int?     // Time limit in minutes
  is_published    Boolean  @default(false)
  auto_grade      Boolean  @default(true)
  rubric_id       String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  class Class @relation(fields: [class_id], references: [id])
  teacher User @relation(fields: [teacher_id], references: [id])
  rubric GradingRubric? @relation(fields: [rubric_id], references: [id])

  @@index([class_id])
  @@index([teacher_id])
  @@index([due_date])
  @@index([is_published])
}

model AssignmentSubmission {
  id              String   @id @default(uuid())
  assignment_id   String
  student_id      String
  attempt_number  Int      @default(1)
  submission_data Json     // Student's answers/responses
  submitted_at    DateTime @default(now())
  graded_at       DateTime?
  score           Float?   // Raw score
  grade           Float?   // Final grade (after curve, etc.)
  feedback        String?
  is_late         Boolean  @default(false)
  time_taken      Int?     // Time taken in minutes

  assignment Assignment @relation(fields: [assignment_id], references: [id])
  student User @relation(fields: [student_id], references: [id])

  @@unique([assignment_id, student_id, attempt_number])
  @@index([assignment_id])
  @@index([student_id])
  @@index([submitted_at])
}

model GradingRubric {
  id              String   @id @default(uuid())
  organization_id String
  name            String
  description     String
  criteria        Json     // Grading criteria and point values
  total_points    Int      @default(100)
  created_by      String
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())

  organization Organization @relation(fields: [organization_id], references: [id])
  creator User @relation(fields: [created_by], references: [id])

  @@index([organization_id])
  @@index([created_by])
  @@index([is_active])
}

model GradeCategory {
  id              String   @id @default(uuid())
  class_id        String
  name            String   // 'Homework', 'Quizzes', 'Exams', 'Participation'
  weight          Float    // Percentage weight in final grade
  drop_lowest     Int      @default(0) // Number of lowest scores to drop
  is_active       Boolean  @default(true)

  class Class @relation(fields: [class_id], references: [id])

  @@index([class_id])
}

model StudentGrade {
  id              String   @id @default(uuid())
  student_id      String
  class_id        String
  category_id     String?
  assignment_id   String?
  grade_value     Float
  points_earned   Float
  points_possible Float
  grade_date      DateTime @default(now())
  is_excused      Boolean  @default(false)
  notes           String?

  student User @relation(fields: [student_id], references: [id])
  class Class @relation(fields: [class_id], references: [id])
  category GradeCategory? @relation(fields: [category_id], references: [id])
  assignment Assignment? @relation(fields: [assignment_id], references: [id])

  @@index([student_id])
  @@index([class_id])
  @@index([category_id])
  @@index([assignment_id])
  @@index([grade_date])
}

model EnterpriseReport {
  id              String   @id @default(uuid())
  organization_id String
  report_type     String   // 'progress', 'usage', 'performance', 'engagement'
  title           String
  description     String
  parameters      Json     // Report parameters and filters
  generated_by    String
  generated_at    DateTime @default(now())
  file_url        String?  // URL to generated report file
  is_scheduled    Boolean  @default(false)
  schedule_config Json?    // Scheduling configuration

  organization Organization @relation(fields: [organization_id], references: [id])
  generator User @relation(fields: [generated_by], references: [id])

  @@index([organization_id])
  @@index([report_type])
  @@index([generated_by])
  @@index([generated_at])
}

model BulkOperation {
  id              String   @id @default(uuid())
  organization_id String
  operation_type  String   // 'user_import', 'grade_export', 'assignment_distribute'
  initiated_by    String
  status          String   @default("pending") // 'pending', 'processing', 'completed', 'failed'
  total_items     Int      @default(0)
  processed_items Int      @default(0)
  failed_items    Int      @default(0)
  error_log       Json?    // Errors encountered during operation
  result_data     Json?    // Operation results
  started_at      DateTime @default(now())
  completed_at    DateTime?

  organization Organization @relation(fields: [organization_id], references: [id])
  initiator User @relation(fields: [initiated_by], references: [id])

  @@index([organization_id])
  @@index([operation_type])
  @@index([status])
  @@index([started_at])
}

model LearningAnalytics {
  id              String   @id @default(uuid())
  organization_id String
  user_id         String
  class_id        String?
  metric_type     String   // 'engagement', 'progress', 'performance', 'time_spent'
  metric_value    Float
  measurement_date DateTime @default(now())
  context_data    Json?    // Additional context information

  organization Organization @relation(fields: [organization_id], references: [id])
  user User @relation(fields: [user_id], references: [id])
  class Class? @relation(fields: [class_id], references: [id])

  @@index([organization_id])
  @@index([user_id])
  @@index([class_id])
  @@index([metric_type])
  @@index([measurement_date])
}
```

## Implementation Plan

### Phase 1: Organization Management (Week 1-2)

#### 1.1 Enterprise Core System

```typescript
// src/backend/services/enterprise.service.ts
export interface EnterpriseService {
	createOrganization(orgData: CreateOrganizationData): Promise<Organization>;
	addMemberToOrganization(
		orgId: string,
		userId: string,
		role: OrganizationRole
	): Promise<OrganizationMember>;
	bulkImportUsers(
		orgId: string,
		userData: BulkUserData[],
		initiatorId: string
	): Promise<BulkOperation>;
	manageSubscription(orgId: string, subscriptionData: SubscriptionData): Promise<Organization>;
}

enum OrganizationRole {
	ADMIN = 'admin',
	TEACHER = 'teacher',
	STUDENT = 'student',
	MANAGER = 'manager',
}

class OrganizationManager {
	async createOrganization(orgData: CreateOrganizationData): Promise<Organization> {
		const organization = await this.prisma.organization.create({
			data: {
				name: orgData.name,
				type: orgData.type,
				domain: orgData.domain,
				settings: orgData.settings || {},
				license_type: orgData.licenseType,
				max_users: this.getLicenseUserLimit(orgData.licenseType),
			},
		});

		// Create default grade categories
		await this.createDefaultGradeCategories(organization.id);

		// Set up default permissions
		await this.setupDefaultPermissions(organization.id);

		return organization;
	}

	async bulkImportUsers(
		orgId: string,
		userData: BulkUserData[],
		initiatorId: string
	): Promise<BulkOperation> {
		const operation = await this.prisma.bulkOperation.create({
			data: {
				organization_id: orgId,
				operation_type: 'user_import',
				initiated_by: initiatorId,
				total_items: userData.length,
				status: 'pending',
			},
		});

		// Process import asynchronously
		this.processUserImport(operation.id, userData);

		return operation;
	}

	private async processUserImport(operationId: string, userData: BulkUserData[]): Promise<void> {
		await this.updateOperationStatus(operationId, 'processing');

		let processed = 0;
		let failed = 0;
		const errors: any[] = [];

		for (const user of userData) {
			try {
				await this.createUserAccount(user);
				processed++;
			} catch (error) {
				failed++;
				errors.push({
					userData: user,
					error: error.message,
				});
			}

			// Update progress
			await this.updateOperationProgress(operationId, processed, failed);
		}

		await this.completeOperation(operationId, {
			processed_items: processed,
			failed_items: failed,
			error_log: errors,
		});
	}
}
```

#### 1.2 Role-Based Access Control

```typescript
class PermissionManager {
	private permissions = {
		[OrganizationRole.ADMIN]: [
			'manage_organization',
			'manage_users',
			'view_all_classes',
			'manage_classes',
			'view_analytics',
			'export_data',
			'manage_billing',
		],
		[OrganizationRole.TEACHER]: [
			'create_classes',
			'manage_own_classes',
			'create_assignments',
			'grade_assignments',
			'view_student_progress',
			'export_grades',
		],
		[OrganizationRole.STUDENT]: [
			'view_own_classes',
			'submit_assignments',
			'view_own_grades',
			'view_own_progress',
		],
		[OrganizationRole.MANAGER]: [
			'view_department_analytics',
			'view_department_classes',
			'export_department_data',
		],
	};

	hasPermission(userRole: OrganizationRole, permission: string): boolean {
		return this.permissions[userRole]?.includes(permission) || false;
	}

	async checkUserPermission(userId: string, orgId: string, permission: string): Promise<boolean> {
		const member = await this.getOrganizationMember(userId, orgId);
		if (!member) return false;

		return this.hasPermission(member.role as OrganizationRole, permission);
	}
}
```

### Phase 2: Assignment System (Week 3-4)

#### 2.1 Assignment Creation and Management

```typescript
// src/backend/services/assignment.service.ts
export interface AssignmentService {
	createAssignment(
		teacherId: string,
		classId: string,
		assignmentData: CreateAssignmentData
	): Promise<Assignment>;

	distributeAssignment(assignmentId: string, studentIds: string[]): Promise<void>;

	submitAssignment(
		assignmentId: string,
		studentId: string,
		submissionData: SubmissionData
	): Promise<AssignmentSubmission>;

	gradeSubmission(submissionId: string, gradeData: GradeData): Promise<AssignmentSubmission>;
}

class AssignmentManager {
	async createAssignment(
		teacherId: string,
		classId: string,
		assignmentData: CreateAssignmentData
	): Promise<Assignment> {
		// Validate teacher permissions
		await this.validateTeacherPermissions(teacherId, classId);

		const assignment = await this.prisma.assignment.create({
			data: {
				class_id: classId,
				teacher_id: teacherId,
				title: assignmentData.title,
				description: assignmentData.description,
				assignment_type: assignmentData.type,
				content_data: assignmentData.content,
				due_date: assignmentData.dueDate,
				points_possible: assignmentData.pointsPossible,
				attempts_allowed: assignmentData.attemptsAllowed,
				time_limit: assignmentData.timeLimit,
				auto_grade: assignmentData.autoGrade,
				rubric_id: assignmentData.rubricId,
			},
		});

		// Create assignment template for reuse
		if (assignmentData.saveAsTemplate) {
			await this.createAssignmentTemplate(assignment, teacherId);
		}

		return assignment;
	}

	async autoGradeSubmission(
		submission: AssignmentSubmission,
		assignment: Assignment
	): Promise<number> {
		const submissionData = submission.submission_data as any;
		const assignmentContent = assignment.content_data as any;

		let totalScore = 0;
		let maxScore = 0;

		// Grade based on assignment type
		switch (assignment.assignment_type) {
			case 'vocabulary':
				return this.gradeVocabularyAssignment(submissionData, assignmentContent);
			case 'quiz':
				return this.gradeQuizAssignment(submissionData, assignmentContent);
			case 'practice':
				return this.gradePracticeAssignment(submissionData, assignmentContent);
			default:
				return 0;
		}
	}

	private gradeVocabularyAssignment(submission: any, content: any): number {
		const answers = submission.answers;
		const correctAnswers = content.correctAnswers;

		let correct = 0;
		for (const [questionId, answer] of Object.entries(answers)) {
			if (correctAnswers[questionId] === answer) {
				correct++;
			}
		}

		return (correct / Object.keys(correctAnswers).length) * 100;
	}
}
```

#### 2.2 Assignment Templates and Reusability

```typescript
class AssignmentTemplateManager {
	async createTemplate(assignment: Assignment, teacherId: string): Promise<AssignmentTemplate> {
		return await this.prisma.assignmentTemplate.create({
			data: {
				name: `${assignment.title} Template`,
				description: assignment.description,
				assignment_type: assignment.assignment_type,
				content_template: assignment.content_data,
				default_points: assignment.points_possible,
				default_attempts: assignment.attempts_allowed,
				default_time_limit: assignment.time_limit,
				created_by: teacherId,
				is_public: false,
			},
		});
	}

	async generateAssignmentFromTemplate(
		templateId: string,
		customizations: TemplateCustomizations
	): Promise<CreateAssignmentData> {
		const template = await this.getTemplate(templateId);

		return {
			title: customizations.title || template.name,
			description: customizations.description || template.description,
			type: template.assignment_type,
			content: this.customizeContent(template.content_template, customizations),
			pointsPossible: customizations.points || template.default_points,
			attemptsAllowed: customizations.attempts || template.default_attempts,
			timeLimit: customizations.timeLimit || template.default_time_limit,
			dueDate: customizations.dueDate,
		};
	}
}
```

### Phase 3: Grade Book Integration (Week 5-6)

#### 3.1 Comprehensive Grading System

```typescript
// src/backend/services/gradebook.service.ts
export interface GradebookService {
	calculateStudentGrade(studentId: string, classId: string): Promise<StudentGradeReport>;

	createGradeCategory(classId: string, categoryData: GradeCategoryData): Promise<GradeCategory>;

	exportGrades(classId: string, format: ExportFormat): Promise<string>; // Returns file URL

	generateProgressReport(
		studentId: string,
		classId: string,
		timeframe: TimeFrame
	): Promise<ProgressReport>;
}

class GradebookManager {
	async calculateStudentGrade(studentId: string, classId: string): Promise<StudentGradeReport> {
		const categories = await this.getClassGradeCategories(classId);
		const studentGrades = await this.getStudentGrades(studentId, classId);

		let totalWeightedScore = 0;
		let totalWeight = 0;
		const categoryBreakdown: CategoryGrade[] = [];

		for (const category of categories) {
			const categoryGrades = studentGrades.filter(
				(grade) => grade.category_id === category.id
			);

			if (categoryGrades.length === 0) continue;

			// Apply drop lowest policy
			const gradesToUse = this.applyDropLowest(categoryGrades, category.drop_lowest);

			// Calculate category average
			const categoryAverage = this.calculateCategoryAverage(gradesToUse);

			categoryBreakdown.push({
				categoryId: category.id,
				categoryName: category.name,
				weight: category.weight,
				average: categoryAverage,
				gradeCount: gradesToUse.length,
			});

			totalWeightedScore += categoryAverage * category.weight;
			totalWeight += category.weight;
		}

		const finalGrade = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

		return {
			studentId,
			classId,
			finalGrade,
			letterGrade: this.calculateLetterGrade(finalGrade),
			categoryBreakdown,
			totalAssignments: studentGrades.length,
			lastUpdated: new Date(),
		};
	}

	async exportGrades(classId: string, format: ExportFormat): Promise<string> {
		const classData = await this.getClassWithStudents(classId);
		const gradeData = await this.getAllClassGrades(classId);

		switch (format) {
			case 'csv':
				return await this.exportToCSV(classData, gradeData);
			case 'excel':
				return await this.exportToExcel(classData, gradeData);
			case 'pdf':
				return await this.exportToPDF(classData, gradeData);
			default:
				throw new Error('Unsupported export format');
		}
	}

	private async exportToCSV(
		classData: ClassWithStudents,
		gradeData: GradeData[]
	): Promise<string> {
		const csv = this.csvBuilder
			.addHeader([
				'Student Name',
				'Student ID',
				'Assignment',
				'Grade',
				'Points Earned',
				'Points Possible',
			])
			.addRows(
				gradeData.map((grade) => [
					grade.studentName,
					grade.studentId,
					grade.assignmentName,
					grade.grade,
					grade.pointsEarned,
					grade.pointsPossible,
				])
			)
			.build();

		const fileName = `grades_${classData.name}_${Date.now()}.csv`;
		const fileUrl = await this.fileStorage.upload(fileName, csv);

		return fileUrl;
	}
}
```

### Phase 4: Analytics and Reporting (Week 7-8)

#### 4.1 Enterprise Analytics Dashboard

```typescript
class EnterpriseAnalytics {
	async generateOrganizationReport(
		orgId: string,
		reportType: ReportType,
		parameters: ReportParameters
	): Promise<EnterpriseReport> {
		const reportData = await this.collectReportData(orgId, reportType, parameters);
		const processedData = await this.processReportData(reportData, reportType);

		const report = await this.prisma.enterpriseReport.create({
			data: {
				organization_id: orgId,
				report_type: reportType,
				title: this.generateReportTitle(reportType, parameters),
				description: this.generateReportDescription(reportType, parameters),
				parameters: parameters,
				generated_by: parameters.generatedBy,
			},
		});

		// Generate report file
		const fileUrl = await this.generateReportFile(report.id, processedData);

		await this.prisma.enterpriseReport.update({
			where: { id: report.id },
			data: { file_url: fileUrl },
		});

		return report;
	}

	async trackLearningAnalytics(
		orgId: string,
		userId: string,
		metrics: LearningMetrics
	): Promise<void> {
		const analyticsEntries = Object.entries(metrics).map(([metricType, value]) => ({
			organization_id: orgId,
			user_id: userId,
			metric_type: metricType,
			metric_value: value,
			measurement_date: new Date(),
		}));

		await this.prisma.learningAnalytics.createMany({
			data: analyticsEntries,
		});

		// Update real-time dashboards
		await this.updateRealTimeDashboards(orgId, userId, metrics);
	}

	async generateUsageReport(orgId: string, timeframe: TimeFrame): Promise<UsageReport> {
		const analytics = await this.getAnalyticsData(orgId, timeframe);

		return {
			totalUsers: analytics.uniqueUsers,
			activeUsers: analytics.activeUsers,
			totalSessions: analytics.totalSessions,
			averageSessionDuration: analytics.avgSessionDuration,
			mostUsedFeatures: analytics.featureUsage,
			engagementTrends: analytics.engagementTrends,
			performanceMetrics: analytics.performanceMetrics,
		};
	}
}
```

## Frontend Integration

### Enterprise Dashboard

```typescript
// src/components/ui/enterprise-dashboard.tsx
export function EnterpriseDashboard({ organizationId }: { organizationId: string }) {
  const { organization, analytics, classes } = useEnterprise(organizationId);
  const { permissions } = usePermissions();

  return (
    <div className="enterprise-dashboard">
      <div className="dashboard-header">
        <h1>{organization.name} Dashboard</h1>
        <OrganizationStats organization={organization} />
      </div>

      <div className="dashboard-grid">
        {permissions.includes('view_analytics') && (
          <AnalyticsOverview analytics={analytics} />
        )}

        {permissions.includes('manage_classes') && (
          <ClassManagement classes={classes} />
        )}

        {permissions.includes('manage_users') && (
          <UserManagement organizationId={organizationId} />
        )}

        <ReportsSection organizationId={organizationId} />
      </div>
    </div>
  );
}
```

### Assignment Management Interface

```typescript
// src/components/ui/assignment-manager.tsx
export function AssignmentManager({ classId }: { classId: string }) {
  const { assignments, submissions } = useAssignments(classId);
  const { createAssignment, gradeSubmission } = useAssignmentActions();

  return (
    <div className="assignment-manager">
      <div className="manager-header">
        <h2>Assignments</h2>
        <CreateAssignmentButton onClick={() => createAssignment(classId)} />
      </div>

      <div className="assignments-grid">
        {assignments.map(assignment => (
          <AssignmentCard
            key={assignment.id}
            assignment={assignment}
            submissions={submissions.filter(s => s.assignment_id === assignment.id)}
            onGrade={gradeSubmission}
          />
        ))}
      </div>
    </div>
  );
}
```

## Success Criteria

### Administrative Efficiency

- 60% reduction in administrative overhead
- 80% faster grade processing and reporting
- 90% automation of routine tasks
- 50% improvement in data accuracy

### Educational Outcomes

- 25% improvement in assignment completion rates
- 30% better student engagement tracking
- 40% more detailed progress insights
- 20% improvement in learning outcomes measurement

### System Adoption

- 85%+ teacher adoption rate
- 90%+ student engagement with assignments
- 75%+ administrator satisfaction
- 95%+ system uptime and reliability

## Timeline

- **Week 1-2**: Organization management and role-based access control
- **Week 3-4**: Assignment system and distribution mechanisms
- **Week 5-6**: Grade book integration and calculation engine
- **Week 7-8**: Analytics, reporting, and bulk operations
- **Week 9**: Testing, security audits, and performance optimization
- **Week 10**: Deployment, training materials, and support documentation
