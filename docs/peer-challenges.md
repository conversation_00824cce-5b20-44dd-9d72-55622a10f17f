# Peer Challenges Development Plan

## Overview

Implement a comprehensive peer-to-peer challenge system that enables users to compete directly with friends, create custom challenges, and participate in community-wide competitions to enhance motivation and engagement.

## Technical Architecture

### Database Schema Extensions

#### Challenge Models

```prisma
model PeerChallenge {
  id              String              @id @default(uuid())
  title           String
  description     String?
  type            ChallengeType
  difficulty      Difficulty          @default(BEGINNER)
  duration        Int                 // in hours
  maxParticipants Int?
  isPublic        Boolean             @default(false)
  settings        Json                // Challenge-specific settings
  status          ChallengeStatus     @default(PENDING)
  startDate       DateTime?
  endDate         DateTime?
  createdBy       String
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  creator         User                @relation("ChallengeCreator", fields: [createdBy], references: [id])
  participants    ChallengeParticipant[]
  invitations     ChallengeInvitation[]
  activities      ChallengeActivity[]

  @@index([type, status])
  @@index([isPublic, status])
  @@index([createdBy])
  @@map("peer_challenges")
}

model ChallengeParticipant {
  id            String         @id @default(uuid())
  challengeId   String
  userId        String
  status        ParticipantStatus @default(INVITED)
  score         Int            @default(0)
  progress      Json?          // Progress tracking data
  rank          Int?
  completedAt   DateTime?
  joinedAt      DateTime?
  lastActivity  DateTime?

  challenge     PeerChallenge  @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([challengeId, userId])
  @@index([challengeId, score])
  @@index([userId])
  @@map("challenge_participants")
}

model ChallengeInvitation {
  id          String            @id @default(uuid())
  challengeId String
  inviterId   String
  inviteeId   String
  status      InvitationStatus  @default(PENDING)
  message     String?
  sentAt      DateTime          @default(now())
  respondedAt DateTime?

  challenge   PeerChallenge     @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  inviter     User              @relation("SentInvitations", fields: [inviterId], references: [id])
  invitee     User              @relation("ReceivedInvitations", fields: [inviteeId], references: [id])

  @@unique([challengeId, inviteeId])
  @@index([inviteeId, status])
  @@map("challenge_invitations")
}

model ChallengeActivity {
  id          String        @id @default(uuid())
  challengeId String
  userId      String
  activityType ActivityType
  data        Json          // Activity-specific data
  points      Int           @default(0)
  timestamp   DateTime      @default(now())

  challenge   PeerChallenge @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([challengeId, userId])
  @@index([challengeId, timestamp])
  @@map("challenge_activities")
}

model ChallengeTemplate {
  id          String        @id @default(uuid())
  name        String
  description String
  type        ChallengeType
  difficulty  Difficulty
  duration    Int           // in hours
  settings    Json          // Template settings
  isOfficial  Boolean       @default(false)
  usageCount  Int           @default(0)
  createdBy   String?
  createdAt   DateTime      @default(now())

  creator     User?         @relation(fields: [createdBy], references: [id])

  @@index([type, difficulty])
  @@index([isOfficial])
  @@map("challenge_templates")
}

enum ChallengeType {
  WORD_RACE
  STREAK_BATTLE
  ACCURACY_CONTEST
  TIME_TRIAL
  VOCABULARY_DUEL
  PARAGRAPH_MARATHON
  CUSTOM
}

enum ChallengeStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum ParticipantStatus {
  INVITED
  ACCEPTED
  DECLINED
  ACTIVE
  COMPLETED
  DROPPED_OUT
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

enum ActivityType {
  WORD_LEARNED
  PARAGRAPH_COMPLETED
  STREAK_EXTENDED
  QUIZ_ANSWERED
  TIME_SPENT
  ACCURACY_ACHIEVED
}
```

#### User Model Extensions

```prisma
model User {
  // ... existing fields
  createdChallenges     PeerChallenge[]       @relation("ChallengeCreator")
  challengeParticipations ChallengeParticipant[]
  sentInvitations       ChallengeInvitation[] @relation("SentInvitations")
  receivedInvitations   ChallengeInvitation[] @relation("ReceivedInvitations")
  challengeActivities   ChallengeActivity[]
  challengeTemplates    ChallengeTemplate[]
}
```

### Backend Implementation

#### Services

**Peer Challenge Service** (`src/backend/services/peer-challenge.service.ts`)

```typescript
export interface PeerChallengeService {
	createChallenge(userId: string, challengeData: CreateChallengeDto): Promise<PeerChallenge>;
	inviteUsers(
		userId: string,
		challengeId: string,
		userIds: string[],
		message?: string
	): Promise<ChallengeInvitation[]>;
	respondToInvitation(
		userId: string,
		invitationId: string,
		response: InvitationResponse
	): Promise<ChallengeInvitation>;
	joinPublicChallenge(userId: string, challengeId: string): Promise<ChallengeParticipant>;
	startChallenge(userId: string, challengeId: string): Promise<PeerChallenge>;
	recordActivity(
		userId: string,
		challengeId: string,
		activity: ActivityData
	): Promise<ChallengeActivity>;
	getChallengeLeaderboard(challengeId: string): Promise<ChallengeParticipant[]>;
	getUserChallenges(userId: string, status?: ChallengeStatus): Promise<PeerChallenge[]>;
	getPublicChallenges(filters: ChallengeFilters): Promise<PeerChallenge[]>;
	getChallengeTemplates(): Promise<ChallengeTemplate[]>;
}

export class PeerChallengeServiceImpl implements PeerChallengeService {
	constructor(
		private getPeerChallengeRepository: () => PeerChallengeRepository,
		private getChallengeParticipantRepository: () => ChallengeParticipantRepository,
		private getChallengeInvitationRepository: () => ChallengeInvitationRepository,
		private getChallengeActivityRepository: () => ChallengeActivityRepository,
		private getNotificationService: () => NotificationService
	) {}

	async createChallenge(
		userId: string,
		challengeData: CreateChallengeDto
	): Promise<PeerChallenge> {
		const challenge = await this.getPeerChallengeRepository().create({
			...challengeData,
			createdBy: userId,
			status: ChallengeStatus.PENDING,
		});

		// Auto-add creator as participant
		await this.getChallengeParticipantRepository().create({
			challengeId: challenge.id,
			userId,
			status: ParticipantStatus.ACCEPTED,
			joinedAt: new Date(),
		});

		return challenge;
	}

	async inviteUsers(
		userId: string,
		challengeId: string,
		userIds: string[],
		message?: string
	): Promise<ChallengeInvitation[]> {
		const challenge = await this.getPeerChallengeRepository().findById(challengeId);
		if (!challenge) {
			throw new NotFoundError('Challenge not found');
		}

		if (challenge.createdBy !== userId) {
			throw new UnauthorizedError('Only challenge creator can invite users');
		}

		if (challenge.status !== ChallengeStatus.PENDING) {
			throw new ValidationError('Cannot invite users to an active or completed challenge');
		}

		const invitations: ChallengeInvitation[] = [];

		for (const inviteeId of userIds) {
			// Check if user is already invited or participating
			const existingInvitation =
				await this.getChallengeInvitationRepository().findByChallengeAndUser(
					challengeId,
					inviteeId
				);

			if (existingInvitation) continue;

			const invitation = await this.getChallengeInvitationRepository().create({
				challengeId,
				inviterId: userId,
				inviteeId,
				message,
			});

			invitations.push(invitation);

			// Send notification
			await this.getNotificationService().sendNotification(inviteeId, {
				type: 'challenge_invitation',
				title: 'Challenge Invitation',
				message: `You've been invited to join "${challenge.title}"`,
				data: { challengeId, invitationId: invitation.id },
			});
		}

		return invitations;
	}

	async respondToInvitation(
		userId: string,
		invitationId: string,
		response: InvitationResponse
	): Promise<ChallengeInvitation> {
		const invitation = await this.getChallengeInvitationRepository().findById(invitationId);
		if (!invitation) {
			throw new NotFoundError('Invitation not found');
		}

		if (invitation.inviteeId !== userId) {
			throw new UnauthorizedError('Cannot respond to invitation for another user');
		}

		if (invitation.status !== InvitationStatus.PENDING) {
			throw new ValidationError('Invitation has already been responded to');
		}

		const updatedInvitation = await this.getChallengeInvitationRepository().update(
			invitationId,
			{
				status:
					response === 'accept' ? InvitationStatus.ACCEPTED : InvitationStatus.DECLINED,
				respondedAt: new Date(),
			}
		);

		if (response === 'accept') {
			// Add user as participant
			await this.getChallengeParticipantRepository().create({
				challengeId: invitation.challengeId,
				userId,
				status: ParticipantStatus.ACCEPTED,
				joinedAt: new Date(),
			});

			// Check if challenge can start
			await this.checkAndStartChallenge(invitation.challengeId);
		}

		return updatedInvitation;
	}

	async recordActivity(
		userId: string,
		challengeId: string,
		activity: ActivityData
	): Promise<ChallengeActivity> {
		const participant = await this.getChallengeParticipantRepository().findByChallengeAndUser(
			challengeId,
			userId
		);

		if (!participant || participant.status !== ParticipantStatus.ACTIVE) {
			throw new ValidationError('User is not an active participant in this challenge');
		}

		const challenge = await this.getPeerChallengeRepository().findById(challengeId);
		if (!challenge || challenge.status !== ChallengeStatus.ACTIVE) {
			throw new ValidationError('Challenge is not active');
		}

		const points = this.calculateActivityPoints(activity, challenge.type);

		const challengeActivity = await this.getChallengeActivityRepository().create({
			challengeId,
			userId,
			activityType: activity.type,
			data: activity.data,
			points,
		});

		// Update participant score and progress
		await this.updateParticipantProgress(participant.id, points, activity);

		// Update leaderboard
		await this.updateChallengeLeaderboard(challengeId);

		return challengeActivity;
	}

	private async checkAndStartChallenge(challengeId: string): Promise<void> {
		const challenge = await this.getPeerChallengeRepository().findById(challengeId);
		if (!challenge) return;

		const participantCount =
			await this.getChallengeParticipantRepository().countActiveParticipants(challengeId);

		const minParticipants = this.getMinParticipants(challenge.type);

		if (participantCount >= minParticipants) {
			const startDate = new Date();
			const endDate = new Date(startDate.getTime() + challenge.duration * 60 * 60 * 1000);

			await this.getPeerChallengeRepository().update(challengeId, {
				status: ChallengeStatus.ACTIVE,
				startDate,
				endDate,
			});

			// Update all participants to active status
			await this.getChallengeParticipantRepository().updateParticipantStatus(
				challengeId,
				ParticipantStatus.ACTIVE
			);

			// Notify participants
			await this.notifyParticipants(challengeId, 'Challenge started!', {
				type: 'challenge_started',
				challengeId,
			});
		}
	}

	private calculateActivityPoints(activity: ActivityData, challengeType: ChallengeType): number {
		const basePoints = {
			[ActivityType.WORD_LEARNED]: 10,
			[ActivityType.PARAGRAPH_COMPLETED]: 25,
			[ActivityType.STREAK_EXTENDED]: 5,
			[ActivityType.QUIZ_ANSWERED]: 3,
			[ActivityType.TIME_SPENT]: 1, // per minute
			[ActivityType.ACCURACY_ACHIEVED]: 15,
		};

		let points = basePoints[activity.type] || 0;

		// Apply challenge type multipliers
		switch (challengeType) {
			case ChallengeType.WORD_RACE:
				if (activity.type === ActivityType.WORD_LEARNED) points *= 2;
				break;
			case ChallengeType.ACCURACY_CONTEST:
				if (activity.type === ActivityType.ACCURACY_ACHIEVED) points *= 3;
				break;
			case ChallengeType.TIME_TRIAL:
				if (activity.type === ActivityType.TIME_SPENT) points *= 2;
				break;
		}

		return points;
	}

	private getMinParticipants(challengeType: ChallengeType): number {
		switch (challengeType) {
			case ChallengeType.VOCABULARY_DUEL:
				return 2;
			case ChallengeType.WORD_RACE:
			case ChallengeType.ACCURACY_CONTEST:
				return 2;
			default:
				return 1;
		}
	}
}
```

### Frontend Implementation

#### Components

**Challenge Card Component** (`src/components/ui/challenge-card.tsx`)

```typescript
interface ChallengeCardProps {
	challenge: PeerChallenge;
	userParticipation?: ChallengeParticipant;
	onJoin?: (challengeId: string) => void;
	onAcceptInvitation?: (invitationId: string) => void;
	onDeclineInvitation?: (invitationId: string) => void;
}

export function ChallengeCard({
	challenge,
	userParticipation,
	onJoin,
	onAcceptInvitation,
	onDeclineInvitation,
}: ChallengeCardProps) {
	const participantCount =
		challenge.participants?.filter((p) => p.status === ParticipantStatus.ACTIVE).length || 0;
	const isParticipant = !!userParticipation;
	const timeRemaining = challenge.endDate
		? new Date(challenge.endDate).getTime() - Date.now()
		: null;

	const getStatusColor = (status: ChallengeStatus) => {
		switch (status) {
			case ChallengeStatus.PENDING:
				return 'bg-yellow-100 text-yellow-800';
			case ChallengeStatus.ACTIVE:
				return 'bg-green-100 text-green-800';
			case ChallengeStatus.COMPLETED:
				return 'bg-gray-100 text-gray-800';
			case ChallengeStatus.CANCELLED:
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const getChallengeTypeIcon = (type: ChallengeType) => {
		switch (type) {
			case ChallengeType.WORD_RACE:
				return <Zap className="w-4 h-4" />;
			case ChallengeType.STREAK_BATTLE:
				return <Flame className="w-4 h-4" />;
			case ChallengeType.ACCURACY_CONTEST:
				return <Target className="w-4 h-4" />;
			case ChallengeType.TIME_TRIAL:
				return <Clock className="w-4 h-4" />;
			case ChallengeType.VOCABULARY_DUEL:
				return <Swords className="w-4 h-4" />;
			case ChallengeType.PARAGRAPH_MARATHON:
				return <BookOpen className="w-4 h-4" />;
			default:
				return <Trophy className="w-4 h-4" />;
		}
	};

	return (
		<div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
			<div className="flex items-start justify-between mb-4">
				<div className="flex-1">
					<div className="flex items-center space-x-2 mb-2">
						{getChallengeTypeIcon(challenge.type)}
						<h3 className="text-lg font-semibold text-gray-900">{challenge.title}</h3>
						<Badge className={getStatusColor(challenge.status)}>
							{challenge.status}
						</Badge>
					</div>

					{challenge.description && (
						<p className="text-gray-600 text-sm mb-3">{challenge.description}</p>
					)}

					<div className="flex items-center space-x-4 text-sm text-gray-500">
						<div className="flex items-center space-x-1">
							<Users className="w-4 h-4" />
							<span>
								{participantCount}
								{challenge.maxParticipants ? `/${challenge.maxParticipants}` : ''}
							</span>
						</div>
						<div className="flex items-center space-x-1">
							<Clock className="w-4 h-4" />
							<span>{challenge.duration}h</span>
						</div>
						<div className="flex items-center space-x-1">
							<BarChart className="w-4 h-4" />
							<span>{challenge.difficulty}</span>
						</div>
					</div>
				</div>

				<div className="flex flex-col items-end space-y-2">
					{challenge.isPublic ? (
						<Badge variant="secondary">Public</Badge>
					) : (
						<Badge variant="outline">Private</Badge>
					)}

					{isParticipant ? (
						<Button size="sm" asChild>
							<Link href={`/challenges/${challenge.id}`}>View Challenge</Link>
						</Button>
					) : (
						<Button
							size="sm"
							onClick={() => onJoin?.(challenge.id)}
							disabled={
								challenge.maxParticipants
									? participantCount >= challenge.maxParticipants
									: false
							}
						>
							Join Challenge
						</Button>
					)}
				</div>
			</div>

			{/* Time remaining for active challenges */}
			{challenge.status === ChallengeStatus.ACTIVE && timeRemaining && timeRemaining > 0 && (
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
					<div className="flex items-center justify-between">
						<span className="text-sm text-blue-800">Time Remaining:</span>
						<span className="font-mono text-sm text-blue-900">
							{formatDuration(timeRemaining)}
						</span>
					</div>
				</div>
			)}

			{/* Participant preview */}
			{challenge.participants && challenge.participants.length > 0 && (
				<div className="flex items-center space-x-2 mt-3">
					<span className="text-xs text-gray-500">Participants:</span>
					<div className="flex -space-x-2">
						{challenge.participants.slice(0, 5).map((participant) => (
							<UserAvatar
								key={participant.id}
								userId={participant.userId}
								size="xs"
								className="border-2 border-white"
							/>
						))}
						{challenge.participants.length > 5 && (
							<div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs text-gray-600 border-2 border-white">
								+{challenge.participants.length - 5}
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
```

**Challenge Leaderboard Component** (`src/components/ui/challenge-leaderboard.tsx`)

```typescript
interface ChallengeLeaderboardProps {
	participants: ChallengeParticipant[];
	currentUserId?: string;
	showProgress?: boolean;
}

export function ChallengeLeaderboard({
	participants,
	currentUserId,
	showProgress,
}: ChallengeLeaderboardProps) {
	const sortedParticipants = [...participants].sort((a, b) => b.score - a.score);

	const getRankIcon = (rank: number) => {
		switch (rank) {
			case 1:
				return <Trophy className="w-5 h-5 text-yellow-500" />;
			case 2:
				return <Medal className="w-5 h-5 text-gray-400" />;
			case 3:
				return <Award className="w-5 h-5 text-amber-600" />;
			default:
				return (
					<span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-600">
						#{rank}
					</span>
				);
		}
	};

	return (
		<div className="space-y-2">
			{sortedParticipants.map((participant, index) => {
				const rank = index + 1;
				const isCurrentUser = participant.userId === currentUserId;

				return (
					<div
						key={participant.id}
						className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
							isCurrentUser
								? 'bg-blue-50 border border-blue-200'
								: rank <= 3
								? 'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200'
								: 'bg-white border border-gray-200'
						}`}
					>
						<div className="flex items-center space-x-2">{getRankIcon(rank)}</div>

						<div className="flex-1 min-w-0">
							<div className="flex items-center space-x-2">
								<UserAvatar userId={participant.userId} size="sm" />
								<div>
									<p
										className={`font-medium truncate ${
											isCurrentUser ? 'text-blue-900' : 'text-gray-900'
										}`}
									>
										<UserDisplayName userId={participant.userId} />
										{isCurrentUser && (
											<span className="text-blue-600 ml-1">(You)</span>
										)}
									</p>
									{showProgress && participant.progress && (
										<p className="text-xs text-gray-500">
											{formatProgress(participant.progress)}
										</p>
									)}
								</div>
							</div>
						</div>

						<div className="text-right">
							<p
								className={`font-bold ${
									isCurrentUser ? 'text-blue-900' : 'text-gray-900'
								}`}
							>
								{participant.score.toLocaleString()}
							</p>
							<p className="text-xs text-gray-500">points</p>
						</div>
					</div>
				);
			})}
		</div>
	);
}
```

#### Hooks

**Peer Challenges Hook** (`src/hooks/use-peer-challenges.ts`)

```typescript
export function usePeerChallenges() {
	const [userChallenges, setUserChallenges] = useState<PeerChallenge[]>([]);
	const [publicChallenges, setPublicChallenges] = useState<PeerChallenge[]>([]);
	const [invitations, setInvitations] = useState<ChallengeInvitation[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const fetchUserChallenges = useCallback(async (status?: ChallengeStatus) => {
		setLoading(true);
		try {
			const challenges = await getUserChallengesApi(status);
			setUserChallenges(challenges);
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to fetch user challenges'));
		} finally {
			setLoading(false);
		}
	}, []);

	const fetchPublicChallenges = useCallback(async (filters?: ChallengeFilters) => {
		setLoading(true);
		try {
			const challenges = await getPublicChallengesApi(filters);
			setPublicChallenges(challenges);
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to fetch public challenges'));
		} finally {
			setLoading(false);
		}
	}, []);

	const createChallenge = useCallback(async (challengeData: CreateChallengeDto) => {
		try {
			const newChallenge = await createChallengeApi(challengeData);
			setUserChallenges((prev) => [newChallenge, ...prev]);
			toast.success('Challenge created successfully!');
			return newChallenge;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to create challenge');
			setError(error);
			toast.error(error.message);
			throw error;
		}
	}, []);

	const joinChallenge = useCallback(
		async (challengeId: string) => {
			try {
				await joinPublicChallengeApi(challengeId);
				await fetchUserChallenges();
				toast.success('Successfully joined the challenge!');
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to join challenge');
				setError(error);
				toast.error(error.message);
			}
		},
		[fetchUserChallenges]
	);

	const respondToInvitation = useCallback(
		async (invitationId: string, response: 'accept' | 'decline') => {
			try {
				await respondToInvitationApi(invitationId, response);
				setInvitations((prev) => prev.filter((inv) => inv.id !== invitationId));

				if (response === 'accept') {
					await fetchUserChallenges();
					toast.success('Challenge invitation accepted!');
				} else {
					toast.success('Challenge invitation declined');
				}
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to respond to invitation');
				setError(error);
				toast.error(error.message);
			}
		},
		[fetchUserChallenges]
	);

	return {
		userChallenges,
		publicChallenges,
		invitations,
		loading,
		error,
		fetchUserChallenges,
		fetchPublicChallenges,
		createChallenge,
		joinChallenge,
		respondToInvitation,
	};
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic challenge service and repository
- Challenge creation and invitation system

### Phase 2 (Weeks 3-4): Challenge Logic

- Activity tracking and scoring
- Leaderboard calculation
- Challenge lifecycle management

### Phase 3 (Weeks 5-6): Frontend Components

- Challenge discovery and creation
- Real-time leaderboards
- Invitation management

### Phase 4 (Weeks 7-8): Advanced Features

- Challenge templates
- Real-time updates
- Social features integration

## Success Metrics

- Challenge creation and participation rates
- User engagement and retention
- Competition completion rates
- Social interaction increase
- Learning motivation improvement

## Future Enhancements

- Live challenge streaming
- Team-based challenges
- Seasonal tournaments
- Custom scoring algorithms
- AI-powered challenge recommendations
