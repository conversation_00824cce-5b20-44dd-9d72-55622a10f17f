# Vocab Application

A robust vocabulary learning application designed to help users expand their vocabulary through spaced repetition and organized learning.

## Features

-   **Vocabulary Management**: Create, edit, and manage vocabulary items with meanings, examples, and categories
-   **Spaced Repetition**: Learn more effectively with an intelligent review system
-   **Progress Tracking**: Monitor your learning journey with detailed statistics
-   **Search & Filtering**: Quickly find vocab items by text, tags, or categories
-   **Mobile-Friendly Interface**: Learn anywhere, anytime with responsive design
-   **Multiple Language Support**: Expand your vocabulary in various languages

# Cải Tiến Bảo Mật cho Dự Án Vocab

Dự án này đã triển khai các cải tiến bảo mật quan trọng để tăng cường tính an toàn của ứng dụng Vocab dựa trên các khuyến nghị từ báo cáo đánh giá dự án.

## Các Cải Tiến Đã Triển Khai

### 1. <PERSON><PERSON><PERSON>hực Đầu Vào <PERSON> (Input Validation)

-   **Tập trung validation với Zod**: Tạo các schema validation cho tất cả các đầu vào API
-   **Middleware xác thực**: Kiểm tra tất cả đầu vào trước khi xử lý
-   **Xử lý lỗi nhất quán**: Định dạng thông báo lỗi rõ ràng và chi tiết
-   **Kiểm tra giới hạn**: Giới hạn độ dài và loại dữ liệu đầu vào

### 2. Giới Hạn Tốc Độ (Rate Limiting)

-   **Middleware rate limit**: Giới hạn số lượng request trong một khoảng thời gian
-   **Phân loại endpoint**: Rate limit khác nhau cho các loại endpoint khác nhau (chuẩn, nghiêm ngặt, công khai)
-   **Header metadata**: Thêm các header về tình trạng giới hạn, thời gian thử lại
-   **Lưu trữ bằng Redis**: Sử dụng Redis để lưu trữ thông tin request và đảm bảo giới hạn tốc độ

### 3. Tiêu Đề Bảo Mật (Security Headers)

-   **Content Security Policy (CSP)**: Ngăn chặn XSS và các tấn công injection
-   **X-XSS-Protection**: Kích hoạt bảo vệ XSS của trình duyệt
-   **X-Content-Type-Options**: Ngăn chặn MIME type sniffing
-   **X-Frame-Options**: Bảo vệ chống lại clickjacking
-   **Strict-Transport-Security**: Buộc kết nối HTTPS
-   **Referrer-Policy**: Kiểm soát thông tin referrer
-   **Permissions-Policy**: Giới hạn quyền truy cập API của trình duyệt

### 4. Bảo Vệ CSRF (Cross-Site Request Forgery)

-   **Double Submit Cookie Pattern**: Kiểm tra token từ cả cookie và header
-   **Middleware CSRF**: Xác thực tất cả các request POST, PUT, DELETE và PATCH
-   **Whitelisting**: Miễn trừ cho các endpoint không cần bảo vệ CSRF
-   **Token ngẫu nhiên**: Tạo CSRF token an toàn cho mỗi phiên làm việc
-   **Client-side Integration**: Tích hợp tự động token CSRF vào tất cả API calls

## Kiến Trúc Bảo Mật

Các cải tiến bảo mật được triển khai theo kiến trúc lớp (defense-in-depth):

1. **Middleware**: Áp dụng các middleware bảo mật theo thứ tự hợp lý cho tất cả request
2. **Backend**: Thêm validation ở tầng dịch vụ/handler
3. **Frontend**: Tích hợp CSRF token và xử lý lỗi phía client

## Hướng Dẫn Sử Dụng

### Cho Frontend Developers

Khi thực hiện API calls, sử dụng hàm `fetchWithCsrf` thay vì `fetch` thông thường:

```typescript
import { fetchWithCsrf } from '@/lib/utils';

// Example
const response = await fetchWithCsrf('/api/endpoint', {
	method: 'POST',
	body: JSON.stringify(data),
});
```

### Cho Backend Developers

Khi thêm API endpoint mới, hãy:

1. Tạo schema validation trong `src/lib/validation.ts`
2. Sử dụng hàm `validateRequest` để xác thực dữ liệu đầu vào
3. Xác định mức độ rate limit phù hợp trong middleware

## Khuyến Nghị Tiếp Theo

1. Triển khai logging và hệ thống giám sát bảo mật
2. Thêm kiểm tra định kỳ bằng các công cụ phân tích bảo mật
3. Thêm Captcha cho các endpoint authentication nhạy cảm
4. Triển khai kiểm thử bảo mật tự động
