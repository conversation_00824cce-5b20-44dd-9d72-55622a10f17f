services:
    postgresql:
        image: postgres:latest
        environment:
            - POSTGRES_DB=vocab
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=postgres
        ports:
            - '5432:5432'
        volumes:
            - postgresql_data:/var/lib/postgresql/data
        networks:
            - vocab-network

    redis:
        image: redis:7-alpine
        environment:
            - REDIS_PASSWORD=redis123
        command: redis-server --requirepass redis123 --appendonly yes --appendfsync everysec
        ports:
            - '6380:6379'
        volumes:
            - redis_data:/data
        networks:
            - vocab-network
        restart: unless-stopped
        healthcheck:
            test: ['CMD', 'redis-cli', '-a', 'redis123', 'ping']
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s

networks:
    vocab-network:
        driver: bridge

volumes:
    postgresql_data:
    redis_data:
